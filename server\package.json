{"name": "dru<PERSON>o", "version": "1.0.0", "description": "Banking software", "main": "server.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "start": "node dist/server.js", "build": "tsc", "lint:check": "eslint --ignore-path .eslint<PERSON>ore --ext .js,.ts .", "lint:fix": "eslint . --fix", "prettier:check": "prettier --ignore-path .gitignore --write \"**/*.+(js|ts|json)\"", "prettier:fix": "prettier --write", "lint-prettier": "yarn lint:check && yarn prettier:check", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.16", "@types/streamifier": "^0.1.2", "bcrypt": "^5.1.1", "cloudinary": "^2.5.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "http-status": "^1.7.4", "jsonwebtoken": "^9.0.2", "mongodb": "^6.3.0", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.15", "streamifier": "^0.1.1", "ts-node-dev": "^2.0.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "zod": "^3.22.4"}, "devDependencies": {"typescript": "^5.3.3"}}