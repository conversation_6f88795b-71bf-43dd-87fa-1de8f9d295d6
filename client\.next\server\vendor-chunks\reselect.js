"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/reselect";
exports.ids = ["vendor-chunks/reselect"];
exports.modules = {

/***/ "(ssr)/./node_modules/reselect/dist/reselect.mjs":
/*!*************************************************!*\
  !*** ./node_modules/reselect/dist/reselect.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSelector: () => (/* binding */ createSelector),\n/* harmony export */   createSelectorCreator: () => (/* binding */ createSelectorCreator),\n/* harmony export */   createStructuredSelector: () => (/* binding */ createStructuredSelector),\n/* harmony export */   lruMemoize: () => (/* binding */ lruMemoize),\n/* harmony export */   referenceEqualityCheck: () => (/* binding */ referenceEqualityCheck),\n/* harmony export */   setGlobalDevModeChecks: () => (/* binding */ setGlobalDevModeChecks),\n/* harmony export */   unstable_autotrackMemoize: () => (/* binding */ autotrackMemoize),\n/* harmony export */   weakMapMemoize: () => (/* binding */ weakMapMemoize)\n/* harmony export */ });\n// src/devModeChecks/identityFunctionCheck.ts\nvar runIdentityFunctionCheck = (resultFunc, inputSelectorsResults, outputSelectorResult)=>{\n    if (inputSelectorsResults.length === 1 && inputSelectorsResults[0] === outputSelectorResult) {\n        let isInputSameAsOutput = false;\n        try {\n            const emptyObject = {};\n            if (resultFunc(emptyObject) === emptyObject) isInputSameAsOutput = true;\n        } catch  {}\n        if (isInputSameAsOutput) {\n            let stack = void 0;\n            try {\n                throw new Error();\n            } catch (e) {\n                ;\n                ({ stack } = e);\n            }\n            console.warn(\"The result function returned its own inputs without modification. e.g\\n`createSelector([state => state.todos], todos => todos)`\\nThis could lead to inefficient memoization and unnecessary re-renders.\\nEnsure transformation logic is in the result function, and extraction logic is in the input selectors.\", {\n                stack\n            });\n        }\n    }\n};\n// src/devModeChecks/inputStabilityCheck.ts\nvar runInputStabilityCheck = (inputSelectorResultsObject, options, inputSelectorArgs)=>{\n    const { memoize, memoizeOptions } = options;\n    const { inputSelectorResults, inputSelectorResultsCopy } = inputSelectorResultsObject;\n    const createAnEmptyObject = memoize(()=>({}), ...memoizeOptions);\n    const areInputSelectorResultsEqual = createAnEmptyObject.apply(null, inputSelectorResults) === createAnEmptyObject.apply(null, inputSelectorResultsCopy);\n    if (!areInputSelectorResultsEqual) {\n        let stack = void 0;\n        try {\n            throw new Error();\n        } catch (e) {\n            ;\n            ({ stack } = e);\n        }\n        console.warn(\"An input selector returned a different result when passed same arguments.\\nThis means your output selector will likely run more frequently than intended.\\nAvoid returning a new reference inside your input selector, e.g.\\n`createSelector([state => state.todos.map(todo => todo.id)], todoIds => todoIds.length)`\", {\n            arguments: inputSelectorArgs,\n            firstInputs: inputSelectorResults,\n            secondInputs: inputSelectorResultsCopy,\n            stack\n        });\n    }\n};\n// src/devModeChecks/setGlobalDevModeChecks.ts\nvar globalDevModeChecks = {\n    inputStabilityCheck: \"once\",\n    identityFunctionCheck: \"once\"\n};\nvar setGlobalDevModeChecks = (devModeChecks)=>{\n    Object.assign(globalDevModeChecks, devModeChecks);\n};\n// src/utils.ts\nvar NOT_FOUND = \"NOT_FOUND\";\nfunction assertIsFunction(func, errorMessage = `expected a function, instead received ${typeof func}`) {\n    if (typeof func !== \"function\") {\n        throw new TypeError(errorMessage);\n    }\n}\nfunction assertIsObject(object, errorMessage = `expected an object, instead received ${typeof object}`) {\n    if (typeof object !== \"object\") {\n        throw new TypeError(errorMessage);\n    }\n}\nfunction assertIsArrayOfFunctions(array, errorMessage = `expected all items to be functions, instead received the following types: `) {\n    if (!array.every((item)=>typeof item === \"function\")) {\n        const itemTypes = array.map((item)=>typeof item === \"function\" ? `function ${item.name || \"unnamed\"}()` : typeof item).join(\", \");\n        throw new TypeError(`${errorMessage}[${itemTypes}]`);\n    }\n}\nvar ensureIsArray = (item)=>{\n    return Array.isArray(item) ? item : [\n        item\n    ];\n};\nfunction getDependencies(createSelectorArgs) {\n    const dependencies = Array.isArray(createSelectorArgs[0]) ? createSelectorArgs[0] : createSelectorArgs;\n    assertIsArrayOfFunctions(dependencies, `createSelector expects all input-selectors to be functions, but received the following types: `);\n    return dependencies;\n}\nfunction collectInputSelectorResults(dependencies, inputSelectorArgs) {\n    const inputSelectorResults = [];\n    const { length } = dependencies;\n    for(let i = 0; i < length; i++){\n        inputSelectorResults.push(dependencies[i].apply(null, inputSelectorArgs));\n    }\n    return inputSelectorResults;\n}\nvar getDevModeChecksExecutionInfo = (firstRun, devModeChecks)=>{\n    const { identityFunctionCheck, inputStabilityCheck } = {\n        ...globalDevModeChecks,\n        ...devModeChecks\n    };\n    return {\n        identityFunctionCheck: {\n            shouldRun: identityFunctionCheck === \"always\" || identityFunctionCheck === \"once\" && firstRun,\n            run: runIdentityFunctionCheck\n        },\n        inputStabilityCheck: {\n            shouldRun: inputStabilityCheck === \"always\" || inputStabilityCheck === \"once\" && firstRun,\n            run: runInputStabilityCheck\n        }\n    };\n};\n// src/autotrackMemoize/autotracking.ts\nvar $REVISION = 0;\nvar CURRENT_TRACKER = null;\nvar Cell = class {\n    constructor(initialValue, isEqual = tripleEq){\n        this.revision = $REVISION;\n        this._isEqual = tripleEq;\n        this._value = this._lastValue = initialValue;\n        this._isEqual = isEqual;\n    }\n    // Whenever a storage value is read, it'll add itself to the current tracker if\n    // one exists, entangling its state with that cache.\n    get value() {\n        CURRENT_TRACKER?.add(this);\n        return this._value;\n    }\n    // Whenever a storage value is updated, we bump the global revision clock,\n    // assign the revision for this storage to the new value, _and_ we schedule a\n    // rerender. This is important, and it's what makes autotracking  _pull_\n    // based. We don't actively tell the caches which depend on the storage that\n    // anything has happened. Instead, we recompute the caches when needed.\n    set value(newValue) {\n        if (this.value === newValue) return;\n        this._value = newValue;\n        this.revision = ++$REVISION;\n    }\n};\nfunction tripleEq(a, b) {\n    return a === b;\n}\nvar TrackingCache = class {\n    constructor(fn){\n        this._cachedRevision = -1;\n        this._deps = [];\n        this.hits = 0;\n        this.fn = fn;\n    }\n    clear() {\n        this._cachedValue = void 0;\n        this._cachedRevision = -1;\n        this._deps = [];\n        this.hits = 0;\n    }\n    get value() {\n        if (this.revision > this._cachedRevision) {\n            const { fn } = this;\n            const currentTracker = /* @__PURE__ */ new Set();\n            const prevTracker = CURRENT_TRACKER;\n            CURRENT_TRACKER = currentTracker;\n            this._cachedValue = fn();\n            CURRENT_TRACKER = prevTracker;\n            this.hits++;\n            this._deps = Array.from(currentTracker);\n            this._cachedRevision = this.revision;\n        }\n        CURRENT_TRACKER?.add(this);\n        return this._cachedValue;\n    }\n    get revision() {\n        return Math.max(...this._deps.map((d)=>d.revision), 0);\n    }\n};\nfunction getValue(cell) {\n    if (!(cell instanceof Cell)) {\n        console.warn(\"Not a valid cell! \", cell);\n    }\n    return cell.value;\n}\nfunction setValue(storage, value) {\n    if (!(storage instanceof Cell)) {\n        throw new TypeError(\"setValue must be passed a tracked store created with `createStorage`.\");\n    }\n    storage.value = storage._lastValue = value;\n}\nfunction createCell(initialValue, isEqual = tripleEq) {\n    return new Cell(initialValue, isEqual);\n}\nfunction createCache(fn) {\n    assertIsFunction(fn, \"the first parameter to `createCache` must be a function\");\n    return new TrackingCache(fn);\n}\n// src/autotrackMemoize/tracking.ts\nvar neverEq = (a, b)=>false;\nfunction createTag() {\n    return createCell(null, neverEq);\n}\nfunction dirtyTag(tag, value) {\n    setValue(tag, value);\n}\nvar consumeCollection = (node)=>{\n    let tag = node.collectionTag;\n    if (tag === null) {\n        tag = node.collectionTag = createTag();\n    }\n    getValue(tag);\n};\nvar dirtyCollection = (node)=>{\n    const tag = node.collectionTag;\n    if (tag !== null) {\n        dirtyTag(tag, null);\n    }\n};\n// src/autotrackMemoize/proxy.ts\nvar REDUX_PROXY_LABEL = Symbol();\nvar nextId = 0;\nvar proto = Object.getPrototypeOf({});\nvar ObjectTreeNode = class {\n    constructor(value){\n        this.proxy = new Proxy(this, objectProxyHandler);\n        this.tag = createTag();\n        this.tags = {};\n        this.children = {};\n        this.collectionTag = null;\n        this.id = nextId++;\n        this.value = value;\n        this.value = value;\n        this.tag.value = value;\n    }\n};\nvar objectProxyHandler = {\n    get (node, key) {\n        function calculateResult() {\n            const { value } = node;\n            const childValue = Reflect.get(value, key);\n            if (typeof key === \"symbol\") {\n                return childValue;\n            }\n            if (key in proto) {\n                return childValue;\n            }\n            if (typeof childValue === \"object\" && childValue !== null) {\n                let childNode = node.children[key];\n                if (childNode === void 0) {\n                    childNode = node.children[key] = createNode(childValue);\n                }\n                if (childNode.tag) {\n                    getValue(childNode.tag);\n                }\n                return childNode.proxy;\n            } else {\n                let tag = node.tags[key];\n                if (tag === void 0) {\n                    tag = node.tags[key] = createTag();\n                    tag.value = childValue;\n                }\n                getValue(tag);\n                return childValue;\n            }\n        }\n        const res = calculateResult();\n        return res;\n    },\n    ownKeys (node) {\n        consumeCollection(node);\n        return Reflect.ownKeys(node.value);\n    },\n    getOwnPropertyDescriptor (node, prop) {\n        return Reflect.getOwnPropertyDescriptor(node.value, prop);\n    },\n    has (node, prop) {\n        return Reflect.has(node.value, prop);\n    }\n};\nvar ArrayTreeNode = class {\n    constructor(value){\n        this.proxy = new Proxy([\n            this\n        ], arrayProxyHandler);\n        this.tag = createTag();\n        this.tags = {};\n        this.children = {};\n        this.collectionTag = null;\n        this.id = nextId++;\n        this.value = value;\n        this.value = value;\n        this.tag.value = value;\n    }\n};\nvar arrayProxyHandler = {\n    get ([node], key) {\n        if (key === \"length\") {\n            consumeCollection(node);\n        }\n        return objectProxyHandler.get(node, key);\n    },\n    ownKeys ([node]) {\n        return objectProxyHandler.ownKeys(node);\n    },\n    getOwnPropertyDescriptor ([node], prop) {\n        return objectProxyHandler.getOwnPropertyDescriptor(node, prop);\n    },\n    has ([node], prop) {\n        return objectProxyHandler.has(node, prop);\n    }\n};\nfunction createNode(value) {\n    if (Array.isArray(value)) {\n        return new ArrayTreeNode(value);\n    }\n    return new ObjectTreeNode(value);\n}\nfunction updateNode(node, newValue) {\n    const { value, tags, children } = node;\n    node.value = newValue;\n    if (Array.isArray(value) && Array.isArray(newValue) && value.length !== newValue.length) {\n        dirtyCollection(node);\n    } else {\n        if (value !== newValue) {\n            let oldKeysSize = 0;\n            let newKeysSize = 0;\n            let anyKeysAdded = false;\n            for(const _key in value){\n                oldKeysSize++;\n            }\n            for(const key in newValue){\n                newKeysSize++;\n                if (!(key in value)) {\n                    anyKeysAdded = true;\n                    break;\n                }\n            }\n            const isDifferent = anyKeysAdded || oldKeysSize !== newKeysSize;\n            if (isDifferent) {\n                dirtyCollection(node);\n            }\n        }\n    }\n    for(const key in tags){\n        const childValue = value[key];\n        const newChildValue = newValue[key];\n        if (childValue !== newChildValue) {\n            dirtyCollection(node);\n            dirtyTag(tags[key], newChildValue);\n        }\n        if (typeof newChildValue === \"object\" && newChildValue !== null) {\n            delete tags[key];\n        }\n    }\n    for(const key in children){\n        const childNode = children[key];\n        const newChildValue = newValue[key];\n        const childValue = childNode.value;\n        if (childValue === newChildValue) {\n            continue;\n        } else if (typeof newChildValue === \"object\" && newChildValue !== null) {\n            updateNode(childNode, newChildValue);\n        } else {\n            deleteNode(childNode);\n            delete children[key];\n        }\n    }\n}\nfunction deleteNode(node) {\n    if (node.tag) {\n        dirtyTag(node.tag, null);\n    }\n    dirtyCollection(node);\n    for(const key in node.tags){\n        dirtyTag(node.tags[key], null);\n    }\n    for(const key in node.children){\n        deleteNode(node.children[key]);\n    }\n}\n// src/lruMemoize.ts\nfunction createSingletonCache(equals) {\n    let entry;\n    return {\n        get (key) {\n            if (entry && equals(entry.key, key)) {\n                return entry.value;\n            }\n            return NOT_FOUND;\n        },\n        put (key, value) {\n            entry = {\n                key,\n                value\n            };\n        },\n        getEntries () {\n            return entry ? [\n                entry\n            ] : [];\n        },\n        clear () {\n            entry = void 0;\n        }\n    };\n}\nfunction createLruCache(maxSize, equals) {\n    let entries = [];\n    function get(key) {\n        const cacheIndex = entries.findIndex((entry)=>equals(key, entry.key));\n        if (cacheIndex > -1) {\n            const entry = entries[cacheIndex];\n            if (cacheIndex > 0) {\n                entries.splice(cacheIndex, 1);\n                entries.unshift(entry);\n            }\n            return entry.value;\n        }\n        return NOT_FOUND;\n    }\n    function put(key, value) {\n        if (get(key) === NOT_FOUND) {\n            entries.unshift({\n                key,\n                value\n            });\n            if (entries.length > maxSize) {\n                entries.pop();\n            }\n        }\n    }\n    function getEntries() {\n        return entries;\n    }\n    function clear() {\n        entries = [];\n    }\n    return {\n        get,\n        put,\n        getEntries,\n        clear\n    };\n}\nvar referenceEqualityCheck = (a, b)=>a === b;\nfunction createCacheKeyComparator(equalityCheck) {\n    return function areArgumentsShallowlyEqual(prev, next) {\n        if (prev === null || next === null || prev.length !== next.length) {\n            return false;\n        }\n        const { length } = prev;\n        for(let i = 0; i < length; i++){\n            if (!equalityCheck(prev[i], next[i])) {\n                return false;\n            }\n        }\n        return true;\n    };\n}\nfunction lruMemoize(func, equalityCheckOrOptions) {\n    const providedOptions = typeof equalityCheckOrOptions === \"object\" ? equalityCheckOrOptions : {\n        equalityCheck: equalityCheckOrOptions\n    };\n    const { equalityCheck = referenceEqualityCheck, maxSize = 1, resultEqualityCheck } = providedOptions;\n    const comparator = createCacheKeyComparator(equalityCheck);\n    let resultsCount = 0;\n    const cache = maxSize === 1 ? createSingletonCache(comparator) : createLruCache(maxSize, comparator);\n    function memoized() {\n        let value = cache.get(arguments);\n        if (value === NOT_FOUND) {\n            value = func.apply(null, arguments);\n            resultsCount++;\n            if (resultEqualityCheck) {\n                const entries = cache.getEntries();\n                const matchingEntry = entries.find((entry)=>resultEqualityCheck(entry.value, value));\n                if (matchingEntry) {\n                    value = matchingEntry.value;\n                    resultsCount !== 0 && resultsCount--;\n                }\n            }\n            cache.put(arguments, value);\n        }\n        return value;\n    }\n    memoized.clearCache = ()=>{\n        cache.clear();\n        memoized.resetResultsCount();\n    };\n    memoized.resultsCount = ()=>resultsCount;\n    memoized.resetResultsCount = ()=>{\n        resultsCount = 0;\n    };\n    return memoized;\n}\n// src/autotrackMemoize/autotrackMemoize.ts\nfunction autotrackMemoize(func) {\n    const node = createNode([]);\n    let lastArgs = null;\n    const shallowEqual = createCacheKeyComparator(referenceEqualityCheck);\n    const cache = createCache(()=>{\n        const res = func.apply(null, node.proxy);\n        return res;\n    });\n    function memoized() {\n        if (!shallowEqual(lastArgs, arguments)) {\n            updateNode(node, arguments);\n            lastArgs = arguments;\n        }\n        return cache.value;\n    }\n    memoized.clearCache = ()=>{\n        return cache.clear();\n    };\n    return memoized;\n}\n// src/weakMapMemoize.ts\nvar StrongRef = class {\n    constructor(value){\n        this.value = value;\n    }\n    deref() {\n        return this.value;\n    }\n};\nvar Ref = typeof WeakRef !== \"undefined\" ? WeakRef : StrongRef;\nvar UNTERMINATED = 0;\nvar TERMINATED = 1;\nfunction createCacheNode() {\n    return {\n        s: UNTERMINATED,\n        v: void 0,\n        o: null,\n        p: null\n    };\n}\nfunction weakMapMemoize(func, options = {}) {\n    let fnNode = createCacheNode();\n    const { resultEqualityCheck } = options;\n    let lastResult;\n    let resultsCount = 0;\n    function memoized() {\n        let cacheNode = fnNode;\n        const { length } = arguments;\n        for(let i = 0, l = length; i < l; i++){\n            const arg = arguments[i];\n            if (typeof arg === \"function\" || typeof arg === \"object\" && arg !== null) {\n                let objectCache = cacheNode.o;\n                if (objectCache === null) {\n                    cacheNode.o = objectCache = /* @__PURE__ */ new WeakMap();\n                }\n                const objectNode = objectCache.get(arg);\n                if (objectNode === void 0) {\n                    cacheNode = createCacheNode();\n                    objectCache.set(arg, cacheNode);\n                } else {\n                    cacheNode = objectNode;\n                }\n            } else {\n                let primitiveCache = cacheNode.p;\n                if (primitiveCache === null) {\n                    cacheNode.p = primitiveCache = /* @__PURE__ */ new Map();\n                }\n                const primitiveNode = primitiveCache.get(arg);\n                if (primitiveNode === void 0) {\n                    cacheNode = createCacheNode();\n                    primitiveCache.set(arg, cacheNode);\n                } else {\n                    cacheNode = primitiveNode;\n                }\n            }\n        }\n        const terminatedNode = cacheNode;\n        let result;\n        if (cacheNode.s === TERMINATED) {\n            result = cacheNode.v;\n        } else {\n            result = func.apply(null, arguments);\n            resultsCount++;\n        }\n        terminatedNode.s = TERMINATED;\n        if (resultEqualityCheck) {\n            const lastResultValue = lastResult?.deref?.() ?? lastResult;\n            if (lastResultValue != null && resultEqualityCheck(lastResultValue, result)) {\n                result = lastResultValue;\n                resultsCount !== 0 && resultsCount--;\n            }\n            const needsWeakRef = typeof result === \"object\" && result !== null || typeof result === \"function\";\n            lastResult = needsWeakRef ? new Ref(result) : result;\n        }\n        terminatedNode.v = result;\n        return result;\n    }\n    memoized.clearCache = ()=>{\n        fnNode = createCacheNode();\n        memoized.resetResultsCount();\n    };\n    memoized.resultsCount = ()=>resultsCount;\n    memoized.resetResultsCount = ()=>{\n        resultsCount = 0;\n    };\n    return memoized;\n}\n// src/createSelectorCreator.ts\nfunction createSelectorCreator(memoizeOrOptions, ...memoizeOptionsFromArgs) {\n    const createSelectorCreatorOptions = typeof memoizeOrOptions === \"function\" ? {\n        memoize: memoizeOrOptions,\n        memoizeOptions: memoizeOptionsFromArgs\n    } : memoizeOrOptions;\n    const createSelector2 = (...createSelectorArgs)=>{\n        let recomputations = 0;\n        let dependencyRecomputations = 0;\n        let lastResult;\n        let directlyPassedOptions = {};\n        let resultFunc = createSelectorArgs.pop();\n        if (typeof resultFunc === \"object\") {\n            directlyPassedOptions = resultFunc;\n            resultFunc = createSelectorArgs.pop();\n        }\n        assertIsFunction(resultFunc, `createSelector expects an output function after the inputs, but received: [${typeof resultFunc}]`);\n        const combinedOptions = {\n            ...createSelectorCreatorOptions,\n            ...directlyPassedOptions\n        };\n        const { memoize, memoizeOptions = [], argsMemoize = weakMapMemoize, argsMemoizeOptions = [], devModeChecks = {} } = combinedOptions;\n        const finalMemoizeOptions = ensureIsArray(memoizeOptions);\n        const finalArgsMemoizeOptions = ensureIsArray(argsMemoizeOptions);\n        const dependencies = getDependencies(createSelectorArgs);\n        const memoizedResultFunc = memoize(function recomputationWrapper() {\n            recomputations++;\n            return resultFunc.apply(null, arguments);\n        }, ...finalMemoizeOptions);\n        let firstRun = true;\n        const selector = argsMemoize(function dependenciesChecker() {\n            dependencyRecomputations++;\n            const inputSelectorResults = collectInputSelectorResults(dependencies, arguments);\n            lastResult = memoizedResultFunc.apply(null, inputSelectorResults);\n            if (true) {\n                const { identityFunctionCheck, inputStabilityCheck } = getDevModeChecksExecutionInfo(firstRun, devModeChecks);\n                if (identityFunctionCheck.shouldRun) {\n                    identityFunctionCheck.run(resultFunc, inputSelectorResults, lastResult);\n                }\n                if (inputStabilityCheck.shouldRun) {\n                    const inputSelectorResultsCopy = collectInputSelectorResults(dependencies, arguments);\n                    inputStabilityCheck.run({\n                        inputSelectorResults,\n                        inputSelectorResultsCopy\n                    }, {\n                        memoize,\n                        memoizeOptions: finalMemoizeOptions\n                    }, arguments);\n                }\n                if (firstRun) firstRun = false;\n            }\n            return lastResult;\n        }, ...finalArgsMemoizeOptions);\n        return Object.assign(selector, {\n            resultFunc,\n            memoizedResultFunc,\n            dependencies,\n            dependencyRecomputations: ()=>dependencyRecomputations,\n            resetDependencyRecomputations: ()=>{\n                dependencyRecomputations = 0;\n            },\n            lastResult: ()=>lastResult,\n            recomputations: ()=>recomputations,\n            resetRecomputations: ()=>{\n                recomputations = 0;\n            },\n            memoize,\n            argsMemoize\n        });\n    };\n    Object.assign(createSelector2, {\n        withTypes: ()=>createSelector2\n    });\n    return createSelector2;\n}\nvar createSelector = /* @__PURE__ */ createSelectorCreator(weakMapMemoize);\n// src/createStructuredSelector.ts\nvar createStructuredSelector = Object.assign((inputSelectorsObject, selectorCreator = createSelector)=>{\n    assertIsObject(inputSelectorsObject, `createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof inputSelectorsObject}`);\n    const inputSelectorKeys = Object.keys(inputSelectorsObject);\n    const dependencies = inputSelectorKeys.map((key)=>inputSelectorsObject[key]);\n    const structuredSelector = selectorCreator(dependencies, (...inputSelectorResults)=>{\n        return inputSelectorResults.reduce((composition, value, index)=>{\n            composition[inputSelectorKeys[index]] = value;\n            return composition;\n        }, {});\n    });\n    return structuredSelector;\n}, {\n    withTypes: ()=>createStructuredSelector\n});\n //# sourceMappingURL=reselect.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/reselect/dist/reselect.mjs\n");

/***/ })

};
;