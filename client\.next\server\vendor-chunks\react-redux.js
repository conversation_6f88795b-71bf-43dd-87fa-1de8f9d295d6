"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-redux";
exports.ids = ["vendor-chunks/react-redux"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-redux/dist/react-redux.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/react-redux/dist/react-redux.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Provider: () => (/* binding */ Provider_default),\n/* harmony export */   ReactReduxContext: () => (/* binding */ ReactReduxContext),\n/* harmony export */   batch: () => (/* binding */ batch),\n/* harmony export */   connect: () => (/* binding */ connect_default),\n/* harmony export */   createDispatchHook: () => (/* binding */ createDispatchHook),\n/* harmony export */   createSelectorHook: () => (/* binding */ createSelectorHook),\n/* harmony export */   createStoreHook: () => (/* binding */ createStoreHook),\n/* harmony export */   shallowEqual: () => (/* binding */ shallowEqual),\n/* harmony export */   useDispatch: () => (/* binding */ useDispatch),\n/* harmony export */   useSelector: () => (/* binding */ useSelector),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_with_selector_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/with-selector.js */ \"(ssr)/./node_modules/use-sync-external-store/with-selector.js\");\n// src/index.ts\n\n\n// src/utils/react.ts\n\nvar React = // prettier-ignore\n// @ts-ignore\n true ? react__WEBPACK_IMPORTED_MODULE_0__ : /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)));\n// src/components/Context.ts\nvar ContextKey = Symbol.for(`react-redux-context`);\nvar gT = typeof globalThis !== \"undefined\" ? globalThis : /* fall back to a per-module scope (pre-8.1 behaviour) if `globalThis` is not available */ {};\nfunction getContext() {\n    if (!React.createContext) return {};\n    const contextMap = gT[ContextKey] ?? (gT[ContextKey] = /* @__PURE__ */ new Map());\n    let realContext = contextMap.get(React.createContext);\n    if (!realContext) {\n        realContext = React.createContext(null);\n        if (true) {\n            realContext.displayName = \"ReactRedux\";\n        }\n        contextMap.set(React.createContext, realContext);\n    }\n    return realContext;\n}\nvar ReactReduxContext = /* @__PURE__ */ getContext();\n// src/utils/useSyncExternalStore.ts\nvar notInitialized = ()=>{\n    throw new Error(\"uSES not initialized!\");\n};\n// src/hooks/useReduxContext.ts\nfunction createReduxContextHook(context = ReactReduxContext) {\n    return function useReduxContext2() {\n        const contextValue = React.useContext(context);\n        if ( true && !contextValue) {\n            throw new Error(\"could not find react-redux context value; please ensure the component is wrapped in a <Provider>\");\n        }\n        return contextValue;\n    };\n}\nvar useReduxContext = /* @__PURE__ */ createReduxContextHook();\n// src/hooks/useSelector.ts\nvar useSyncExternalStoreWithSelector = notInitialized;\nvar initializeUseSelector = (fn)=>{\n    useSyncExternalStoreWithSelector = fn;\n};\nvar refEquality = (a, b)=>a === b;\nfunction createSelectorHook(context = ReactReduxContext) {\n    const useReduxContext2 = context === ReactReduxContext ? useReduxContext : createReduxContextHook(context);\n    const useSelector2 = (selector, equalityFnOrOptions = {})=>{\n        const { equalityFn = refEquality, devModeChecks = {} } = typeof equalityFnOrOptions === \"function\" ? {\n            equalityFn: equalityFnOrOptions\n        } : equalityFnOrOptions;\n        if (true) {\n            if (!selector) {\n                throw new Error(`You must pass a selector to useSelector`);\n            }\n            if (typeof selector !== \"function\") {\n                throw new Error(`You must pass a function as a selector to useSelector`);\n            }\n            if (typeof equalityFn !== \"function\") {\n                throw new Error(`You must pass a function as an equality function to useSelector`);\n            }\n        }\n        const { store, subscription, getServerState, stabilityCheck, identityFunctionCheck } = useReduxContext2();\n        const firstRun = React.useRef(true);\n        const wrappedSelector = React.useCallback({\n            [selector.name] (state) {\n                const selected = selector(state);\n                if (true) {\n                    const { identityFunctionCheck: finalIdentityFunctionCheck, stabilityCheck: finalStabilityCheck } = {\n                        stabilityCheck,\n                        identityFunctionCheck,\n                        ...devModeChecks\n                    };\n                    if (finalStabilityCheck === \"always\" || finalStabilityCheck === \"once\" && firstRun.current) {\n                        const toCompare = selector(state);\n                        if (!equalityFn(selected, toCompare)) {\n                            let stack = void 0;\n                            try {\n                                throw new Error();\n                            } catch (e) {\n                                ;\n                                ({ stack } = e);\n                            }\n                            console.warn(\"Selector \" + (selector.name || \"unknown\") + \" returned a different result when called with the same parameters. This can lead to unnecessary rerenders.\\nSelectors that return a new reference (such as an object or an array) should be memoized: https://redux.js.org/usage/deriving-data-selectors#optimizing-selectors-with-memoization\", {\n                                state,\n                                selected,\n                                selected2: toCompare,\n                                stack\n                            });\n                        }\n                    }\n                    if (finalIdentityFunctionCheck === \"always\" || finalIdentityFunctionCheck === \"once\" && firstRun.current) {\n                        if (selected === state) {\n                            let stack = void 0;\n                            try {\n                                throw new Error();\n                            } catch (e) {\n                                ;\n                                ({ stack } = e);\n                            }\n                            console.warn(\"Selector \" + (selector.name || \"unknown\") + \" returned the root state when called. This can lead to unnecessary rerenders.\\nSelectors that return the entire state are almost certainly a mistake, as they will cause a rerender whenever *anything* in state changes.\", {\n                                stack\n                            });\n                        }\n                    }\n                    if (firstRun.current) firstRun.current = false;\n                }\n                return selected;\n            }\n        }[selector.name], [\n            selector,\n            stabilityCheck,\n            devModeChecks.stabilityCheck\n        ]);\n        const selectedState = useSyncExternalStoreWithSelector(subscription.addNestedSub, store.getState, getServerState || store.getState, wrappedSelector, equalityFn);\n        React.useDebugValue(selectedState);\n        return selectedState;\n    };\n    Object.assign(useSelector2, {\n        withTypes: ()=>useSelector2\n    });\n    return useSelector2;\n}\nvar useSelector = /* @__PURE__ */ createSelectorHook();\n// src/utils/react-is.ts\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.element\");\nvar REACT_PORTAL_TYPE = Symbol.for(\"react.portal\");\nvar REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\nvar REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\");\nvar REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\nvar REACT_PROVIDER_TYPE = Symbol.for(\"react.provider\");\nvar REACT_CONTEXT_TYPE = Symbol.for(\"react.context\");\nvar REACT_SERVER_CONTEXT_TYPE = Symbol.for(\"react.server_context\");\nvar REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\");\nvar REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\");\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\");\nvar REACT_MEMO_TYPE = Symbol.for(\"react.memo\");\nvar REACT_LAZY_TYPE = Symbol.for(\"react.lazy\");\nvar REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\");\nvar REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\");\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nfunction isValidElementType(type) {\n    if (typeof type === \"string\" || typeof type === \"function\") {\n        return true;\n    }\n    if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || type === REACT_OFFSCREEN_TYPE) {\n        return true;\n    }\n    if (typeof type === \"object\" && type !== null) {\n        if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n        // types supported by any Flight configuration anywhere since\n        // we don't know which Flight build this will end up being used\n        // with.\n        type.$$typeof === REACT_CLIENT_REFERENCE || type.getModuleId !== void 0) {\n            return true;\n        }\n    }\n    return false;\n}\nfunction typeOf(object) {\n    if (typeof object === \"object\" && object !== null) {\n        const $$typeof = object.$$typeof;\n        switch($$typeof){\n            case REACT_ELEMENT_TYPE:\n                {\n                    const type = object.type;\n                    switch(type){\n                        case REACT_FRAGMENT_TYPE:\n                        case REACT_PROFILER_TYPE:\n                        case REACT_STRICT_MODE_TYPE:\n                        case REACT_SUSPENSE_TYPE:\n                        case REACT_SUSPENSE_LIST_TYPE:\n                            return type;\n                        default:\n                            {\n                                const $$typeofType = type && type.$$typeof;\n                                switch($$typeofType){\n                                    case REACT_SERVER_CONTEXT_TYPE:\n                                    case REACT_CONTEXT_TYPE:\n                                    case REACT_FORWARD_REF_TYPE:\n                                    case REACT_LAZY_TYPE:\n                                    case REACT_MEMO_TYPE:\n                                    case REACT_PROVIDER_TYPE:\n                                        return $$typeofType;\n                                    default:\n                                        return $$typeof;\n                                }\n                            }\n                    }\n                }\n            case REACT_PORTAL_TYPE:\n                {\n                    return $$typeof;\n                }\n        }\n    }\n    return void 0;\n}\nfunction isContextConsumer(object) {\n    return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isMemo(object) {\n    return typeOf(object) === REACT_MEMO_TYPE;\n}\n// src/utils/warning.ts\nfunction warning(message) {\n    if (typeof console !== \"undefined\" && typeof console.error === \"function\") {\n        console.error(message);\n    }\n    try {\n        throw new Error(message);\n    } catch (e) {}\n}\n// src/connect/verifySubselectors.ts\nfunction verify(selector, methodName) {\n    if (!selector) {\n        throw new Error(`Unexpected value for ${methodName} in connect.`);\n    } else if (methodName === \"mapStateToProps\" || methodName === \"mapDispatchToProps\") {\n        if (!Object.prototype.hasOwnProperty.call(selector, \"dependsOnOwnProps\")) {\n            warning(`The selector for ${methodName} of connect did not specify a value for dependsOnOwnProps.`);\n        }\n    }\n}\nfunction verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps) {\n    verify(mapStateToProps, \"mapStateToProps\");\n    verify(mapDispatchToProps, \"mapDispatchToProps\");\n    verify(mergeProps, \"mergeProps\");\n}\n// src/connect/selectorFactory.ts\nfunction pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, { areStatesEqual, areOwnPropsEqual, areStatePropsEqual }) {\n    let hasRunAtLeastOnce = false;\n    let state;\n    let ownProps;\n    let stateProps;\n    let dispatchProps;\n    let mergedProps;\n    function handleFirstCall(firstState, firstOwnProps) {\n        state = firstState;\n        ownProps = firstOwnProps;\n        stateProps = mapStateToProps(state, ownProps);\n        dispatchProps = mapDispatchToProps(dispatch, ownProps);\n        mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n        hasRunAtLeastOnce = true;\n        return mergedProps;\n    }\n    function handleNewPropsAndNewState() {\n        stateProps = mapStateToProps(state, ownProps);\n        if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n        mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n        return mergedProps;\n    }\n    function handleNewProps() {\n        if (mapStateToProps.dependsOnOwnProps) stateProps = mapStateToProps(state, ownProps);\n        if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n        mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n        return mergedProps;\n    }\n    function handleNewState() {\n        const nextStateProps = mapStateToProps(state, ownProps);\n        const statePropsChanged = !areStatePropsEqual(nextStateProps, stateProps);\n        stateProps = nextStateProps;\n        if (statePropsChanged) mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n        return mergedProps;\n    }\n    function handleSubsequentCalls(nextState, nextOwnProps) {\n        const propsChanged = !areOwnPropsEqual(nextOwnProps, ownProps);\n        const stateChanged = !areStatesEqual(nextState, state, nextOwnProps, ownProps);\n        state = nextState;\n        ownProps = nextOwnProps;\n        if (propsChanged && stateChanged) return handleNewPropsAndNewState();\n        if (propsChanged) return handleNewProps();\n        if (stateChanged) return handleNewState();\n        return mergedProps;\n    }\n    return function pureFinalPropsSelector(nextState, nextOwnProps) {\n        return hasRunAtLeastOnce ? handleSubsequentCalls(nextState, nextOwnProps) : handleFirstCall(nextState, nextOwnProps);\n    };\n}\nfunction finalPropsSelectorFactory(dispatch, { initMapStateToProps, initMapDispatchToProps, initMergeProps, ...options }) {\n    const mapStateToProps = initMapStateToProps(dispatch, options);\n    const mapDispatchToProps = initMapDispatchToProps(dispatch, options);\n    const mergeProps = initMergeProps(dispatch, options);\n    if (true) {\n        verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps);\n    }\n    return pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, options);\n}\n// src/utils/bindActionCreators.ts\nfunction bindActionCreators(actionCreators, dispatch) {\n    const boundActionCreators = {};\n    for(const key in actionCreators){\n        const actionCreator = actionCreators[key];\n        if (typeof actionCreator === \"function\") {\n            boundActionCreators[key] = (...args)=>dispatch(actionCreator(...args));\n        }\n    }\n    return boundActionCreators;\n}\n// src/utils/isPlainObject.ts\nfunction isPlainObject(obj) {\n    if (typeof obj !== \"object\" || obj === null) return false;\n    const proto = Object.getPrototypeOf(obj);\n    if (proto === null) return true;\n    let baseProto = proto;\n    while(Object.getPrototypeOf(baseProto) !== null){\n        baseProto = Object.getPrototypeOf(baseProto);\n    }\n    return proto === baseProto;\n}\n// src/utils/verifyPlainObject.ts\nfunction verifyPlainObject(value, displayName, methodName) {\n    if (!isPlainObject(value)) {\n        warning(`${methodName}() in ${displayName} must return a plain object. Instead received ${value}.`);\n    }\n}\n// src/connect/wrapMapToProps.ts\nfunction wrapMapToPropsConstant(getConstant) {\n    return function initConstantSelector(dispatch) {\n        const constant = getConstant(dispatch);\n        function constantSelector() {\n            return constant;\n        }\n        constantSelector.dependsOnOwnProps = false;\n        return constantSelector;\n    };\n}\nfunction getDependsOnOwnProps(mapToProps) {\n    return mapToProps.dependsOnOwnProps ? Boolean(mapToProps.dependsOnOwnProps) : mapToProps.length !== 1;\n}\nfunction wrapMapToPropsFunc(mapToProps, methodName) {\n    return function initProxySelector(dispatch, { displayName }) {\n        const proxy = function mapToPropsProxy(stateOrDispatch, ownProps) {\n            return proxy.dependsOnOwnProps ? proxy.mapToProps(stateOrDispatch, ownProps) : proxy.mapToProps(stateOrDispatch, void 0);\n        };\n        proxy.dependsOnOwnProps = true;\n        proxy.mapToProps = function detectFactoryAndVerify(stateOrDispatch, ownProps) {\n            proxy.mapToProps = mapToProps;\n            proxy.dependsOnOwnProps = getDependsOnOwnProps(mapToProps);\n            let props = proxy(stateOrDispatch, ownProps);\n            if (typeof props === \"function\") {\n                proxy.mapToProps = props;\n                proxy.dependsOnOwnProps = getDependsOnOwnProps(props);\n                props = proxy(stateOrDispatch, ownProps);\n            }\n            if (true) verifyPlainObject(props, displayName, methodName);\n            return props;\n        };\n        return proxy;\n    };\n}\n// src/connect/invalidArgFactory.ts\nfunction createInvalidArgFactory(arg, name) {\n    return (dispatch, options)=>{\n        throw new Error(`Invalid value of type ${typeof arg} for ${name} argument when connecting component ${options.wrappedComponentName}.`);\n    };\n}\n// src/connect/mapDispatchToProps.ts\nfunction mapDispatchToPropsFactory(mapDispatchToProps) {\n    return mapDispatchToProps && typeof mapDispatchToProps === \"object\" ? wrapMapToPropsConstant((dispatch)=>// @ts-ignore\n        bindActionCreators(mapDispatchToProps, dispatch)) : !mapDispatchToProps ? wrapMapToPropsConstant((dispatch)=>({\n            dispatch\n        })) : typeof mapDispatchToProps === \"function\" ? // @ts-ignore\n    wrapMapToPropsFunc(mapDispatchToProps, \"mapDispatchToProps\") : createInvalidArgFactory(mapDispatchToProps, \"mapDispatchToProps\");\n}\n// src/connect/mapStateToProps.ts\nfunction mapStateToPropsFactory(mapStateToProps) {\n    return !mapStateToProps ? wrapMapToPropsConstant(()=>({})) : typeof mapStateToProps === \"function\" ? // @ts-ignore\n    wrapMapToPropsFunc(mapStateToProps, \"mapStateToProps\") : createInvalidArgFactory(mapStateToProps, \"mapStateToProps\");\n}\n// src/connect/mergeProps.ts\nfunction defaultMergeProps(stateProps, dispatchProps, ownProps) {\n    return {\n        ...ownProps,\n        ...stateProps,\n        ...dispatchProps\n    };\n}\nfunction wrapMergePropsFunc(mergeProps) {\n    return function initMergePropsProxy(dispatch, { displayName, areMergedPropsEqual }) {\n        let hasRunOnce = false;\n        let mergedProps;\n        return function mergePropsProxy(stateProps, dispatchProps, ownProps) {\n            const nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n            if (hasRunOnce) {\n                if (!areMergedPropsEqual(nextMergedProps, mergedProps)) mergedProps = nextMergedProps;\n            } else {\n                hasRunOnce = true;\n                mergedProps = nextMergedProps;\n                if (true) verifyPlainObject(mergedProps, displayName, \"mergeProps\");\n            }\n            return mergedProps;\n        };\n    };\n}\nfunction mergePropsFactory(mergeProps) {\n    return !mergeProps ? ()=>defaultMergeProps : typeof mergeProps === \"function\" ? wrapMergePropsFunc(mergeProps) : createInvalidArgFactory(mergeProps, \"mergeProps\");\n}\n// src/utils/batch.ts\nfunction defaultNoopBatch(callback) {\n    callback();\n}\n// src/utils/Subscription.ts\nfunction createListenerCollection() {\n    let first = null;\n    let last = null;\n    return {\n        clear () {\n            first = null;\n            last = null;\n        },\n        notify () {\n            defaultNoopBatch(()=>{\n                let listener = first;\n                while(listener){\n                    listener.callback();\n                    listener = listener.next;\n                }\n            });\n        },\n        get () {\n            const listeners = [];\n            let listener = first;\n            while(listener){\n                listeners.push(listener);\n                listener = listener.next;\n            }\n            return listeners;\n        },\n        subscribe (callback) {\n            let isSubscribed = true;\n            const listener = last = {\n                callback,\n                next: null,\n                prev: last\n            };\n            if (listener.prev) {\n                listener.prev.next = listener;\n            } else {\n                first = listener;\n            }\n            return function unsubscribe() {\n                if (!isSubscribed || first === null) return;\n                isSubscribed = false;\n                if (listener.next) {\n                    listener.next.prev = listener.prev;\n                } else {\n                    last = listener.prev;\n                }\n                if (listener.prev) {\n                    listener.prev.next = listener.next;\n                } else {\n                    first = listener.next;\n                }\n            };\n        }\n    };\n}\nvar nullListeners = {\n    notify () {},\n    get: ()=>[]\n};\nfunction createSubscription(store, parentSub) {\n    let unsubscribe;\n    let listeners = nullListeners;\n    let subscriptionsAmount = 0;\n    let selfSubscribed = false;\n    function addNestedSub(listener) {\n        trySubscribe();\n        const cleanupListener = listeners.subscribe(listener);\n        let removed = false;\n        return ()=>{\n            if (!removed) {\n                removed = true;\n                cleanupListener();\n                tryUnsubscribe();\n            }\n        };\n    }\n    function notifyNestedSubs() {\n        listeners.notify();\n    }\n    function handleChangeWrapper() {\n        if (subscription.onStateChange) {\n            subscription.onStateChange();\n        }\n    }\n    function isSubscribed() {\n        return selfSubscribed;\n    }\n    function trySubscribe() {\n        subscriptionsAmount++;\n        if (!unsubscribe) {\n            unsubscribe = parentSub ? parentSub.addNestedSub(handleChangeWrapper) : store.subscribe(handleChangeWrapper);\n            listeners = createListenerCollection();\n        }\n    }\n    function tryUnsubscribe() {\n        subscriptionsAmount--;\n        if (unsubscribe && subscriptionsAmount === 0) {\n            unsubscribe();\n            unsubscribe = void 0;\n            listeners.clear();\n            listeners = nullListeners;\n        }\n    }\n    function trySubscribeSelf() {\n        if (!selfSubscribed) {\n            selfSubscribed = true;\n            trySubscribe();\n        }\n    }\n    function tryUnsubscribeSelf() {\n        if (selfSubscribed) {\n            selfSubscribed = false;\n            tryUnsubscribe();\n        }\n    }\n    const subscription = {\n        addNestedSub,\n        notifyNestedSubs,\n        handleChangeWrapper,\n        isSubscribed,\n        trySubscribe: trySubscribeSelf,\n        tryUnsubscribe: tryUnsubscribeSelf,\n        getListeners: ()=>listeners\n    };\n    return subscription;\n}\n// src/utils/useIsomorphicLayoutEffect.ts\nvar canUseDOM = !!( false && 0);\nvar useIsomorphicLayoutEffect = canUseDOM ? React.useLayoutEffect : React.useEffect;\n// src/utils/shallowEqual.ts\nfunction is(x, y) {\n    if (x === y) {\n        return x !== 0 || y !== 0 || 1 / x === 1 / y;\n    } else {\n        return x !== x && y !== y;\n    }\n}\nfunction shallowEqual(objA, objB) {\n    if (is(objA, objB)) return true;\n    if (typeof objA !== \"object\" || objA === null || typeof objB !== \"object\" || objB === null) {\n        return false;\n    }\n    const keysA = Object.keys(objA);\n    const keysB = Object.keys(objB);\n    if (keysA.length !== keysB.length) return false;\n    for(let i = 0; i < keysA.length; i++){\n        if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {\n            return false;\n        }\n    }\n    return true;\n}\n// src/utils/hoistStatics.ts\nvar REACT_STATICS = {\n    childContextTypes: true,\n    contextType: true,\n    contextTypes: true,\n    defaultProps: true,\n    displayName: true,\n    getDefaultProps: true,\n    getDerivedStateFromError: true,\n    getDerivedStateFromProps: true,\n    mixins: true,\n    propTypes: true,\n    type: true\n};\nvar KNOWN_STATICS = {\n    name: true,\n    length: true,\n    prototype: true,\n    caller: true,\n    callee: true,\n    arguments: true,\n    arity: true\n};\nvar FORWARD_REF_STATICS = {\n    $$typeof: true,\n    render: true,\n    defaultProps: true,\n    displayName: true,\n    propTypes: true\n};\nvar MEMO_STATICS = {\n    $$typeof: true,\n    compare: true,\n    defaultProps: true,\n    displayName: true,\n    propTypes: true,\n    type: true\n};\nvar TYPE_STATICS = {\n    [ForwardRef]: FORWARD_REF_STATICS,\n    [Memo]: MEMO_STATICS\n};\nfunction getStatics(component) {\n    if (isMemo(component)) {\n        return MEMO_STATICS;\n    }\n    return TYPE_STATICS[component[\"$$typeof\"]] || REACT_STATICS;\n}\nvar defineProperty = Object.defineProperty;\nvar getOwnPropertyNames = Object.getOwnPropertyNames;\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar getPrototypeOf = Object.getPrototypeOf;\nvar objectPrototype = Object.prototype;\nfunction hoistNonReactStatics(targetComponent, sourceComponent) {\n    if (typeof sourceComponent !== \"string\") {\n        if (objectPrototype) {\n            const inheritedComponent = getPrototypeOf(sourceComponent);\n            if (inheritedComponent && inheritedComponent !== objectPrototype) {\n                hoistNonReactStatics(targetComponent, inheritedComponent);\n            }\n        }\n        let keys = getOwnPropertyNames(sourceComponent);\n        if (getOwnPropertySymbols) {\n            keys = keys.concat(getOwnPropertySymbols(sourceComponent));\n        }\n        const targetStatics = getStatics(targetComponent);\n        const sourceStatics = getStatics(sourceComponent);\n        for(let i = 0; i < keys.length; ++i){\n            const key = keys[i];\n            if (!KNOWN_STATICS[key] && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {\n                const descriptor = getOwnPropertyDescriptor(sourceComponent, key);\n                try {\n                    defineProperty(targetComponent, key, descriptor);\n                } catch (e) {}\n            }\n        }\n    }\n    return targetComponent;\n}\n// src/components/connect.tsx\nvar useSyncExternalStore = notInitialized;\nvar initializeConnect = (fn)=>{\n    useSyncExternalStore = fn;\n};\nvar NO_SUBSCRIPTION_ARRAY = [\n    null,\n    null\n];\nvar stringifyComponent = (Comp)=>{\n    try {\n        return JSON.stringify(Comp);\n    } catch (err) {\n        return String(Comp);\n    }\n};\nfunction useIsomorphicLayoutEffectWithArgs(effectFunc, effectArgs, dependencies) {\n    useIsomorphicLayoutEffect(()=>effectFunc(...effectArgs), dependencies);\n}\nfunction captureWrapperProps(lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, childPropsFromStoreUpdate, notifyNestedSubs) {\n    lastWrapperProps.current = wrapperProps;\n    renderIsScheduled.current = false;\n    if (childPropsFromStoreUpdate.current) {\n        childPropsFromStoreUpdate.current = null;\n        notifyNestedSubs();\n    }\n}\nfunction subscribeUpdates(shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, isMounted, childPropsFromStoreUpdate, notifyNestedSubs, additionalSubscribeListener) {\n    if (!shouldHandleStateChanges) return ()=>{};\n    let didUnsubscribe = false;\n    let lastThrownError = null;\n    const checkForUpdates = ()=>{\n        if (didUnsubscribe || !isMounted.current) {\n            return;\n        }\n        const latestStoreState = store.getState();\n        let newChildProps, error;\n        try {\n            newChildProps = childPropsSelector(latestStoreState, lastWrapperProps.current);\n        } catch (e) {\n            error = e;\n            lastThrownError = e;\n        }\n        if (!error) {\n            lastThrownError = null;\n        }\n        if (newChildProps === lastChildProps.current) {\n            if (!renderIsScheduled.current) {\n                notifyNestedSubs();\n            }\n        } else {\n            lastChildProps.current = newChildProps;\n            childPropsFromStoreUpdate.current = newChildProps;\n            renderIsScheduled.current = true;\n            additionalSubscribeListener();\n        }\n    };\n    subscription.onStateChange = checkForUpdates;\n    subscription.trySubscribe();\n    checkForUpdates();\n    const unsubscribeWrapper = ()=>{\n        didUnsubscribe = true;\n        subscription.tryUnsubscribe();\n        subscription.onStateChange = null;\n        if (lastThrownError) {\n            throw lastThrownError;\n        }\n    };\n    return unsubscribeWrapper;\n}\nfunction strictEqual(a, b) {\n    return a === b;\n}\nvar hasWarnedAboutDeprecatedPureOption = false;\nfunction connect(mapStateToProps, mapDispatchToProps, mergeProps, { // The `pure` option has been removed, so TS doesn't like us destructuring this to check its existence.\n// @ts-ignore\npure, areStatesEqual = strictEqual, areOwnPropsEqual = shallowEqual, areStatePropsEqual = shallowEqual, areMergedPropsEqual = shallowEqual, // use React's forwardRef to expose a ref of the wrapped component\nforwardRef = false, // the context consumer to use\ncontext = ReactReduxContext } = {}) {\n    if (true) {\n        if (pure !== void 0 && !hasWarnedAboutDeprecatedPureOption) {\n            hasWarnedAboutDeprecatedPureOption = true;\n            warning('The `pure` option has been removed. `connect` is now always a \"pure/memoized\" component');\n        }\n    }\n    const Context = context;\n    const initMapStateToProps = mapStateToPropsFactory(mapStateToProps);\n    const initMapDispatchToProps = mapDispatchToPropsFactory(mapDispatchToProps);\n    const initMergeProps = mergePropsFactory(mergeProps);\n    const shouldHandleStateChanges = Boolean(mapStateToProps);\n    const wrapWithConnect = (WrappedComponent)=>{\n        if (true) {\n            const isValid = /* @__PURE__ */ isValidElementType(WrappedComponent);\n            if (!isValid) throw new Error(`You must pass a component to the function returned by connect. Instead received ${stringifyComponent(WrappedComponent)}`);\n        }\n        const wrappedComponentName = WrappedComponent.displayName || WrappedComponent.name || \"Component\";\n        const displayName = `Connect(${wrappedComponentName})`;\n        const selectorFactoryOptions = {\n            shouldHandleStateChanges,\n            displayName,\n            wrappedComponentName,\n            WrappedComponent,\n            // @ts-ignore\n            initMapStateToProps,\n            // @ts-ignore\n            initMapDispatchToProps,\n            initMergeProps,\n            areStatesEqual,\n            areStatePropsEqual,\n            areOwnPropsEqual,\n            areMergedPropsEqual\n        };\n        function ConnectFunction(props) {\n            const [propsContext, reactReduxForwardedRef, wrapperProps] = React.useMemo(()=>{\n                const { reactReduxForwardedRef: reactReduxForwardedRef2, ...wrapperProps2 } = props;\n                return [\n                    props.context,\n                    reactReduxForwardedRef2,\n                    wrapperProps2\n                ];\n            }, [\n                props\n            ]);\n            const ContextToUse = React.useMemo(()=>{\n                let ResultContext = Context;\n                if (propsContext?.Consumer) {\n                    if (true) {\n                        const isValid = /* @__PURE__ */ isContextConsumer(// @ts-ignore\n                        /* @__PURE__ */ React.createElement(propsContext.Consumer, null));\n                        if (!isValid) {\n                            throw new Error(\"You must pass a valid React context consumer as `props.context`\");\n                        }\n                        ResultContext = propsContext;\n                    }\n                }\n                return ResultContext;\n            }, [\n                propsContext,\n                Context\n            ]);\n            const contextValue = React.useContext(ContextToUse);\n            const didStoreComeFromProps = Boolean(props.store) && Boolean(props.store.getState) && Boolean(props.store.dispatch);\n            const didStoreComeFromContext = Boolean(contextValue) && Boolean(contextValue.store);\n            if ( true && !didStoreComeFromProps && !didStoreComeFromContext) {\n                throw new Error(`Could not find \"store\" in the context of \"${displayName}\". Either wrap the root component in a <Provider>, or pass a custom React context provider to <Provider> and the corresponding React context consumer to ${displayName} in connect options.`);\n            }\n            const store = didStoreComeFromProps ? props.store : contextValue.store;\n            const getServerState = didStoreComeFromContext ? contextValue.getServerState : store.getState;\n            const childPropsSelector = React.useMemo(()=>{\n                return finalPropsSelectorFactory(store.dispatch, selectorFactoryOptions);\n            }, [\n                store\n            ]);\n            const [subscription, notifyNestedSubs] = React.useMemo(()=>{\n                if (!shouldHandleStateChanges) return NO_SUBSCRIPTION_ARRAY;\n                const subscription2 = createSubscription(store, didStoreComeFromProps ? void 0 : contextValue.subscription);\n                const notifyNestedSubs2 = subscription2.notifyNestedSubs.bind(subscription2);\n                return [\n                    subscription2,\n                    notifyNestedSubs2\n                ];\n            }, [\n                store,\n                didStoreComeFromProps,\n                contextValue\n            ]);\n            const overriddenContextValue = React.useMemo(()=>{\n                if (didStoreComeFromProps) {\n                    return contextValue;\n                }\n                return {\n                    ...contextValue,\n                    subscription\n                };\n            }, [\n                didStoreComeFromProps,\n                contextValue,\n                subscription\n            ]);\n            const lastChildProps = React.useRef();\n            const lastWrapperProps = React.useRef(wrapperProps);\n            const childPropsFromStoreUpdate = React.useRef();\n            const renderIsScheduled = React.useRef(false);\n            const isProcessingDispatch = React.useRef(false);\n            const isMounted = React.useRef(false);\n            const latestSubscriptionCallbackError = React.useRef();\n            useIsomorphicLayoutEffect(()=>{\n                isMounted.current = true;\n                return ()=>{\n                    isMounted.current = false;\n                };\n            }, []);\n            const actualChildPropsSelector = React.useMemo(()=>{\n                const selector = ()=>{\n                    if (childPropsFromStoreUpdate.current && wrapperProps === lastWrapperProps.current) {\n                        return childPropsFromStoreUpdate.current;\n                    }\n                    return childPropsSelector(store.getState(), wrapperProps);\n                };\n                return selector;\n            }, [\n                store,\n                wrapperProps\n            ]);\n            const subscribeForReact = React.useMemo(()=>{\n                const subscribe = (reactListener)=>{\n                    if (!subscription) {\n                        return ()=>{};\n                    }\n                    return subscribeUpdates(shouldHandleStateChanges, store, subscription, // @ts-ignore\n                    childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, isMounted, childPropsFromStoreUpdate, notifyNestedSubs, reactListener);\n                };\n                return subscribe;\n            }, [\n                subscription\n            ]);\n            useIsomorphicLayoutEffectWithArgs(captureWrapperProps, [\n                lastWrapperProps,\n                lastChildProps,\n                renderIsScheduled,\n                wrapperProps,\n                childPropsFromStoreUpdate,\n                notifyNestedSubs\n            ]);\n            let actualChildProps;\n            try {\n                actualChildProps = useSyncExternalStore(// TODO We're passing through a big wrapper that does a bunch of extra side effects besides subscribing\n                subscribeForReact, // TODO This is incredibly hacky. We've already processed the store update and calculated new child props,\n                // TODO and we're just passing that through so it triggers a re-render for us rather than relying on `uSES`.\n                actualChildPropsSelector, getServerState ? ()=>childPropsSelector(getServerState(), wrapperProps) : actualChildPropsSelector);\n            } catch (err) {\n                if (latestSubscriptionCallbackError.current) {\n                    ;\n                    err.message += `\nThe error may be correlated with this previous error:\n${latestSubscriptionCallbackError.current.stack}\n\n`;\n                }\n                throw err;\n            }\n            useIsomorphicLayoutEffect(()=>{\n                latestSubscriptionCallbackError.current = void 0;\n                childPropsFromStoreUpdate.current = void 0;\n                lastChildProps.current = actualChildProps;\n            });\n            const renderedWrappedComponent = React.useMemo(()=>{\n                return(// @ts-ignore\n                /* @__PURE__ */ React.createElement(WrappedComponent, {\n                    ...actualChildProps,\n                    ref: reactReduxForwardedRef\n                }));\n            }, [\n                reactReduxForwardedRef,\n                WrappedComponent,\n                actualChildProps\n            ]);\n            const renderedChild = React.useMemo(()=>{\n                if (shouldHandleStateChanges) {\n                    return /* @__PURE__ */ React.createElement(ContextToUse.Provider, {\n                        value: overriddenContextValue\n                    }, renderedWrappedComponent);\n                }\n                return renderedWrappedComponent;\n            }, [\n                ContextToUse,\n                renderedWrappedComponent,\n                overriddenContextValue\n            ]);\n            return renderedChild;\n        }\n        const _Connect = React.memo(ConnectFunction);\n        const Connect = _Connect;\n        Connect.WrappedComponent = WrappedComponent;\n        Connect.displayName = ConnectFunction.displayName = displayName;\n        if (forwardRef) {\n            const _forwarded = React.forwardRef(function forwardConnectRef(props, ref) {\n                return /* @__PURE__ */ React.createElement(Connect, {\n                    ...props,\n                    reactReduxForwardedRef: ref\n                });\n            });\n            const forwarded = _forwarded;\n            forwarded.displayName = displayName;\n            forwarded.WrappedComponent = WrappedComponent;\n            return /* @__PURE__ */ hoistNonReactStatics(forwarded, WrappedComponent);\n        }\n        return /* @__PURE__ */ hoistNonReactStatics(Connect, WrappedComponent);\n    };\n    return wrapWithConnect;\n}\nvar connect_default = connect;\n// src/components/Provider.tsx\nfunction Provider({ store, context, children, serverState, stabilityCheck = \"once\", identityFunctionCheck = \"once\" }) {\n    const contextValue = React.useMemo(()=>{\n        const subscription = createSubscription(store);\n        return {\n            store,\n            subscription,\n            getServerState: serverState ? ()=>serverState : void 0,\n            stabilityCheck,\n            identityFunctionCheck\n        };\n    }, [\n        store,\n        serverState,\n        stabilityCheck,\n        identityFunctionCheck\n    ]);\n    const previousState = React.useMemo(()=>store.getState(), [\n        store\n    ]);\n    useIsomorphicLayoutEffect(()=>{\n        const { subscription } = contextValue;\n        subscription.onStateChange = subscription.notifyNestedSubs;\n        subscription.trySubscribe();\n        if (previousState !== store.getState()) {\n            subscription.notifyNestedSubs();\n        }\n        return ()=>{\n            subscription.tryUnsubscribe();\n            subscription.onStateChange = void 0;\n        };\n    }, [\n        contextValue,\n        previousState\n    ]);\n    const Context = context || ReactReduxContext;\n    return /* @__PURE__ */ React.createElement(Context.Provider, {\n        value: contextValue\n    }, children);\n}\nvar Provider_default = Provider;\n// src/hooks/useStore.ts\nfunction createStoreHook(context = ReactReduxContext) {\n    const useReduxContext2 = context === ReactReduxContext ? useReduxContext : // @ts-ignore\n    createReduxContextHook(context);\n    const useStore2 = ()=>{\n        const { store } = useReduxContext2();\n        return store;\n    };\n    Object.assign(useStore2, {\n        withTypes: ()=>useStore2\n    });\n    return useStore2;\n}\nvar useStore = /* @__PURE__ */ createStoreHook();\n// src/hooks/useDispatch.ts\nfunction createDispatchHook(context = ReactReduxContext) {\n    const useStore2 = context === ReactReduxContext ? useStore : createStoreHook(context);\n    const useDispatch2 = ()=>{\n        const store = useStore2();\n        return store.dispatch;\n    };\n    Object.assign(useDispatch2, {\n        withTypes: ()=>useDispatch2\n    });\n    return useDispatch2;\n}\nvar useDispatch = /* @__PURE__ */ createDispatchHook();\n// src/exports.ts\nvar batch = defaultNoopBatch;\n// src/index.ts\ninitializeUseSelector(use_sync_external_store_with_selector_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStoreWithSelector);\ninitializeConnect(react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore);\n //# sourceMappingURL=react-redux.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-redux/dist/react-redux.mjs\n");

/***/ })

};
;