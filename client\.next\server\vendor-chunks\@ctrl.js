"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ctrl";
exports.ids = ["vendor-chunks/@ctrl"];
exports.modules = {

/***/ "(ssr)/./node_modules/@ctrl/tinycolor/dist/module/conversion.js":
/*!****************************************************************!*\
  !*** ./node_modules/@ctrl/tinycolor/dist/module/conversion.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertDecimalToHex: () => (/* binding */ convertDecimalToHex),\n/* harmony export */   convertHexToDecimal: () => (/* binding */ convertHexToDecimal),\n/* harmony export */   hslToRgb: () => (/* binding */ hslToRgb),\n/* harmony export */   hsvToRgb: () => (/* binding */ hsvToRgb),\n/* harmony export */   numberInputToObject: () => (/* binding */ numberInputToObject),\n/* harmony export */   parseIntFromHex: () => (/* binding */ parseIntFromHex),\n/* harmony export */   rgbToHex: () => (/* binding */ rgbToHex),\n/* harmony export */   rgbToHsl: () => (/* binding */ rgbToHsl),\n/* harmony export */   rgbToHsv: () => (/* binding */ rgbToHsv),\n/* harmony export */   rgbToRgb: () => (/* binding */ rgbToRgb),\n/* harmony export */   rgbaToArgbHex: () => (/* binding */ rgbaToArgbHex),\n/* harmony export */   rgbaToHex: () => (/* binding */ rgbaToHex)\n/* harmony export */ });\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/util.js\");\n\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n/**\n * Handle bounds / percentage checking to conform to CSS color spec\n * <http://www.w3.org/TR/css3-color/>\n * *Assumes:* r, g, b in [0, 255] or [0, 1]\n * *Returns:* { r, g, b } in [0, 255]\n */ function rgbToRgb(r, g, b) {\n    return {\n        r: (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(r, 255) * 255,\n        g: (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(g, 255) * 255,\n        b: (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(b, 255) * 255\n    };\n}\n/**\n * Converts an RGB color value to HSL.\n * *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n * *Returns:* { h, s, l } in [0,1]\n */ function rgbToHsl(r, g, b) {\n    r = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(r, 255);\n    g = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(g, 255);\n    b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var s = 0;\n    var l = (max + min) / 2;\n    if (max === min) {\n        s = 0;\n        h = 0; // achromatic\n    } else {\n        var d = max - min;\n        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n        switch(max){\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return {\n        h: h,\n        s: s,\n        l: l\n    };\n}\nfunction hue2rgb(p, q, t) {\n    if (t < 0) {\n        t += 1;\n    }\n    if (t > 1) {\n        t -= 1;\n    }\n    if (t < 1 / 6) {\n        return p + (q - p) * (6 * t);\n    }\n    if (t < 1 / 2) {\n        return q;\n    }\n    if (t < 2 / 3) {\n        return p + (q - p) * (2 / 3 - t) * 6;\n    }\n    return p;\n}\n/**\n * Converts an HSL color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */ function hslToRgb(h, s, l) {\n    var r;\n    var g;\n    var b;\n    h = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(h, 360);\n    s = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(s, 100);\n    l = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(l, 100);\n    if (s === 0) {\n        // achromatic\n        g = l;\n        b = l;\n        r = l;\n    } else {\n        var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n        var p = 2 * l - q;\n        r = hue2rgb(p, q, h + 1 / 3);\n        g = hue2rgb(p, q, h);\n        b = hue2rgb(p, q, h - 1 / 3);\n    }\n    return {\n        r: r * 255,\n        g: g * 255,\n        b: b * 255\n    };\n}\n/**\n * Converts an RGB color value to HSV\n *\n * *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n * *Returns:* { h, s, v } in [0,1]\n */ function rgbToHsv(r, g, b) {\n    r = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(r, 255);\n    g = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(g, 255);\n    b = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(b, 255);\n    var max = Math.max(r, g, b);\n    var min = Math.min(r, g, b);\n    var h = 0;\n    var v = max;\n    var d = max - min;\n    var s = max === 0 ? 0 : d / max;\n    if (max === min) {\n        h = 0; // achromatic\n    } else {\n        switch(max){\n            case r:\n                h = (g - b) / d + (g < b ? 6 : 0);\n                break;\n            case g:\n                h = (b - r) / d + 2;\n                break;\n            case b:\n                h = (r - g) / d + 4;\n                break;\n            default:\n                break;\n        }\n        h /= 6;\n    }\n    return {\n        h: h,\n        s: s,\n        v: v\n    };\n}\n/**\n * Converts an HSV color value to RGB.\n *\n * *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n * *Returns:* { r, g, b } in the set [0, 255]\n */ function hsvToRgb(h, s, v) {\n    h = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(h, 360) * 6;\n    s = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(s, 100);\n    v = (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.bound01)(v, 100);\n    var i = Math.floor(h);\n    var f = h - i;\n    var p = v * (1 - s);\n    var q = v * (1 - f * s);\n    var t = v * (1 - (1 - f) * s);\n    var mod = i % 6;\n    var r = [\n        v,\n        q,\n        p,\n        p,\n        t,\n        v\n    ][mod];\n    var g = [\n        t,\n        v,\n        v,\n        q,\n        p,\n        p\n    ][mod];\n    var b = [\n        p,\n        p,\n        t,\n        v,\n        v,\n        q\n    ][mod];\n    return {\n        r: r * 255,\n        g: g * 255,\n        b: b * 255\n    };\n}\n/**\n * Converts an RGB color to hex\n *\n * Assumes r, g, and b are contained in the set [0, 255]\n * Returns a 3 or 6 character hex\n */ function rgbToHex(r, g, b, allow3Char) {\n    var hex = [\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(r).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(g).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(b).toString(16))\n    ];\n    // Return a 3 character hex if possible\n    if (allow3Char && hex[0].startsWith(hex[0].charAt(1)) && hex[1].startsWith(hex[1].charAt(1)) && hex[2].startsWith(hex[2].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n    }\n    return hex.join(\"\");\n}\n/**\n * Converts an RGBA color plus alpha transparency to hex\n *\n * Assumes r, g, b are contained in the set [0, 255] and\n * a in [0, 1]. Returns a 4 or 8 character rgba hex\n */ // eslint-disable-next-line max-params\nfunction rgbaToHex(r, g, b, a, allow4Char) {\n    var hex = [\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(r).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(g).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(b).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(convertDecimalToHex(a))\n    ];\n    // Return a 4 character hex if possible\n    if (allow4Char && hex[0].startsWith(hex[0].charAt(1)) && hex[1].startsWith(hex[1].charAt(1)) && hex[2].startsWith(hex[2].charAt(1)) && hex[3].startsWith(hex[3].charAt(1))) {\n        return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n    }\n    return hex.join(\"\");\n}\n/**\n * Converts an RGBA color to an ARGB Hex8 string\n * Rarely used, but required for \"toFilter()\"\n */ function rgbaToArgbHex(r, g, b, a) {\n    var hex = [\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(convertDecimalToHex(a)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(r).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(g).toString(16)),\n        (0,_util_js__WEBPACK_IMPORTED_MODULE_0__.pad2)(Math.round(b).toString(16))\n    ];\n    return hex.join(\"\");\n}\n/** Converts a decimal to a hex value */ function convertDecimalToHex(d) {\n    return Math.round(parseFloat(d) * 255).toString(16);\n}\n/** Converts a hex value to a decimal */ function convertHexToDecimal(h) {\n    return parseIntFromHex(h) / 255;\n}\n/** Parse a base-16 hex value into a base-10 integer */ function parseIntFromHex(val) {\n    return parseInt(val, 16);\n}\nfunction numberInputToObject(color) {\n    return {\n        r: color >> 16,\n        g: (color & 0xff00) >> 8,\n        b: color & 0xff\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQGN0cmwvdGlueWNvbG9yL2Rpc3QvbW9kdWxlL2NvbnZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUEwQztBQUMxQyxnRUFBZ0U7QUFDaEUsNEdBQTRHO0FBQzVHOzs7OztDQUtDLEdBQ00sU0FBU0UsU0FBU0MsQ0FBQyxFQUFFQyxDQUFDLEVBQUVDLENBQUM7SUFDNUIsT0FBTztRQUNIRixHQUFHSCxpREFBT0EsQ0FBQ0csR0FBRyxPQUFPO1FBQ3JCQyxHQUFHSixpREFBT0EsQ0FBQ0ksR0FBRyxPQUFPO1FBQ3JCQyxHQUFHTCxpREFBT0EsQ0FBQ0ssR0FBRyxPQUFPO0lBQ3pCO0FBQ0o7QUFDQTs7OztDQUlDLEdBQ00sU0FBU0MsU0FBU0gsQ0FBQyxFQUFFQyxDQUFDLEVBQUVDLENBQUM7SUFDNUJGLElBQUlILGlEQUFPQSxDQUFDRyxHQUFHO0lBQ2ZDLElBQUlKLGlEQUFPQSxDQUFDSSxHQUFHO0lBQ2ZDLElBQUlMLGlEQUFPQSxDQUFDSyxHQUFHO0lBQ2YsSUFBSUUsTUFBTUMsS0FBS0QsR0FBRyxDQUFDSixHQUFHQyxHQUFHQztJQUN6QixJQUFJSSxNQUFNRCxLQUFLQyxHQUFHLENBQUNOLEdBQUdDLEdBQUdDO0lBQ3pCLElBQUlLLElBQUk7SUFDUixJQUFJQyxJQUFJO0lBQ1IsSUFBSUMsSUFBSSxDQUFDTCxNQUFNRSxHQUFFLElBQUs7SUFDdEIsSUFBSUYsUUFBUUUsS0FBSztRQUNiRSxJQUFJO1FBQ0pELElBQUksR0FBRyxhQUFhO0lBQ3hCLE9BQ0s7UUFDRCxJQUFJRyxJQUFJTixNQUFNRTtRQUNkRSxJQUFJQyxJQUFJLE1BQU1DLElBQUssS0FBSU4sTUFBTUUsR0FBRSxJQUFLSSxJQUFLTixDQUFBQSxNQUFNRSxHQUFFO1FBQ2pELE9BQVFGO1lBQ0osS0FBS0o7Z0JBQ0RPLElBQUksQ0FBQ04sSUFBSUMsQ0FBQUEsSUFBS1EsSUFBS1QsQ0FBQUEsSUFBSUMsSUFBSSxJQUFJO2dCQUMvQjtZQUNKLEtBQUtEO2dCQUNETSxJQUFJLENBQUNMLElBQUlGLENBQUFBLElBQUtVLElBQUk7Z0JBQ2xCO1lBQ0osS0FBS1I7Z0JBQ0RLLElBQUksQ0FBQ1AsSUFBSUMsQ0FBQUEsSUFBS1MsSUFBSTtnQkFDbEI7WUFDSjtnQkFDSTtRQUNSO1FBQ0FILEtBQUs7SUFDVDtJQUNBLE9BQU87UUFBRUEsR0FBR0E7UUFBR0MsR0FBR0E7UUFBR0MsR0FBR0E7SUFBRTtBQUM5QjtBQUNBLFNBQVNFLFFBQVFDLENBQUMsRUFBRUMsQ0FBQyxFQUFFQyxDQUFDO0lBQ3BCLElBQUlBLElBQUksR0FBRztRQUNQQSxLQUFLO0lBQ1Q7SUFDQSxJQUFJQSxJQUFJLEdBQUc7UUFDUEEsS0FBSztJQUNUO0lBQ0EsSUFBSUEsSUFBSSxJQUFJLEdBQUc7UUFDWCxPQUFPRixJQUFJLENBQUNDLElBQUlELENBQUFBLElBQU0sS0FBSUUsQ0FBQUE7SUFDOUI7SUFDQSxJQUFJQSxJQUFJLElBQUksR0FBRztRQUNYLE9BQU9EO0lBQ1g7SUFDQSxJQUFJQyxJQUFJLElBQUksR0FBRztRQUNYLE9BQU9GLElBQUksQ0FBQ0MsSUFBSUQsQ0FBQUEsSUFBTSxLQUFJLElBQUlFLENBQUFBLElBQUs7SUFDdkM7SUFDQSxPQUFPRjtBQUNYO0FBQ0E7Ozs7O0NBS0MsR0FDTSxTQUFTRyxTQUFTUixDQUFDLEVBQUVDLENBQUMsRUFBRUMsQ0FBQztJQUM1QixJQUFJVDtJQUNKLElBQUlDO0lBQ0osSUFBSUM7SUFDSkssSUFBSVYsaURBQU9BLENBQUNVLEdBQUc7SUFDZkMsSUFBSVgsaURBQU9BLENBQUNXLEdBQUc7SUFDZkMsSUFBSVosaURBQU9BLENBQUNZLEdBQUc7SUFDZixJQUFJRCxNQUFNLEdBQUc7UUFDVCxhQUFhO1FBQ2JQLElBQUlRO1FBQ0pQLElBQUlPO1FBQ0pULElBQUlTO0lBQ1IsT0FDSztRQUNELElBQUlJLElBQUlKLElBQUksTUFBTUEsSUFBSyxLQUFJRCxDQUFBQSxJQUFLQyxJQUFJRCxJQUFJQyxJQUFJRDtRQUM1QyxJQUFJSSxJQUFJLElBQUlILElBQUlJO1FBQ2hCYixJQUFJVyxRQUFRQyxHQUFHQyxHQUFHTixJQUFJLElBQUk7UUFDMUJOLElBQUlVLFFBQVFDLEdBQUdDLEdBQUdOO1FBQ2xCTCxJQUFJUyxRQUFRQyxHQUFHQyxHQUFHTixJQUFJLElBQUk7SUFDOUI7SUFDQSxPQUFPO1FBQUVQLEdBQUdBLElBQUk7UUFBS0MsR0FBR0EsSUFBSTtRQUFLQyxHQUFHQSxJQUFJO0lBQUk7QUFDaEQ7QUFDQTs7Ozs7Q0FLQyxHQUNNLFNBQVNjLFNBQVNoQixDQUFDLEVBQUVDLENBQUMsRUFBRUMsQ0FBQztJQUM1QkYsSUFBSUgsaURBQU9BLENBQUNHLEdBQUc7SUFDZkMsSUFBSUosaURBQU9BLENBQUNJLEdBQUc7SUFDZkMsSUFBSUwsaURBQU9BLENBQUNLLEdBQUc7SUFDZixJQUFJRSxNQUFNQyxLQUFLRCxHQUFHLENBQUNKLEdBQUdDLEdBQUdDO0lBQ3pCLElBQUlJLE1BQU1ELEtBQUtDLEdBQUcsQ0FBQ04sR0FBR0MsR0FBR0M7SUFDekIsSUFBSUssSUFBSTtJQUNSLElBQUlVLElBQUliO0lBQ1IsSUFBSU0sSUFBSU4sTUFBTUU7SUFDZCxJQUFJRSxJQUFJSixRQUFRLElBQUksSUFBSU0sSUFBSU47SUFDNUIsSUFBSUEsUUFBUUUsS0FBSztRQUNiQyxJQUFJLEdBQUcsYUFBYTtJQUN4QixPQUNLO1FBQ0QsT0FBUUg7WUFDSixLQUFLSjtnQkFDRE8sSUFBSSxDQUFDTixJQUFJQyxDQUFBQSxJQUFLUSxJQUFLVCxDQUFBQSxJQUFJQyxJQUFJLElBQUk7Z0JBQy9CO1lBQ0osS0FBS0Q7Z0JBQ0RNLElBQUksQ0FBQ0wsSUFBSUYsQ0FBQUEsSUFBS1UsSUFBSTtnQkFDbEI7WUFDSixLQUFLUjtnQkFDREssSUFBSSxDQUFDUCxJQUFJQyxDQUFBQSxJQUFLUyxJQUFJO2dCQUNsQjtZQUNKO2dCQUNJO1FBQ1I7UUFDQUgsS0FBSztJQUNUO0lBQ0EsT0FBTztRQUFFQSxHQUFHQTtRQUFHQyxHQUFHQTtRQUFHUyxHQUFHQTtJQUFFO0FBQzlCO0FBQ0E7Ozs7O0NBS0MsR0FDTSxTQUFTQyxTQUFTWCxDQUFDLEVBQUVDLENBQUMsRUFBRVMsQ0FBQztJQUM1QlYsSUFBSVYsaURBQU9BLENBQUNVLEdBQUcsT0FBTztJQUN0QkMsSUFBSVgsaURBQU9BLENBQUNXLEdBQUc7SUFDZlMsSUFBSXBCLGlEQUFPQSxDQUFDb0IsR0FBRztJQUNmLElBQUlFLElBQUlkLEtBQUtlLEtBQUssQ0FBQ2I7SUFDbkIsSUFBSWMsSUFBSWQsSUFBSVk7SUFDWixJQUFJUCxJQUFJSyxJQUFLLEtBQUlULENBQUFBO0lBQ2pCLElBQUlLLElBQUlJLElBQUssS0FBSUksSUFBSWIsQ0FBQUE7SUFDckIsSUFBSU0sSUFBSUcsSUFBSyxLQUFJLENBQUMsSUFBSUksQ0FBQUEsSUFBS2IsQ0FBQUE7SUFDM0IsSUFBSWMsTUFBTUgsSUFBSTtJQUNkLElBQUluQixJQUFJO1FBQUNpQjtRQUFHSjtRQUFHRDtRQUFHQTtRQUFHRTtRQUFHRztLQUFFLENBQUNLLElBQUk7SUFDL0IsSUFBSXJCLElBQUk7UUFBQ2E7UUFBR0c7UUFBR0E7UUFBR0o7UUFBR0Q7UUFBR0E7S0FBRSxDQUFDVSxJQUFJO0lBQy9CLElBQUlwQixJQUFJO1FBQUNVO1FBQUdBO1FBQUdFO1FBQUdHO1FBQUdBO1FBQUdKO0tBQUUsQ0FBQ1MsSUFBSTtJQUMvQixPQUFPO1FBQUV0QixHQUFHQSxJQUFJO1FBQUtDLEdBQUdBLElBQUk7UUFBS0MsR0FBR0EsSUFBSTtJQUFJO0FBQ2hEO0FBQ0E7Ozs7O0NBS0MsR0FDTSxTQUFTcUIsU0FBU3ZCLENBQUMsRUFBRUMsQ0FBQyxFQUFFQyxDQUFDLEVBQUVzQixVQUFVO0lBQ3hDLElBQUlDLE1BQU07UUFDTjNCLDhDQUFJQSxDQUFDTyxLQUFLcUIsS0FBSyxDQUFDMUIsR0FBRzJCLFFBQVEsQ0FBQztRQUM1QjdCLDhDQUFJQSxDQUFDTyxLQUFLcUIsS0FBSyxDQUFDekIsR0FBRzBCLFFBQVEsQ0FBQztRQUM1QjdCLDhDQUFJQSxDQUFDTyxLQUFLcUIsS0FBSyxDQUFDeEIsR0FBR3lCLFFBQVEsQ0FBQztLQUMvQjtJQUNELHVDQUF1QztJQUN2QyxJQUFJSCxjQUNBQyxHQUFHLENBQUMsRUFBRSxDQUFDRyxVQUFVLENBQUNILEdBQUcsQ0FBQyxFQUFFLENBQUNJLE1BQU0sQ0FBQyxPQUNoQ0osR0FBRyxDQUFDLEVBQUUsQ0FBQ0csVUFBVSxDQUFDSCxHQUFHLENBQUMsRUFBRSxDQUFDSSxNQUFNLENBQUMsT0FDaENKLEdBQUcsQ0FBQyxFQUFFLENBQUNHLFVBQVUsQ0FBQ0gsR0FBRyxDQUFDLEVBQUUsQ0FBQ0ksTUFBTSxDQUFDLEtBQUs7UUFDckMsT0FBT0osR0FBRyxDQUFDLEVBQUUsQ0FBQ0ksTUFBTSxDQUFDLEtBQUtKLEdBQUcsQ0FBQyxFQUFFLENBQUNJLE1BQU0sQ0FBQyxLQUFLSixHQUFHLENBQUMsRUFBRSxDQUFDSSxNQUFNLENBQUM7SUFDL0Q7SUFDQSxPQUFPSixJQUFJSyxJQUFJLENBQUM7QUFDcEI7QUFDQTs7Ozs7Q0FLQyxHQUNELHNDQUFzQztBQUMvQixTQUFTQyxVQUFVL0IsQ0FBQyxFQUFFQyxDQUFDLEVBQUVDLENBQUMsRUFBRThCLENBQUMsRUFBRUMsVUFBVTtJQUM1QyxJQUFJUixNQUFNO1FBQ04zQiw4Q0FBSUEsQ0FBQ08sS0FBS3FCLEtBQUssQ0FBQzFCLEdBQUcyQixRQUFRLENBQUM7UUFDNUI3Qiw4Q0FBSUEsQ0FBQ08sS0FBS3FCLEtBQUssQ0FBQ3pCLEdBQUcwQixRQUFRLENBQUM7UUFDNUI3Qiw4Q0FBSUEsQ0FBQ08sS0FBS3FCLEtBQUssQ0FBQ3hCLEdBQUd5QixRQUFRLENBQUM7UUFDNUI3Qiw4Q0FBSUEsQ0FBQ29DLG9CQUFvQkY7S0FDNUI7SUFDRCx1Q0FBdUM7SUFDdkMsSUFBSUMsY0FDQVIsR0FBRyxDQUFDLEVBQUUsQ0FBQ0csVUFBVSxDQUFDSCxHQUFHLENBQUMsRUFBRSxDQUFDSSxNQUFNLENBQUMsT0FDaENKLEdBQUcsQ0FBQyxFQUFFLENBQUNHLFVBQVUsQ0FBQ0gsR0FBRyxDQUFDLEVBQUUsQ0FBQ0ksTUFBTSxDQUFDLE9BQ2hDSixHQUFHLENBQUMsRUFBRSxDQUFDRyxVQUFVLENBQUNILEdBQUcsQ0FBQyxFQUFFLENBQUNJLE1BQU0sQ0FBQyxPQUNoQ0osR0FBRyxDQUFDLEVBQUUsQ0FBQ0csVUFBVSxDQUFDSCxHQUFHLENBQUMsRUFBRSxDQUFDSSxNQUFNLENBQUMsS0FBSztRQUNyQyxPQUFPSixHQUFHLENBQUMsRUFBRSxDQUFDSSxNQUFNLENBQUMsS0FBS0osR0FBRyxDQUFDLEVBQUUsQ0FBQ0ksTUFBTSxDQUFDLEtBQUtKLEdBQUcsQ0FBQyxFQUFFLENBQUNJLE1BQU0sQ0FBQyxLQUFLSixHQUFHLENBQUMsRUFBRSxDQUFDSSxNQUFNLENBQUM7SUFDbEY7SUFDQSxPQUFPSixJQUFJSyxJQUFJLENBQUM7QUFDcEI7QUFDQTs7O0NBR0MsR0FDTSxTQUFTSyxjQUFjbkMsQ0FBQyxFQUFFQyxDQUFDLEVBQUVDLENBQUMsRUFBRThCLENBQUM7SUFDcEMsSUFBSVAsTUFBTTtRQUNOM0IsOENBQUlBLENBQUNvQyxvQkFBb0JGO1FBQ3pCbEMsOENBQUlBLENBQUNPLEtBQUtxQixLQUFLLENBQUMxQixHQUFHMkIsUUFBUSxDQUFDO1FBQzVCN0IsOENBQUlBLENBQUNPLEtBQUtxQixLQUFLLENBQUN6QixHQUFHMEIsUUFBUSxDQUFDO1FBQzVCN0IsOENBQUlBLENBQUNPLEtBQUtxQixLQUFLLENBQUN4QixHQUFHeUIsUUFBUSxDQUFDO0tBQy9CO0lBQ0QsT0FBT0YsSUFBSUssSUFBSSxDQUFDO0FBQ3BCO0FBQ0Esc0NBQXNDLEdBQy9CLFNBQVNJLG9CQUFvQnhCLENBQUM7SUFDakMsT0FBT0wsS0FBS3FCLEtBQUssQ0FBQ1UsV0FBVzFCLEtBQUssS0FBS2lCLFFBQVEsQ0FBQztBQUNwRDtBQUNBLHNDQUFzQyxHQUMvQixTQUFTVSxvQkFBb0I5QixDQUFDO0lBQ2pDLE9BQU8rQixnQkFBZ0IvQixLQUFLO0FBQ2hDO0FBQ0EscURBQXFELEdBQzlDLFNBQVMrQixnQkFBZ0JDLEdBQUc7SUFDL0IsT0FBT0MsU0FBU0QsS0FBSztBQUN6QjtBQUNPLFNBQVNFLG9CQUFvQkMsS0FBSztJQUNyQyxPQUFPO1FBQ0gxQyxHQUFHMEMsU0FBUztRQUNaekMsR0FBRyxDQUFDeUMsUUFBUSxNQUFLLEtBQU07UUFDdkJ4QyxHQUFHd0MsUUFBUTtJQUNmO0FBQ0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kcnV0b28vLi9ub2RlX21vZHVsZXMvQGN0cmwvdGlueWNvbG9yL2Rpc3QvbW9kdWxlL2NvbnZlcnNpb24uanM/NjZhZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBib3VuZDAxLCBwYWQyIH0gZnJvbSAnLi91dGlsLmpzJztcbi8vIGByZ2JUb0hzbGAsIGByZ2JUb0hzdmAsIGBoc2xUb1JnYmAsIGBoc3ZUb1JnYmAgbW9kaWZpZWQgZnJvbTpcbi8vIDxodHRwOi8vbWppamFja3Nvbi5jb20vMjAwOC8wMi9yZ2ItdG8taHNsLWFuZC1yZ2ItdG8taHN2LWNvbG9yLW1vZGVsLWNvbnZlcnNpb24tYWxnb3JpdGhtcy1pbi1qYXZhc2NyaXB0PlxuLyoqXG4gKiBIYW5kbGUgYm91bmRzIC8gcGVyY2VudGFnZSBjaGVja2luZyB0byBjb25mb3JtIHRvIENTUyBjb2xvciBzcGVjXG4gKiA8aHR0cDovL3d3dy53My5vcmcvVFIvY3NzMy1jb2xvci8+XG4gKiAqQXNzdW1lczoqIHIsIGcsIGIgaW4gWzAsIDI1NV0gb3IgWzAsIDFdXG4gKiAqUmV0dXJuczoqIHsgciwgZywgYiB9IGluIFswLCAyNTVdXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiByZ2JUb1JnYihyLCBnLCBiKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgcjogYm91bmQwMShyLCAyNTUpICogMjU1LFxuICAgICAgICBnOiBib3VuZDAxKGcsIDI1NSkgKiAyNTUsXG4gICAgICAgIGI6IGJvdW5kMDEoYiwgMjU1KSAqIDI1NSxcbiAgICB9O1xufVxuLyoqXG4gKiBDb252ZXJ0cyBhbiBSR0IgY29sb3IgdmFsdWUgdG8gSFNMLlxuICogKkFzc3VtZXM6KiByLCBnLCBhbmQgYiBhcmUgY29udGFpbmVkIGluIFswLCAyNTVdIG9yIFswLCAxXVxuICogKlJldHVybnM6KiB7IGgsIHMsIGwgfSBpbiBbMCwxXVxuICovXG5leHBvcnQgZnVuY3Rpb24gcmdiVG9Ic2wociwgZywgYikge1xuICAgIHIgPSBib3VuZDAxKHIsIDI1NSk7XG4gICAgZyA9IGJvdW5kMDEoZywgMjU1KTtcbiAgICBiID0gYm91bmQwMShiLCAyNTUpO1xuICAgIHZhciBtYXggPSBNYXRoLm1heChyLCBnLCBiKTtcbiAgICB2YXIgbWluID0gTWF0aC5taW4ociwgZywgYik7XG4gICAgdmFyIGggPSAwO1xuICAgIHZhciBzID0gMDtcbiAgICB2YXIgbCA9IChtYXggKyBtaW4pIC8gMjtcbiAgICBpZiAobWF4ID09PSBtaW4pIHtcbiAgICAgICAgcyA9IDA7XG4gICAgICAgIGggPSAwOyAvLyBhY2hyb21hdGljXG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICB2YXIgZCA9IG1heCAtIG1pbjtcbiAgICAgICAgcyA9IGwgPiAwLjUgPyBkIC8gKDIgLSBtYXggLSBtaW4pIDogZCAvIChtYXggKyBtaW4pO1xuICAgICAgICBzd2l0Y2ggKG1heCkge1xuICAgICAgICAgICAgY2FzZSByOlxuICAgICAgICAgICAgICAgIGggPSAoZyAtIGIpIC8gZCArIChnIDwgYiA/IDYgOiAwKTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgZzpcbiAgICAgICAgICAgICAgICBoID0gKGIgLSByKSAvIGQgKyAyO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSBiOlxuICAgICAgICAgICAgICAgIGggPSAociAtIGcpIC8gZCArIDQ7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICB9XG4gICAgICAgIGggLz0gNjtcbiAgICB9XG4gICAgcmV0dXJuIHsgaDogaCwgczogcywgbDogbCB9O1xufVxuZnVuY3Rpb24gaHVlMnJnYihwLCBxLCB0KSB7XG4gICAgaWYgKHQgPCAwKSB7XG4gICAgICAgIHQgKz0gMTtcbiAgICB9XG4gICAgaWYgKHQgPiAxKSB7XG4gICAgICAgIHQgLT0gMTtcbiAgICB9XG4gICAgaWYgKHQgPCAxIC8gNikge1xuICAgICAgICByZXR1cm4gcCArIChxIC0gcCkgKiAoNiAqIHQpO1xuICAgIH1cbiAgICBpZiAodCA8IDEgLyAyKSB7XG4gICAgICAgIHJldHVybiBxO1xuICAgIH1cbiAgICBpZiAodCA8IDIgLyAzKSB7XG4gICAgICAgIHJldHVybiBwICsgKHEgLSBwKSAqICgyIC8gMyAtIHQpICogNjtcbiAgICB9XG4gICAgcmV0dXJuIHA7XG59XG4vKipcbiAqIENvbnZlcnRzIGFuIEhTTCBjb2xvciB2YWx1ZSB0byBSR0IuXG4gKlxuICogKkFzc3VtZXM6KiBoIGlzIGNvbnRhaW5lZCBpbiBbMCwgMV0gb3IgWzAsIDM2MF0gYW5kIHMgYW5kIGwgYXJlIGNvbnRhaW5lZCBbMCwgMV0gb3IgWzAsIDEwMF1cbiAqICpSZXR1cm5zOiogeyByLCBnLCBiIH0gaW4gdGhlIHNldCBbMCwgMjU1XVxuICovXG5leHBvcnQgZnVuY3Rpb24gaHNsVG9SZ2IoaCwgcywgbCkge1xuICAgIHZhciByO1xuICAgIHZhciBnO1xuICAgIHZhciBiO1xuICAgIGggPSBib3VuZDAxKGgsIDM2MCk7XG4gICAgcyA9IGJvdW5kMDEocywgMTAwKTtcbiAgICBsID0gYm91bmQwMShsLCAxMDApO1xuICAgIGlmIChzID09PSAwKSB7XG4gICAgICAgIC8vIGFjaHJvbWF0aWNcbiAgICAgICAgZyA9IGw7XG4gICAgICAgIGIgPSBsO1xuICAgICAgICByID0gbDtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHZhciBxID0gbCA8IDAuNSA/IGwgKiAoMSArIHMpIDogbCArIHMgLSBsICogcztcbiAgICAgICAgdmFyIHAgPSAyICogbCAtIHE7XG4gICAgICAgIHIgPSBodWUycmdiKHAsIHEsIGggKyAxIC8gMyk7XG4gICAgICAgIGcgPSBodWUycmdiKHAsIHEsIGgpO1xuICAgICAgICBiID0gaHVlMnJnYihwLCBxLCBoIC0gMSAvIDMpO1xuICAgIH1cbiAgICByZXR1cm4geyByOiByICogMjU1LCBnOiBnICogMjU1LCBiOiBiICogMjU1IH07XG59XG4vKipcbiAqIENvbnZlcnRzIGFuIFJHQiBjb2xvciB2YWx1ZSB0byBIU1ZcbiAqXG4gKiAqQXNzdW1lczoqIHIsIGcsIGFuZCBiIGFyZSBjb250YWluZWQgaW4gdGhlIHNldCBbMCwgMjU1XSBvciBbMCwgMV1cbiAqICpSZXR1cm5zOiogeyBoLCBzLCB2IH0gaW4gWzAsMV1cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJnYlRvSHN2KHIsIGcsIGIpIHtcbiAgICByID0gYm91bmQwMShyLCAyNTUpO1xuICAgIGcgPSBib3VuZDAxKGcsIDI1NSk7XG4gICAgYiA9IGJvdW5kMDEoYiwgMjU1KTtcbiAgICB2YXIgbWF4ID0gTWF0aC5tYXgociwgZywgYik7XG4gICAgdmFyIG1pbiA9IE1hdGgubWluKHIsIGcsIGIpO1xuICAgIHZhciBoID0gMDtcbiAgICB2YXIgdiA9IG1heDtcbiAgICB2YXIgZCA9IG1heCAtIG1pbjtcbiAgICB2YXIgcyA9IG1heCA9PT0gMCA/IDAgOiBkIC8gbWF4O1xuICAgIGlmIChtYXggPT09IG1pbikge1xuICAgICAgICBoID0gMDsgLy8gYWNocm9tYXRpY1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgc3dpdGNoIChtYXgpIHtcbiAgICAgICAgICAgIGNhc2UgcjpcbiAgICAgICAgICAgICAgICBoID0gKGcgLSBiKSAvIGQgKyAoZyA8IGIgPyA2IDogMCk7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBjYXNlIGc6XG4gICAgICAgICAgICAgICAgaCA9IChiIC0gcikgLyBkICsgMjtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgYjpcbiAgICAgICAgICAgICAgICBoID0gKHIgLSBnKSAvIGQgKyA0O1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICBoIC89IDY7XG4gICAgfVxuICAgIHJldHVybiB7IGg6IGgsIHM6IHMsIHY6IHYgfTtcbn1cbi8qKlxuICogQ29udmVydHMgYW4gSFNWIGNvbG9yIHZhbHVlIHRvIFJHQi5cbiAqXG4gKiAqQXNzdW1lczoqIGggaXMgY29udGFpbmVkIGluIFswLCAxXSBvciBbMCwgMzYwXSBhbmQgcyBhbmQgdiBhcmUgY29udGFpbmVkIGluIFswLCAxXSBvciBbMCwgMTAwXVxuICogKlJldHVybnM6KiB7IHIsIGcsIGIgfSBpbiB0aGUgc2V0IFswLCAyNTVdXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoc3ZUb1JnYihoLCBzLCB2KSB7XG4gICAgaCA9IGJvdW5kMDEoaCwgMzYwKSAqIDY7XG4gICAgcyA9IGJvdW5kMDEocywgMTAwKTtcbiAgICB2ID0gYm91bmQwMSh2LCAxMDApO1xuICAgIHZhciBpID0gTWF0aC5mbG9vcihoKTtcbiAgICB2YXIgZiA9IGggLSBpO1xuICAgIHZhciBwID0gdiAqICgxIC0gcyk7XG4gICAgdmFyIHEgPSB2ICogKDEgLSBmICogcyk7XG4gICAgdmFyIHQgPSB2ICogKDEgLSAoMSAtIGYpICogcyk7XG4gICAgdmFyIG1vZCA9IGkgJSA2O1xuICAgIHZhciByID0gW3YsIHEsIHAsIHAsIHQsIHZdW21vZF07XG4gICAgdmFyIGcgPSBbdCwgdiwgdiwgcSwgcCwgcF1bbW9kXTtcbiAgICB2YXIgYiA9IFtwLCBwLCB0LCB2LCB2LCBxXVttb2RdO1xuICAgIHJldHVybiB7IHI6IHIgKiAyNTUsIGc6IGcgKiAyNTUsIGI6IGIgKiAyNTUgfTtcbn1cbi8qKlxuICogQ29udmVydHMgYW4gUkdCIGNvbG9yIHRvIGhleFxuICpcbiAqIEFzc3VtZXMgciwgZywgYW5kIGIgYXJlIGNvbnRhaW5lZCBpbiB0aGUgc2V0IFswLCAyNTVdXG4gKiBSZXR1cm5zIGEgMyBvciA2IGNoYXJhY3RlciBoZXhcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJnYlRvSGV4KHIsIGcsIGIsIGFsbG93M0NoYXIpIHtcbiAgICB2YXIgaGV4ID0gW1xuICAgICAgICBwYWQyKE1hdGgucm91bmQocikudG9TdHJpbmcoMTYpKSxcbiAgICAgICAgcGFkMihNYXRoLnJvdW5kKGcpLnRvU3RyaW5nKDE2KSksXG4gICAgICAgIHBhZDIoTWF0aC5yb3VuZChiKS50b1N0cmluZygxNikpLFxuICAgIF07XG4gICAgLy8gUmV0dXJuIGEgMyBjaGFyYWN0ZXIgaGV4IGlmIHBvc3NpYmxlXG4gICAgaWYgKGFsbG93M0NoYXIgJiZcbiAgICAgICAgaGV4WzBdLnN0YXJ0c1dpdGgoaGV4WzBdLmNoYXJBdCgxKSkgJiZcbiAgICAgICAgaGV4WzFdLnN0YXJ0c1dpdGgoaGV4WzFdLmNoYXJBdCgxKSkgJiZcbiAgICAgICAgaGV4WzJdLnN0YXJ0c1dpdGgoaGV4WzJdLmNoYXJBdCgxKSkpIHtcbiAgICAgICAgcmV0dXJuIGhleFswXS5jaGFyQXQoMCkgKyBoZXhbMV0uY2hhckF0KDApICsgaGV4WzJdLmNoYXJBdCgwKTtcbiAgICB9XG4gICAgcmV0dXJuIGhleC5qb2luKCcnKTtcbn1cbi8qKlxuICogQ29udmVydHMgYW4gUkdCQSBjb2xvciBwbHVzIGFscGhhIHRyYW5zcGFyZW5jeSB0byBoZXhcbiAqXG4gKiBBc3N1bWVzIHIsIGcsIGIgYXJlIGNvbnRhaW5lZCBpbiB0aGUgc2V0IFswLCAyNTVdIGFuZFxuICogYSBpbiBbMCwgMV0uIFJldHVybnMgYSA0IG9yIDggY2hhcmFjdGVyIHJnYmEgaGV4XG4gKi9cbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBtYXgtcGFyYW1zXG5leHBvcnQgZnVuY3Rpb24gcmdiYVRvSGV4KHIsIGcsIGIsIGEsIGFsbG93NENoYXIpIHtcbiAgICB2YXIgaGV4ID0gW1xuICAgICAgICBwYWQyKE1hdGgucm91bmQocikudG9TdHJpbmcoMTYpKSxcbiAgICAgICAgcGFkMihNYXRoLnJvdW5kKGcpLnRvU3RyaW5nKDE2KSksXG4gICAgICAgIHBhZDIoTWF0aC5yb3VuZChiKS50b1N0cmluZygxNikpLFxuICAgICAgICBwYWQyKGNvbnZlcnREZWNpbWFsVG9IZXgoYSkpLFxuICAgIF07XG4gICAgLy8gUmV0dXJuIGEgNCBjaGFyYWN0ZXIgaGV4IGlmIHBvc3NpYmxlXG4gICAgaWYgKGFsbG93NENoYXIgJiZcbiAgICAgICAgaGV4WzBdLnN0YXJ0c1dpdGgoaGV4WzBdLmNoYXJBdCgxKSkgJiZcbiAgICAgICAgaGV4WzFdLnN0YXJ0c1dpdGgoaGV4WzFdLmNoYXJBdCgxKSkgJiZcbiAgICAgICAgaGV4WzJdLnN0YXJ0c1dpdGgoaGV4WzJdLmNoYXJBdCgxKSkgJiZcbiAgICAgICAgaGV4WzNdLnN0YXJ0c1dpdGgoaGV4WzNdLmNoYXJBdCgxKSkpIHtcbiAgICAgICAgcmV0dXJuIGhleFswXS5jaGFyQXQoMCkgKyBoZXhbMV0uY2hhckF0KDApICsgaGV4WzJdLmNoYXJBdCgwKSArIGhleFszXS5jaGFyQXQoMCk7XG4gICAgfVxuICAgIHJldHVybiBoZXguam9pbignJyk7XG59XG4vKipcbiAqIENvbnZlcnRzIGFuIFJHQkEgY29sb3IgdG8gYW4gQVJHQiBIZXg4IHN0cmluZ1xuICogUmFyZWx5IHVzZWQsIGJ1dCByZXF1aXJlZCBmb3IgXCJ0b0ZpbHRlcigpXCJcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHJnYmFUb0FyZ2JIZXgociwgZywgYiwgYSkge1xuICAgIHZhciBoZXggPSBbXG4gICAgICAgIHBhZDIoY29udmVydERlY2ltYWxUb0hleChhKSksXG4gICAgICAgIHBhZDIoTWF0aC5yb3VuZChyKS50b1N0cmluZygxNikpLFxuICAgICAgICBwYWQyKE1hdGgucm91bmQoZykudG9TdHJpbmcoMTYpKSxcbiAgICAgICAgcGFkMihNYXRoLnJvdW5kKGIpLnRvU3RyaW5nKDE2KSksXG4gICAgXTtcbiAgICByZXR1cm4gaGV4LmpvaW4oJycpO1xufVxuLyoqIENvbnZlcnRzIGEgZGVjaW1hbCB0byBhIGhleCB2YWx1ZSAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNvbnZlcnREZWNpbWFsVG9IZXgoZCkge1xuICAgIHJldHVybiBNYXRoLnJvdW5kKHBhcnNlRmxvYXQoZCkgKiAyNTUpLnRvU3RyaW5nKDE2KTtcbn1cbi8qKiBDb252ZXJ0cyBhIGhleCB2YWx1ZSB0byBhIGRlY2ltYWwgKi9cbmV4cG9ydCBmdW5jdGlvbiBjb252ZXJ0SGV4VG9EZWNpbWFsKGgpIHtcbiAgICByZXR1cm4gcGFyc2VJbnRGcm9tSGV4KGgpIC8gMjU1O1xufVxuLyoqIFBhcnNlIGEgYmFzZS0xNiBoZXggdmFsdWUgaW50byBhIGJhc2UtMTAgaW50ZWdlciAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhcnNlSW50RnJvbUhleCh2YWwpIHtcbiAgICByZXR1cm4gcGFyc2VJbnQodmFsLCAxNik7XG59XG5leHBvcnQgZnVuY3Rpb24gbnVtYmVySW5wdXRUb09iamVjdChjb2xvcikge1xuICAgIHJldHVybiB7XG4gICAgICAgIHI6IGNvbG9yID4+IDE2LFxuICAgICAgICBnOiAoY29sb3IgJiAweGZmMDApID4+IDgsXG4gICAgICAgIGI6IGNvbG9yICYgMHhmZixcbiAgICB9O1xufVxuIl0sIm5hbWVzIjpbImJvdW5kMDEiLCJwYWQyIiwicmdiVG9SZ2IiLCJyIiwiZyIsImIiLCJyZ2JUb0hzbCIsIm1heCIsIk1hdGgiLCJtaW4iLCJoIiwicyIsImwiLCJkIiwiaHVlMnJnYiIsInAiLCJxIiwidCIsImhzbFRvUmdiIiwicmdiVG9Ic3YiLCJ2IiwiaHN2VG9SZ2IiLCJpIiwiZmxvb3IiLCJmIiwibW9kIiwicmdiVG9IZXgiLCJhbGxvdzNDaGFyIiwiaGV4Iiwicm91bmQiLCJ0b1N0cmluZyIsInN0YXJ0c1dpdGgiLCJjaGFyQXQiLCJqb2luIiwicmdiYVRvSGV4IiwiYSIsImFsbG93NENoYXIiLCJjb252ZXJ0RGVjaW1hbFRvSGV4IiwicmdiYVRvQXJnYkhleCIsInBhcnNlRmxvYXQiLCJjb252ZXJ0SGV4VG9EZWNpbWFsIiwicGFyc2VJbnRGcm9tSGV4IiwidmFsIiwicGFyc2VJbnQiLCJudW1iZXJJbnB1dFRvT2JqZWN0IiwiY29sb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ctrl/tinycolor/dist/module/conversion.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ctrl/tinycolor/dist/module/css-color-names.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@ctrl/tinycolor/dist/module/css-color-names.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   names: () => (/* binding */ names)\n/* harmony export */ });\n// https://github.com/bahamas10/css-color-names/blob/master/css-color-names.json\n/**\n * @hidden\n */ var names = {\n    aliceblue: \"#f0f8ff\",\n    antiquewhite: \"#faebd7\",\n    aqua: \"#00ffff\",\n    aquamarine: \"#7fffd4\",\n    azure: \"#f0ffff\",\n    beige: \"#f5f5dc\",\n    bisque: \"#ffe4c4\",\n    black: \"#000000\",\n    blanchedalmond: \"#ffebcd\",\n    blue: \"#0000ff\",\n    blueviolet: \"#8a2be2\",\n    brown: \"#a52a2a\",\n    burlywood: \"#deb887\",\n    cadetblue: \"#5f9ea0\",\n    chartreuse: \"#7fff00\",\n    chocolate: \"#d2691e\",\n    coral: \"#ff7f50\",\n    cornflowerblue: \"#6495ed\",\n    cornsilk: \"#fff8dc\",\n    crimson: \"#dc143c\",\n    cyan: \"#00ffff\",\n    darkblue: \"#00008b\",\n    darkcyan: \"#008b8b\",\n    darkgoldenrod: \"#b8860b\",\n    darkgray: \"#a9a9a9\",\n    darkgreen: \"#006400\",\n    darkgrey: \"#a9a9a9\",\n    darkkhaki: \"#bdb76b\",\n    darkmagenta: \"#8b008b\",\n    darkolivegreen: \"#556b2f\",\n    darkorange: \"#ff8c00\",\n    darkorchid: \"#9932cc\",\n    darkred: \"#8b0000\",\n    darksalmon: \"#e9967a\",\n    darkseagreen: \"#8fbc8f\",\n    darkslateblue: \"#483d8b\",\n    darkslategray: \"#2f4f4f\",\n    darkslategrey: \"#2f4f4f\",\n    darkturquoise: \"#00ced1\",\n    darkviolet: \"#9400d3\",\n    deeppink: \"#ff1493\",\n    deepskyblue: \"#00bfff\",\n    dimgray: \"#696969\",\n    dimgrey: \"#696969\",\n    dodgerblue: \"#1e90ff\",\n    firebrick: \"#b22222\",\n    floralwhite: \"#fffaf0\",\n    forestgreen: \"#228b22\",\n    fuchsia: \"#ff00ff\",\n    gainsboro: \"#dcdcdc\",\n    ghostwhite: \"#f8f8ff\",\n    goldenrod: \"#daa520\",\n    gold: \"#ffd700\",\n    gray: \"#808080\",\n    green: \"#008000\",\n    greenyellow: \"#adff2f\",\n    grey: \"#808080\",\n    honeydew: \"#f0fff0\",\n    hotpink: \"#ff69b4\",\n    indianred: \"#cd5c5c\",\n    indigo: \"#4b0082\",\n    ivory: \"#fffff0\",\n    khaki: \"#f0e68c\",\n    lavenderblush: \"#fff0f5\",\n    lavender: \"#e6e6fa\",\n    lawngreen: \"#7cfc00\",\n    lemonchiffon: \"#fffacd\",\n    lightblue: \"#add8e6\",\n    lightcoral: \"#f08080\",\n    lightcyan: \"#e0ffff\",\n    lightgoldenrodyellow: \"#fafad2\",\n    lightgray: \"#d3d3d3\",\n    lightgreen: \"#90ee90\",\n    lightgrey: \"#d3d3d3\",\n    lightpink: \"#ffb6c1\",\n    lightsalmon: \"#ffa07a\",\n    lightseagreen: \"#20b2aa\",\n    lightskyblue: \"#87cefa\",\n    lightslategray: \"#778899\",\n    lightslategrey: \"#778899\",\n    lightsteelblue: \"#b0c4de\",\n    lightyellow: \"#ffffe0\",\n    lime: \"#00ff00\",\n    limegreen: \"#32cd32\",\n    linen: \"#faf0e6\",\n    magenta: \"#ff00ff\",\n    maroon: \"#800000\",\n    mediumaquamarine: \"#66cdaa\",\n    mediumblue: \"#0000cd\",\n    mediumorchid: \"#ba55d3\",\n    mediumpurple: \"#9370db\",\n    mediumseagreen: \"#3cb371\",\n    mediumslateblue: \"#7b68ee\",\n    mediumspringgreen: \"#00fa9a\",\n    mediumturquoise: \"#48d1cc\",\n    mediumvioletred: \"#c71585\",\n    midnightblue: \"#191970\",\n    mintcream: \"#f5fffa\",\n    mistyrose: \"#ffe4e1\",\n    moccasin: \"#ffe4b5\",\n    navajowhite: \"#ffdead\",\n    navy: \"#000080\",\n    oldlace: \"#fdf5e6\",\n    olive: \"#808000\",\n    olivedrab: \"#6b8e23\",\n    orange: \"#ffa500\",\n    orangered: \"#ff4500\",\n    orchid: \"#da70d6\",\n    palegoldenrod: \"#eee8aa\",\n    palegreen: \"#98fb98\",\n    paleturquoise: \"#afeeee\",\n    palevioletred: \"#db7093\",\n    papayawhip: \"#ffefd5\",\n    peachpuff: \"#ffdab9\",\n    peru: \"#cd853f\",\n    pink: \"#ffc0cb\",\n    plum: \"#dda0dd\",\n    powderblue: \"#b0e0e6\",\n    purple: \"#800080\",\n    rebeccapurple: \"#663399\",\n    red: \"#ff0000\",\n    rosybrown: \"#bc8f8f\",\n    royalblue: \"#4169e1\",\n    saddlebrown: \"#8b4513\",\n    salmon: \"#fa8072\",\n    sandybrown: \"#f4a460\",\n    seagreen: \"#2e8b57\",\n    seashell: \"#fff5ee\",\n    sienna: \"#a0522d\",\n    silver: \"#c0c0c0\",\n    skyblue: \"#87ceeb\",\n    slateblue: \"#6a5acd\",\n    slategray: \"#708090\",\n    slategrey: \"#708090\",\n    snow: \"#fffafa\",\n    springgreen: \"#00ff7f\",\n    steelblue: \"#4682b4\",\n    tan: \"#d2b48c\",\n    teal: \"#008080\",\n    thistle: \"#d8bfd8\",\n    tomato: \"#ff6347\",\n    turquoise: \"#40e0d0\",\n    violet: \"#ee82ee\",\n    wheat: \"#f5deb3\",\n    white: \"#ffffff\",\n    whitesmoke: \"#f5f5f5\",\n    yellow: \"#ffff00\",\n    yellowgreen: \"#9acd32\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ctrl/tinycolor/dist/module/css-color-names.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ctrl/tinycolor/dist/module/format-input.js":
/*!******************************************************************!*\
  !*** ./node_modules/@ctrl/tinycolor/dist/module/format-input.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inputToRGB: () => (/* binding */ inputToRGB),\n/* harmony export */   isValidCSSUnit: () => (/* binding */ isValidCSSUnit),\n/* harmony export */   stringInputToObject: () => (/* binding */ stringInputToObject)\n/* harmony export */ });\n/* harmony import */ var _conversion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./conversion.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/conversion.js\");\n/* harmony import */ var _css_color_names_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./css-color-names.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/css-color-names.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/util.js\");\n/* eslint-disable @typescript-eslint/no-redundant-type-constituents */ \n\n\n/**\n * Given a string or object, convert that input to RGB\n *\n * Possible string inputs:\n * ```\n * \"red\"\n * \"#f00\" or \"f00\"\n * \"#ff0000\" or \"ff0000\"\n * \"#ff000000\" or \"ff000000\"\n * \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n * \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n * \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n * \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n * \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n * \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n * \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n * ```\n */ function inputToRGB(color) {\n    var rgb = {\n        r: 0,\n        g: 0,\n        b: 0\n    };\n    var a = 1;\n    var s = null;\n    var v = null;\n    var l = null;\n    var ok = false;\n    var format = false;\n    if (typeof color === \"string\") {\n        color = stringInputToObject(color);\n    }\n    if (typeof color === \"object\") {\n        if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n            rgb = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToRgb)(color.r, color.g, color.b);\n            ok = true;\n            format = String(color.r).substr(-1) === \"%\" ? \"prgb\" : \"rgb\";\n        } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n            s = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.convertToPercentage)(color.s);\n            v = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.convertToPercentage)(color.v);\n            rgb = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.hsvToRgb)(color.h, s, v);\n            ok = true;\n            format = \"hsv\";\n        } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n            s = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.convertToPercentage)(color.s);\n            l = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.convertToPercentage)(color.l);\n            rgb = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.hslToRgb)(color.h, s, l);\n            ok = true;\n            format = \"hsl\";\n        }\n        if (Object.prototype.hasOwnProperty.call(color, \"a\")) {\n            a = color.a;\n        }\n    }\n    a = (0,_util_js__WEBPACK_IMPORTED_MODULE_1__.boundAlpha)(a);\n    return {\n        ok: ok,\n        format: color.format || format,\n        r: Math.min(255, Math.max(rgb.r, 0)),\n        g: Math.min(255, Math.max(rgb.g, 0)),\n        b: Math.min(255, Math.max(rgb.b, 0)),\n        a: a\n    };\n}\n// <http://www.w3.org/TR/css3-values/#integers>\nvar CSS_INTEGER = \"[-\\\\+]?\\\\d+%?\";\n// <http://www.w3.org/TR/css3-values/#number-value>\nvar CSS_NUMBER = \"[-\\\\+]?\\\\d*\\\\.\\\\d+%?\";\n// Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\nvar CSS_UNIT = \"(?:\".concat(CSS_NUMBER, \")|(?:\").concat(CSS_INTEGER, \")\");\n// Actual matching.\n// Parentheses and commas are optional, but not required.\n// Whitespace can take the place of commas or opening paren\nvar PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\".concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")[,|\\\\s]+(\").concat(CSS_UNIT, \")\\\\s*\\\\)?\");\nvar matchers = {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp(\"rgb\" + PERMISSIVE_MATCH3),\n    rgba: new RegExp(\"rgba\" + PERMISSIVE_MATCH4),\n    hsl: new RegExp(\"hsl\" + PERMISSIVE_MATCH3),\n    hsla: new RegExp(\"hsla\" + PERMISSIVE_MATCH4),\n    hsv: new RegExp(\"hsv\" + PERMISSIVE_MATCH3),\n    hsva: new RegExp(\"hsva\" + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n};\n/**\n * Permissive string parsing.  Take in a number of formats, and output an object\n * based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\n */ function stringInputToObject(color) {\n    color = color.trim().toLowerCase();\n    if (color.length === 0) {\n        return false;\n    }\n    var named = false;\n    if (_css_color_names_js__WEBPACK_IMPORTED_MODULE_2__.names[color]) {\n        color = _css_color_names_js__WEBPACK_IMPORTED_MODULE_2__.names[color];\n        named = true;\n    } else if (color === \"transparent\") {\n        return {\n            r: 0,\n            g: 0,\n            b: 0,\n            a: 0,\n            format: \"name\"\n        };\n    }\n    // Try to match string input using regular expressions.\n    // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n    // Just return an object and let the conversion functions handle that.\n    // This way the result will be the same whether the tinycolor is initialized with string or object.\n    var match = matchers.rgb.exec(color);\n    if (match) {\n        return {\n            r: match[1],\n            g: match[2],\n            b: match[3]\n        };\n    }\n    match = matchers.rgba.exec(color);\n    if (match) {\n        return {\n            r: match[1],\n            g: match[2],\n            b: match[3],\n            a: match[4]\n        };\n    }\n    match = matchers.hsl.exec(color);\n    if (match) {\n        return {\n            h: match[1],\n            s: match[2],\n            l: match[3]\n        };\n    }\n    match = matchers.hsla.exec(color);\n    if (match) {\n        return {\n            h: match[1],\n            s: match[2],\n            l: match[3],\n            a: match[4]\n        };\n    }\n    match = matchers.hsv.exec(color);\n    if (match) {\n        return {\n            h: match[1],\n            s: match[2],\n            v: match[3]\n        };\n    }\n    match = matchers.hsva.exec(color);\n    if (match) {\n        return {\n            h: match[1],\n            s: match[2],\n            v: match[3],\n            a: match[4]\n        };\n    }\n    match = matchers.hex8.exec(color);\n    if (match) {\n        return {\n            r: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[1]),\n            g: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[2]),\n            b: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[3]),\n            a: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.convertHexToDecimal)(match[4]),\n            format: named ? \"name\" : \"hex8\"\n        };\n    }\n    match = matchers.hex6.exec(color);\n    if (match) {\n        return {\n            r: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[1]),\n            g: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[2]),\n            b: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[3]),\n            format: named ? \"name\" : \"hex\"\n        };\n    }\n    match = matchers.hex4.exec(color);\n    if (match) {\n        return {\n            r: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[1] + match[1]),\n            g: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[2] + match[2]),\n            b: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[3] + match[3]),\n            a: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.convertHexToDecimal)(match[4] + match[4]),\n            format: named ? \"name\" : \"hex8\"\n        };\n    }\n    match = matchers.hex3.exec(color);\n    if (match) {\n        return {\n            r: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[1] + match[1]),\n            g: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[2] + match[2]),\n            b: (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.parseIntFromHex)(match[3] + match[3]),\n            format: named ? \"name\" : \"hex\"\n        };\n    }\n    return false;\n}\n/**\n * Check to see if it looks like a CSS unit\n * (see `matchers` above for definition).\n */ function isValidCSSUnit(color) {\n    return Boolean(matchers.CSS_UNIT.exec(String(color)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ctrl/tinycolor/dist/module/format-input.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ctrl/tinycolor/dist/module/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/@ctrl/tinycolor/dist/module/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TinyColor: () => (/* binding */ TinyColor),\n/* harmony export */   tinycolor: () => (/* binding */ tinycolor)\n/* harmony export */ });\n/* harmony import */ var _conversion_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./conversion.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/conversion.js\");\n/* harmony import */ var _css_color_names_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./css-color-names.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/css-color-names.js\");\n/* harmony import */ var _format_input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./format-input */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/format-input.js\");\n/* harmony import */ var _util_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util.js */ \"(ssr)/./node_modules/@ctrl/tinycolor/dist/module/util.js\");\n\n\n\n\nvar TinyColor = /** @class */ function() {\n    function TinyColor(color, opts) {\n        if (color === void 0) {\n            color = \"\";\n        }\n        if (opts === void 0) {\n            opts = {};\n        }\n        var _a;\n        // If input is already a tinycolor, return itself\n        if (color instanceof TinyColor) {\n            // eslint-disable-next-line no-constructor-return\n            return color;\n        }\n        if (typeof color === \"number\") {\n            color = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.numberInputToObject)(color);\n        }\n        this.originalInput = color;\n        var rgb = (0,_format_input__WEBPACK_IMPORTED_MODULE_1__.inputToRGB)(color);\n        this.originalInput = color;\n        this.r = rgb.r;\n        this.g = rgb.g;\n        this.b = rgb.b;\n        this.a = rgb.a;\n        this.roundA = Math.round(100 * this.a) / 100;\n        this.format = (_a = opts.format) !== null && _a !== void 0 ? _a : rgb.format;\n        this.gradientType = opts.gradientType;\n        // Don't let the range of [0,255] come back in [0,1].\n        // Potentially lose a little bit of precision here, but will fix issues where\n        // .5 gets interpreted as half of the total, instead of half of 1\n        // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n        if (this.r < 1) {\n            this.r = Math.round(this.r);\n        }\n        if (this.g < 1) {\n            this.g = Math.round(this.g);\n        }\n        if (this.b < 1) {\n            this.b = Math.round(this.b);\n        }\n        this.isValid = rgb.ok;\n    }\n    TinyColor.prototype.isDark = function() {\n        return this.getBrightness() < 128;\n    };\n    TinyColor.prototype.isLight = function() {\n        return !this.isDark();\n    };\n    /**\n     * Returns the perceived brightness of the color, from 0-255.\n     */ TinyColor.prototype.getBrightness = function() {\n        // http://www.w3.org/TR/AERT#color-contrast\n        var rgb = this.toRgb();\n        return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n    };\n    /**\n     * Returns the perceived luminance of a color, from 0-1.\n     */ TinyColor.prototype.getLuminance = function() {\n        // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n        var rgb = this.toRgb();\n        var R;\n        var G;\n        var B;\n        var RsRGB = rgb.r / 255;\n        var GsRGB = rgb.g / 255;\n        var BsRGB = rgb.b / 255;\n        if (RsRGB <= 0.03928) {\n            R = RsRGB / 12.92;\n        } else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (GsRGB <= 0.03928) {\n            G = GsRGB / 12.92;\n        } else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (BsRGB <= 0.03928) {\n            B = BsRGB / 12.92;\n        } else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n        }\n        return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n    };\n    /**\n     * Returns the alpha value of a color, from 0-1.\n     */ TinyColor.prototype.getAlpha = function() {\n        return this.a;\n    };\n    /**\n     * Sets the alpha value on the current color.\n     *\n     * @param alpha - The new alpha value. The accepted range is 0-1.\n     */ TinyColor.prototype.setAlpha = function(alpha) {\n        this.a = (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.boundAlpha)(alpha);\n        this.roundA = Math.round(100 * this.a) / 100;\n        return this;\n    };\n    /**\n     * Returns whether the color is monochrome.\n     */ TinyColor.prototype.isMonochrome = function() {\n        var s = this.toHsl().s;\n        return s === 0;\n    };\n    /**\n     * Returns the object as a HSVA object.\n     */ TinyColor.prototype.toHsv = function() {\n        var hsv = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToHsv)(this.r, this.g, this.b);\n        return {\n            h: hsv.h * 360,\n            s: hsv.s,\n            v: hsv.v,\n            a: this.a\n        };\n    };\n    /**\n     * Returns the hsva values interpolated into a string with the following format:\n     * \"hsva(xxx, xxx, xxx, xx)\".\n     */ TinyColor.prototype.toHsvString = function() {\n        var hsv = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToHsv)(this.r, this.g, this.b);\n        var h = Math.round(hsv.h * 360);\n        var s = Math.round(hsv.s * 100);\n        var v = Math.round(hsv.v * 100);\n        return this.a === 1 ? \"hsv(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%)\") : \"hsva(\".concat(h, \", \").concat(s, \"%, \").concat(v, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a HSLA object.\n     */ TinyColor.prototype.toHsl = function() {\n        var hsl = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToHsl)(this.r, this.g, this.b);\n        return {\n            h: hsl.h * 360,\n            s: hsl.s,\n            l: hsl.l,\n            a: this.a\n        };\n    };\n    /**\n     * Returns the hsla values interpolated into a string with the following format:\n     * \"hsla(xxx, xxx, xxx, xx)\".\n     */ TinyColor.prototype.toHslString = function() {\n        var hsl = (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToHsl)(this.r, this.g, this.b);\n        var h = Math.round(hsl.h * 360);\n        var s = Math.round(hsl.s * 100);\n        var l = Math.round(hsl.l * 100);\n        return this.a === 1 ? \"hsl(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%)\") : \"hsla(\".concat(h, \", \").concat(s, \"%, \").concat(l, \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the hex value of the color.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */ TinyColor.prototype.toHex = function(allow3Char) {\n        if (allow3Char === void 0) {\n            allow3Char = false;\n        }\n        return (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToHex)(this.r, this.g, this.b, allow3Char);\n    };\n    /**\n     * Returns the hex value of the color -with a # prefixed.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */ TinyColor.prototype.toHexString = function(allow3Char) {\n        if (allow3Char === void 0) {\n            allow3Char = false;\n        }\n        return \"#\" + this.toHex(allow3Char);\n    };\n    /**\n     * Returns the hex 8 value of the color.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */ TinyColor.prototype.toHex8 = function(allow4Char) {\n        if (allow4Char === void 0) {\n            allow4Char = false;\n        }\n        return (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbaToHex)(this.r, this.g, this.b, this.a, allow4Char);\n    };\n    /**\n     * Returns the hex 8 value of the color -with a # prefixed.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */ TinyColor.prototype.toHex8String = function(allow4Char) {\n        if (allow4Char === void 0) {\n            allow4Char = false;\n        }\n        return \"#\" + this.toHex8(allow4Char);\n    };\n    /**\n     * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n     * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n     */ TinyColor.prototype.toHexShortString = function(allowShortChar) {\n        if (allowShortChar === void 0) {\n            allowShortChar = false;\n        }\n        return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */ TinyColor.prototype.toRgb = function() {\n        return {\n            r: Math.round(this.r),\n            g: Math.round(this.g),\n            b: Math.round(this.b),\n            a: this.a\n        };\n    };\n    /**\n     * Returns the RGBA values interpolated into a string with the following format:\n     * \"RGBA(xxx, xxx, xxx, xx)\".\n     */ TinyColor.prototype.toRgbString = function() {\n        var r = Math.round(this.r);\n        var g = Math.round(this.g);\n        var b = Math.round(this.b);\n        return this.a === 1 ? \"rgb(\".concat(r, \", \").concat(g, \", \").concat(b, \")\") : \"rgba(\".concat(r, \", \").concat(g, \", \").concat(b, \", \").concat(this.roundA, \")\");\n    };\n    /**\n     * Returns the object as a RGBA object.\n     */ TinyColor.prototype.toPercentageRgb = function() {\n        var fmt = function(x) {\n            return \"\".concat(Math.round((0,_util_js__WEBPACK_IMPORTED_MODULE_2__.bound01)(x, 255) * 100), \"%\");\n        };\n        return {\n            r: fmt(this.r),\n            g: fmt(this.g),\n            b: fmt(this.b),\n            a: this.a\n        };\n    };\n    /**\n     * Returns the RGBA relative values interpolated into a string\n     */ TinyColor.prototype.toPercentageRgbString = function() {\n        var rnd = function(x) {\n            return Math.round((0,_util_js__WEBPACK_IMPORTED_MODULE_2__.bound01)(x, 255) * 100);\n        };\n        return this.a === 1 ? \"rgb(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%)\") : \"rgba(\".concat(rnd(this.r), \"%, \").concat(rnd(this.g), \"%, \").concat(rnd(this.b), \"%, \").concat(this.roundA, \")\");\n    };\n    /**\n     * The 'real' name of the color -if there is one.\n     */ TinyColor.prototype.toName = function() {\n        if (this.a === 0) {\n            return \"transparent\";\n        }\n        if (this.a < 1) {\n            return false;\n        }\n        var hex = \"#\" + (0,_conversion_js__WEBPACK_IMPORTED_MODULE_0__.rgbToHex)(this.r, this.g, this.b, false);\n        for(var _i = 0, _a = Object.entries(_css_color_names_js__WEBPACK_IMPORTED_MODULE_3__.names); _i < _a.length; _i++){\n            var _b = _a[_i], key = _b[0], value = _b[1];\n            if (hex === value) {\n                return key;\n            }\n        }\n        return false;\n    };\n    TinyColor.prototype.toString = function(format) {\n        var formatSet = Boolean(format);\n        format = format !== null && format !== void 0 ? format : this.format;\n        var formattedString = false;\n        var hasAlpha = this.a < 1 && this.a >= 0;\n        var needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith(\"hex\") || format === \"name\");\n        if (needsAlphaFormat) {\n            // Special case for \"transparent\", all other non-alpha formats\n            // will return rgba when there is transparency.\n            if (format === \"name\" && this.a === 0) {\n                return this.toName();\n            }\n            return this.toRgbString();\n        }\n        if (format === \"rgb\") {\n            formattedString = this.toRgbString();\n        }\n        if (format === \"prgb\") {\n            formattedString = this.toPercentageRgbString();\n        }\n        if (format === \"hex\" || format === \"hex6\") {\n            formattedString = this.toHexString();\n        }\n        if (format === \"hex3\") {\n            formattedString = this.toHexString(true);\n        }\n        if (format === \"hex4\") {\n            formattedString = this.toHex8String(true);\n        }\n        if (format === \"hex8\") {\n            formattedString = this.toHex8String();\n        }\n        if (format === \"name\") {\n            formattedString = this.toName();\n        }\n        if (format === \"hsl\") {\n            formattedString = this.toHslString();\n        }\n        if (format === \"hsv\") {\n            formattedString = this.toHsvString();\n        }\n        return formattedString || this.toHexString();\n    };\n    TinyColor.prototype.toNumber = function() {\n        return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n    };\n    TinyColor.prototype.clone = function() {\n        return new TinyColor(this.toString());\n    };\n    /**\n     * Lighten the color a given amount. Providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */ TinyColor.prototype.lighten = function(amount) {\n        if (amount === void 0) {\n            amount = 10;\n        }\n        var hsl = this.toHsl();\n        hsl.l += amount / 100;\n        hsl.l = (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.clamp01)(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Brighten the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */ TinyColor.prototype.brighten = function(amount) {\n        if (amount === void 0) {\n            amount = 10;\n        }\n        var rgb = this.toRgb();\n        rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n        rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n        rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n        return new TinyColor(rgb);\n    };\n    /**\n     * Darken the color a given amount, from 0 to 100.\n     * Providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */ TinyColor.prototype.darken = function(amount) {\n        if (amount === void 0) {\n            amount = 10;\n        }\n        var hsl = this.toHsl();\n        hsl.l -= amount / 100;\n        hsl.l = (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.clamp01)(hsl.l);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the color with pure white, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */ TinyColor.prototype.tint = function(amount) {\n        if (amount === void 0) {\n            amount = 10;\n        }\n        return this.mix(\"white\", amount);\n    };\n    /**\n     * Mix the color with pure black, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */ TinyColor.prototype.shade = function(amount) {\n        if (amount === void 0) {\n            amount = 10;\n        }\n        return this.mix(\"black\", amount);\n    };\n    /**\n     * Desaturate the color a given amount, from 0 to 100.\n     * Providing 100 will is the same as calling greyscale\n     * @param amount - valid between 1-100\n     */ TinyColor.prototype.desaturate = function(amount) {\n        if (amount === void 0) {\n            amount = 10;\n        }\n        var hsl = this.toHsl();\n        hsl.s -= amount / 100;\n        hsl.s = (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.clamp01)(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Saturate the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */ TinyColor.prototype.saturate = function(amount) {\n        if (amount === void 0) {\n            amount = 10;\n        }\n        var hsl = this.toHsl();\n        hsl.s += amount / 100;\n        hsl.s = (0,_util_js__WEBPACK_IMPORTED_MODULE_2__.clamp01)(hsl.s);\n        return new TinyColor(hsl);\n    };\n    /**\n     * Completely desaturates a color into greyscale.\n     * Same as calling `desaturate(100)`\n     */ TinyColor.prototype.greyscale = function() {\n        return this.desaturate(100);\n    };\n    /**\n     * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n     * Values outside of this range will be wrapped into this range.\n     */ TinyColor.prototype.spin = function(amount) {\n        var hsl = this.toHsl();\n        var hue = (hsl.h + amount) % 360;\n        hsl.h = hue < 0 ? 360 + hue : hue;\n        return new TinyColor(hsl);\n    };\n    /**\n     * Mix the current color a given amount with another color, from 0 to 100.\n     * 0 means no mixing (return current color).\n     */ TinyColor.prototype.mix = function(color, amount) {\n        if (amount === void 0) {\n            amount = 50;\n        }\n        var rgb1 = this.toRgb();\n        var rgb2 = new TinyColor(color).toRgb();\n        var p = amount / 100;\n        var rgba = {\n            r: (rgb2.r - rgb1.r) * p + rgb1.r,\n            g: (rgb2.g - rgb1.g) * p + rgb1.g,\n            b: (rgb2.b - rgb1.b) * p + rgb1.b,\n            a: (rgb2.a - rgb1.a) * p + rgb1.a\n        };\n        return new TinyColor(rgba);\n    };\n    TinyColor.prototype.analogous = function(results, slices) {\n        if (results === void 0) {\n            results = 6;\n        }\n        if (slices === void 0) {\n            slices = 30;\n        }\n        var hsl = this.toHsl();\n        var part = 360 / slices;\n        var ret = [\n            this\n        ];\n        for(hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results;){\n            hsl.h = (hsl.h + part) % 360;\n            ret.push(new TinyColor(hsl));\n        }\n        return ret;\n    };\n    /**\n     * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n     */ TinyColor.prototype.complement = function() {\n        var hsl = this.toHsl();\n        hsl.h = (hsl.h + 180) % 360;\n        return new TinyColor(hsl);\n    };\n    TinyColor.prototype.monochromatic = function(results) {\n        if (results === void 0) {\n            results = 6;\n        }\n        var hsv = this.toHsv();\n        var h = hsv.h;\n        var s = hsv.s;\n        var v = hsv.v;\n        var res = [];\n        var modification = 1 / results;\n        while(results--){\n            res.push(new TinyColor({\n                h: h,\n                s: s,\n                v: v\n            }));\n            v = (v + modification) % 1;\n        }\n        return res;\n    };\n    TinyColor.prototype.splitcomplement = function() {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        return [\n            this,\n            new TinyColor({\n                h: (h + 72) % 360,\n                s: hsl.s,\n                l: hsl.l\n            }),\n            new TinyColor({\n                h: (h + 216) % 360,\n                s: hsl.s,\n                l: hsl.l\n            })\n        ];\n    };\n    /**\n     * Compute how the color would appear on a background\n     */ TinyColor.prototype.onBackground = function(background) {\n        var fg = this.toRgb();\n        var bg = new TinyColor(background).toRgb();\n        var alpha = fg.a + bg.a * (1 - fg.a);\n        return new TinyColor({\n            r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n            g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n            b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n            a: alpha\n        });\n    };\n    /**\n     * Alias for `polyad(3)`\n     */ TinyColor.prototype.triad = function() {\n        return this.polyad(3);\n    };\n    /**\n     * Alias for `polyad(4)`\n     */ TinyColor.prototype.tetrad = function() {\n        return this.polyad(4);\n    };\n    /**\n     * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n     * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n     */ TinyColor.prototype.polyad = function(n) {\n        var hsl = this.toHsl();\n        var h = hsl.h;\n        var result = [\n            this\n        ];\n        var increment = 360 / n;\n        for(var i = 1; i < n; i++){\n            result.push(new TinyColor({\n                h: (h + i * increment) % 360,\n                s: hsl.s,\n                l: hsl.l\n            }));\n        }\n        return result;\n    };\n    /**\n     * compare color vs current color\n     */ TinyColor.prototype.equals = function(color) {\n        return this.toRgbString() === new TinyColor(color).toRgbString();\n    };\n    return TinyColor;\n}();\n\n// kept for backwards compatability with v1\nfunction tinycolor(color, opts) {\n    if (color === void 0) {\n        color = \"\";\n    }\n    if (opts === void 0) {\n        opts = {};\n    }\n    return new TinyColor(color, opts);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ctrl/tinycolor/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@ctrl/tinycolor/dist/module/util.js":
/*!**********************************************************!*\
  !*** ./node_modules/@ctrl/tinycolor/dist/module/util.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bound01: () => (/* binding */ bound01),\n/* harmony export */   boundAlpha: () => (/* binding */ boundAlpha),\n/* harmony export */   clamp01: () => (/* binding */ clamp01),\n/* harmony export */   convertToPercentage: () => (/* binding */ convertToPercentage),\n/* harmony export */   isOnePointZero: () => (/* binding */ isOnePointZero),\n/* harmony export */   isPercentage: () => (/* binding */ isPercentage),\n/* harmony export */   pad2: () => (/* binding */ pad2)\n/* harmony export */ });\n/**\n * Take input from [0, n] and return it as [0, 1]\n * @hidden\n */ function bound01(n, max) {\n    if (isOnePointZero(n)) {\n        n = \"100%\";\n    }\n    var isPercent = isPercentage(n);\n    n = max === 360 ? n : Math.min(max, Math.max(0, parseFloat(n)));\n    // Automatically convert percentage into number\n    if (isPercent) {\n        n = parseInt(String(n * max), 10) / 100;\n    }\n    // Handle floating point rounding errors\n    if (Math.abs(n - max) < 0.000001) {\n        return 1;\n    }\n    // Convert into [0, 1] range if it isn't already\n    if (max === 360) {\n        // If n is a hue given in degrees,\n        // wrap around out-of-range values into [0, 360] range\n        // then convert into [0, 1].\n        n = (n < 0 ? n % max + max : n % max) / parseFloat(String(max));\n    } else {\n        // If n not a hue given in degrees\n        // Convert into [0, 1] range if it isn't already.\n        n = n % max / parseFloat(String(max));\n    }\n    return n;\n}\n/**\n * Force a number between 0 and 1\n * @hidden\n */ function clamp01(val) {\n    return Math.min(1, Math.max(0, val));\n}\n/**\n * Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n * <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\n * @hidden\n */ function isOnePointZero(n) {\n    return typeof n === \"string\" && n.indexOf(\".\") !== -1 && parseFloat(n) === 1;\n}\n/**\n * Check to see if string passed in is a percentage\n * @hidden\n */ function isPercentage(n) {\n    return typeof n === \"string\" && n.indexOf(\"%\") !== -1;\n}\n/**\n * Return a valid alpha value [0,1] with all invalid values being set to 1\n * @hidden\n */ function boundAlpha(a) {\n    a = parseFloat(a);\n    if (isNaN(a) || a < 0 || a > 1) {\n        a = 1;\n    }\n    return a;\n}\n/**\n * Replace a decimal with it's percentage value\n * @hidden\n */ function convertToPercentage(n) {\n    if (n <= 1) {\n        return \"\".concat(Number(n) * 100, \"%\");\n    }\n    return n;\n}\n/**\n * Force a hex value to have 2 characters\n * @hidden\n */ function pad2(c) {\n    return c.length === 1 ? \"0\" + c : String(c);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@ctrl/tinycolor/dist/module/util.js\n");

/***/ })

};
;