"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/immer";
exports.ids = ["vendor-chunks/immer"];
exports.modules = {

/***/ "(ssr)/./node_modules/immer/dist/immer.mjs":
/*!*******************************************!*\
  !*** ./node_modules/immer/dist/immer.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Immer: () => (/* binding */ Immer2),\n/* harmony export */   applyPatches: () => (/* binding */ applyPatches),\n/* harmony export */   castDraft: () => (/* binding */ castDraft),\n/* harmony export */   castImmutable: () => (/* binding */ castImmutable),\n/* harmony export */   createDraft: () => (/* binding */ createDraft),\n/* harmony export */   current: () => (/* binding */ current),\n/* harmony export */   enableMapSet: () => (/* binding */ enableMapSet),\n/* harmony export */   enablePatches: () => (/* binding */ enablePatches),\n/* harmony export */   finishDraft: () => (/* binding */ finishDraft),\n/* harmony export */   freeze: () => (/* binding */ freeze),\n/* harmony export */   immerable: () => (/* binding */ DRAFTABLE),\n/* harmony export */   isDraft: () => (/* binding */ isDraft),\n/* harmony export */   isDraftable: () => (/* binding */ isDraftable),\n/* harmony export */   nothing: () => (/* binding */ NOTHING),\n/* harmony export */   original: () => (/* binding */ original),\n/* harmony export */   produce: () => (/* binding */ produce),\n/* harmony export */   produceWithPatches: () => (/* binding */ produceWithPatches),\n/* harmony export */   setAutoFreeze: () => (/* binding */ setAutoFreeze),\n/* harmony export */   setUseStrictShallowCopy: () => (/* binding */ setUseStrictShallowCopy)\n/* harmony export */ });\n// src/utils/env.ts\nvar NOTHING = Symbol.for(\"immer-nothing\");\nvar DRAFTABLE = Symbol.for(\"immer-draftable\");\nvar DRAFT_STATE = Symbol.for(\"immer-state\");\n// src/utils/errors.ts\nvar errors =  true ? [\n    // All error codes, starting by 0:\n    function(plugin) {\n        return `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`;\n    },\n    function(thing) {\n        return `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`;\n    },\n    \"This object has been frozen and should not be mutated\",\n    function(data) {\n        return \"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" + data;\n    },\n    \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n    \"Immer forbids circular references\",\n    \"The first or second argument to `produce` must be a function\",\n    \"The third argument to `produce` must be a function or undefined\",\n    \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n    \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n    function(thing) {\n        return `'current' expects a draft, got: ${thing}`;\n    },\n    \"Object.defineProperty() cannot be used on an Immer draft\",\n    \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n    \"Immer only supports deleting array indices\",\n    \"Immer only supports setting array indices and the 'length' property\",\n    function(thing) {\n        return `'original' expects a draft, got: ${thing}`;\n    }\n] : 0;\nfunction die(error, ...args) {\n    if (true) {\n        const e = errors[error];\n        const msg = typeof e === \"function\" ? e.apply(null, args) : e;\n        throw new Error(`[Immer] ${msg}`);\n    }\n    throw new Error(`[Immer] minified error nr: ${error}. Full error at: https://bit.ly/3cXEKWf`);\n}\n// src/utils/common.ts\nvar getPrototypeOf = Object.getPrototypeOf;\nfunction isDraft(value) {\n    return !!value && !!value[DRAFT_STATE];\n}\nfunction isDraftable(value) {\n    if (!value) return false;\n    return isPlainObject(value) || Array.isArray(value) || !!value[DRAFTABLE] || !!value.constructor?.[DRAFTABLE] || isMap(value) || isSet(value);\n}\nvar objectCtorString = Object.prototype.constructor.toString();\nfunction isPlainObject(value) {\n    if (!value || typeof value !== \"object\") return false;\n    const proto = getPrototypeOf(value);\n    if (proto === null) {\n        return true;\n    }\n    const Ctor = Object.hasOwnProperty.call(proto, \"constructor\") && proto.constructor;\n    if (Ctor === Object) return true;\n    return typeof Ctor == \"function\" && Function.toString.call(Ctor) === objectCtorString;\n}\nfunction original(value) {\n    if (!isDraft(value)) die(15, value);\n    return value[DRAFT_STATE].base_;\n}\nfunction each(obj, iter) {\n    if (getArchtype(obj) === 0 /* Object */ ) {\n        Object.entries(obj).forEach(([key, value])=>{\n            iter(key, value, obj);\n        });\n    } else {\n        obj.forEach((entry, index)=>iter(index, entry, obj));\n    }\n}\nfunction getArchtype(thing) {\n    const state = thing[DRAFT_STATE];\n    return state ? state.type_ : Array.isArray(thing) ? 1 /* Array */  : isMap(thing) ? 2 /* Map */  : isSet(thing) ? 3 /* Set */  : 0 /* Object */ ;\n}\nfunction has(thing, prop) {\n    return getArchtype(thing) === 2 /* Map */  ? thing.has(prop) : Object.prototype.hasOwnProperty.call(thing, prop);\n}\nfunction get(thing, prop) {\n    return getArchtype(thing) === 2 /* Map */  ? thing.get(prop) : thing[prop];\n}\nfunction set(thing, propOrOldValue, value) {\n    const t = getArchtype(thing);\n    if (t === 2 /* Map */ ) thing.set(propOrOldValue, value);\n    else if (t === 3 /* Set */ ) {\n        thing.add(value);\n    } else thing[propOrOldValue] = value;\n}\nfunction is(x, y) {\n    if (x === y) {\n        return x !== 0 || 1 / x === 1 / y;\n    } else {\n        return x !== x && y !== y;\n    }\n}\nfunction isMap(target) {\n    return target instanceof Map;\n}\nfunction isSet(target) {\n    return target instanceof Set;\n}\nfunction latest(state) {\n    return state.copy_ || state.base_;\n}\nfunction shallowCopy(base, strict) {\n    if (isMap(base)) {\n        return new Map(base);\n    }\n    if (isSet(base)) {\n        return new Set(base);\n    }\n    if (Array.isArray(base)) return Array.prototype.slice.call(base);\n    if (!strict && isPlainObject(base)) {\n        if (!getPrototypeOf(base)) {\n            const obj = /* @__PURE__ */ Object.create(null);\n            return Object.assign(obj, base);\n        }\n        return {\n            ...base\n        };\n    }\n    const descriptors = Object.getOwnPropertyDescriptors(base);\n    delete descriptors[DRAFT_STATE];\n    let keys = Reflect.ownKeys(descriptors);\n    for(let i = 0; i < keys.length; i++){\n        const key = keys[i];\n        const desc = descriptors[key];\n        if (desc.writable === false) {\n            desc.writable = true;\n            desc.configurable = true;\n        }\n        if (desc.get || desc.set) descriptors[key] = {\n            configurable: true,\n            writable: true,\n            // could live with !!desc.set as well here...\n            enumerable: desc.enumerable,\n            value: base[key]\n        };\n    }\n    return Object.create(getPrototypeOf(base), descriptors);\n}\nfunction freeze(obj, deep = false) {\n    if (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj;\n    if (getArchtype(obj) > 1) {\n        obj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections;\n    }\n    Object.freeze(obj);\n    if (deep) each(obj, (_key, value)=>freeze(value, true), true);\n    return obj;\n}\nfunction dontMutateFrozenCollections() {\n    die(2);\n}\nfunction isFrozen(obj) {\n    return Object.isFrozen(obj);\n}\n// src/utils/plugins.ts\nvar plugins = {};\nfunction getPlugin(pluginKey) {\n    const plugin = plugins[pluginKey];\n    if (!plugin) {\n        die(0, pluginKey);\n    }\n    return plugin;\n}\nfunction loadPlugin(pluginKey, implementation) {\n    if (!plugins[pluginKey]) plugins[pluginKey] = implementation;\n}\n// src/core/scope.ts\nvar currentScope;\nfunction getCurrentScope() {\n    return currentScope;\n}\nfunction createScope(parent_, immer_) {\n    return {\n        drafts_: [],\n        parent_,\n        immer_,\n        // Whenever the modified draft contains a draft from another scope, we\n        // need to prevent auto-freezing so the unowned draft can be finalized.\n        canAutoFreeze_: true,\n        unfinalizedDrafts_: 0\n    };\n}\nfunction usePatchesInScope(scope, patchListener) {\n    if (patchListener) {\n        getPlugin(\"Patches\");\n        scope.patches_ = [];\n        scope.inversePatches_ = [];\n        scope.patchListener_ = patchListener;\n    }\n}\nfunction revokeScope(scope) {\n    leaveScope(scope);\n    scope.drafts_.forEach(revokeDraft);\n    scope.drafts_ = null;\n}\nfunction leaveScope(scope) {\n    if (scope === currentScope) {\n        currentScope = scope.parent_;\n    }\n}\nfunction enterScope(immer2) {\n    return currentScope = createScope(currentScope, immer2);\n}\nfunction revokeDraft(draft) {\n    const state = draft[DRAFT_STATE];\n    if (state.type_ === 0 /* Object */  || state.type_ === 1 /* Array */ ) state.revoke_();\n    else state.revoked_ = true;\n}\n// src/core/finalize.ts\nfunction processResult(result, scope) {\n    scope.unfinalizedDrafts_ = scope.drafts_.length;\n    const baseDraft = scope.drafts_[0];\n    const isReplaced = result !== void 0 && result !== baseDraft;\n    if (isReplaced) {\n        if (baseDraft[DRAFT_STATE].modified_) {\n            revokeScope(scope);\n            die(4);\n        }\n        if (isDraftable(result)) {\n            result = finalize(scope, result);\n            if (!scope.parent_) maybeFreeze(scope, result);\n        }\n        if (scope.patches_) {\n            getPlugin(\"Patches\").generateReplacementPatches_(baseDraft[DRAFT_STATE].base_, result, scope.patches_, scope.inversePatches_);\n        }\n    } else {\n        result = finalize(scope, baseDraft, []);\n    }\n    revokeScope(scope);\n    if (scope.patches_) {\n        scope.patchListener_(scope.patches_, scope.inversePatches_);\n    }\n    return result !== NOTHING ? result : void 0;\n}\nfunction finalize(rootScope, value, path) {\n    if (isFrozen(value)) return value;\n    const state = value[DRAFT_STATE];\n    if (!state) {\n        each(value, (key, childValue)=>finalizeProperty(rootScope, state, value, key, childValue, path), true);\n        return value;\n    }\n    if (state.scope_ !== rootScope) return value;\n    if (!state.modified_) {\n        maybeFreeze(rootScope, state.base_, true);\n        return state.base_;\n    }\n    if (!state.finalized_) {\n        state.finalized_ = true;\n        state.scope_.unfinalizedDrafts_--;\n        const result = state.copy_;\n        let resultEach = result;\n        let isSet2 = false;\n        if (state.type_ === 3 /* Set */ ) {\n            resultEach = new Set(result);\n            result.clear();\n            isSet2 = true;\n        }\n        each(resultEach, (key, childValue)=>finalizeProperty(rootScope, state, result, key, childValue, path, isSet2));\n        maybeFreeze(rootScope, result, false);\n        if (path && rootScope.patches_) {\n            getPlugin(\"Patches\").generatePatches_(state, path, rootScope.patches_, rootScope.inversePatches_);\n        }\n    }\n    return state.copy_;\n}\nfunction finalizeProperty(rootScope, parentState, targetObject, prop, childValue, rootPath, targetIsSet) {\n    if ( true && childValue === targetObject) die(5);\n    if (isDraft(childValue)) {\n        const path = rootPath && parentState && parentState.type_ !== 3 /* Set */  && // Set objects are atomic since they have no keys.\n        !has(parentState.assigned_, prop) ? rootPath.concat(prop) : void 0;\n        const res = finalize(rootScope, childValue, path);\n        set(targetObject, prop, res);\n        if (isDraft(res)) {\n            rootScope.canAutoFreeze_ = false;\n        } else return;\n    } else if (targetIsSet) {\n        targetObject.add(childValue);\n    }\n    if (isDraftable(childValue) && !isFrozen(childValue)) {\n        if (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n            return;\n        }\n        finalize(rootScope, childValue);\n        if (!parentState || !parentState.scope_.parent_) maybeFreeze(rootScope, childValue);\n    }\n}\nfunction maybeFreeze(scope, value, deep = false) {\n    if (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n        freeze(value, deep);\n    }\n}\n// src/core/proxy.ts\nfunction createProxyProxy(base, parent) {\n    const isArray = Array.isArray(base);\n    const state = {\n        type_: isArray ? 1 /* Array */  : 0 /* Object */ ,\n        // Track which produce call this is associated with.\n        scope_: parent ? parent.scope_ : getCurrentScope(),\n        // True for both shallow and deep changes.\n        modified_: false,\n        // Used during finalization.\n        finalized_: false,\n        // Track which properties have been assigned (true) or deleted (false).\n        assigned_: {},\n        // The parent draft state.\n        parent_: parent,\n        // The base state.\n        base_: base,\n        // The base proxy.\n        draft_: null,\n        // set below\n        // The base copy with any updated values.\n        copy_: null,\n        // Called by the `produce` function.\n        revoke_: null,\n        isManual_: false\n    };\n    let target = state;\n    let traps = objectTraps;\n    if (isArray) {\n        target = [\n            state\n        ];\n        traps = arrayTraps;\n    }\n    const { revoke, proxy } = Proxy.revocable(target, traps);\n    state.draft_ = proxy;\n    state.revoke_ = revoke;\n    return proxy;\n}\nvar objectTraps = {\n    get (state, prop) {\n        if (prop === DRAFT_STATE) return state;\n        const source = latest(state);\n        if (!has(source, prop)) {\n            return readPropFromProto(state, source, prop);\n        }\n        const value = source[prop];\n        if (state.finalized_ || !isDraftable(value)) {\n            return value;\n        }\n        if (value === peek(state.base_, prop)) {\n            prepareCopy(state);\n            return state.copy_[prop] = createProxy(value, state);\n        }\n        return value;\n    },\n    has (state, prop) {\n        return prop in latest(state);\n    },\n    ownKeys (state) {\n        return Reflect.ownKeys(latest(state));\n    },\n    set (state, prop, value) {\n        const desc = getDescriptorFromProto(latest(state), prop);\n        if (desc?.set) {\n            desc.set.call(state.draft_, value);\n            return true;\n        }\n        if (!state.modified_) {\n            const current2 = peek(latest(state), prop);\n            const currentState = current2?.[DRAFT_STATE];\n            if (currentState && currentState.base_ === value) {\n                state.copy_[prop] = value;\n                state.assigned_[prop] = false;\n                return true;\n            }\n            if (is(value, current2) && (value !== void 0 || has(state.base_, prop))) return true;\n            prepareCopy(state);\n            markChanged(state);\n        }\n        if (state.copy_[prop] === value && // special case: handle new props with value 'undefined'\n        (value !== void 0 || prop in state.copy_) || // special case: NaN\n        Number.isNaN(value) && Number.isNaN(state.copy_[prop])) return true;\n        state.copy_[prop] = value;\n        state.assigned_[prop] = true;\n        return true;\n    },\n    deleteProperty (state, prop) {\n        if (peek(state.base_, prop) !== void 0 || prop in state.base_) {\n            state.assigned_[prop] = false;\n            prepareCopy(state);\n            markChanged(state);\n        } else {\n            delete state.assigned_[prop];\n        }\n        if (state.copy_) {\n            delete state.copy_[prop];\n        }\n        return true;\n    },\n    // Note: We never coerce `desc.value` into an Immer draft, because we can't make\n    // the same guarantee in ES5 mode.\n    getOwnPropertyDescriptor (state, prop) {\n        const owner = latest(state);\n        const desc = Reflect.getOwnPropertyDescriptor(owner, prop);\n        if (!desc) return desc;\n        return {\n            writable: true,\n            configurable: state.type_ !== 1 /* Array */  || prop !== \"length\",\n            enumerable: desc.enumerable,\n            value: owner[prop]\n        };\n    },\n    defineProperty () {\n        die(11);\n    },\n    getPrototypeOf (state) {\n        return getPrototypeOf(state.base_);\n    },\n    setPrototypeOf () {\n        die(12);\n    }\n};\nvar arrayTraps = {};\neach(objectTraps, (key, fn)=>{\n    arrayTraps[key] = function() {\n        arguments[0] = arguments[0][0];\n        return fn.apply(this, arguments);\n    };\n});\narrayTraps.deleteProperty = function(state, prop) {\n    if ( true && isNaN(parseInt(prop))) die(13);\n    return arrayTraps.set.call(this, state, prop, void 0);\n};\narrayTraps.set = function(state, prop, value) {\n    if ( true && prop !== \"length\" && isNaN(parseInt(prop))) die(14);\n    return objectTraps.set.call(this, state[0], prop, value, state[0]);\n};\nfunction peek(draft, prop) {\n    const state = draft[DRAFT_STATE];\n    const source = state ? latest(state) : draft;\n    return source[prop];\n}\nfunction readPropFromProto(state, source, prop) {\n    const desc = getDescriptorFromProto(source, prop);\n    return desc ? `value` in desc ? desc.value : // This is a very special case, if the prop is a getter defined by the\n    // prototype, we should invoke it with the draft as context!\n    desc.get?.call(state.draft_) : void 0;\n}\nfunction getDescriptorFromProto(source, prop) {\n    if (!(prop in source)) return void 0;\n    let proto = getPrototypeOf(source);\n    while(proto){\n        const desc = Object.getOwnPropertyDescriptor(proto, prop);\n        if (desc) return desc;\n        proto = getPrototypeOf(proto);\n    }\n    return void 0;\n}\nfunction markChanged(state) {\n    if (!state.modified_) {\n        state.modified_ = true;\n        if (state.parent_) {\n            markChanged(state.parent_);\n        }\n    }\n}\nfunction prepareCopy(state) {\n    if (!state.copy_) {\n        state.copy_ = shallowCopy(state.base_, state.scope_.immer_.useStrictShallowCopy_);\n    }\n}\n// src/core/immerClass.ts\nvar Immer2 = class {\n    constructor(config){\n        this.autoFreeze_ = true;\n        this.useStrictShallowCopy_ = false;\n        /**\n     * The `produce` function takes a value and a \"recipe function\" (whose\n     * return value often depends on the base state). The recipe function is\n     * free to mutate its first argument however it wants. All mutations are\n     * only ever applied to a __copy__ of the base state.\n     *\n     * Pass only a function to create a \"curried producer\" which relieves you\n     * from passing the recipe function every time.\n     *\n     * Only plain objects and arrays are made mutable. All other objects are\n     * considered uncopyable.\n     *\n     * Note: This function is __bound__ to its `Immer` instance.\n     *\n     * @param {any} base - the initial state\n     * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n     * @param {Function} patchListener - optional function that will be called with all the patches produced here\n     * @returns {any} a new state, or the initial state if nothing was modified\n     */ this.produce = (base, recipe, patchListener)=>{\n            if (typeof base === \"function\" && typeof recipe !== \"function\") {\n                const defaultBase = recipe;\n                recipe = base;\n                const self = this;\n                return function curriedProduce(base2 = defaultBase, ...args) {\n                    return self.produce(base2, (draft)=>recipe.call(this, draft, ...args));\n                };\n            }\n            if (typeof recipe !== \"function\") die(6);\n            if (patchListener !== void 0 && typeof patchListener !== \"function\") die(7);\n            let result;\n            if (isDraftable(base)) {\n                const scope = enterScope(this);\n                const proxy = createProxy(base, void 0);\n                let hasError = true;\n                try {\n                    result = recipe(proxy);\n                    hasError = false;\n                } finally{\n                    if (hasError) revokeScope(scope);\n                    else leaveScope(scope);\n                }\n                usePatchesInScope(scope, patchListener);\n                return processResult(result, scope);\n            } else if (!base || typeof base !== \"object\") {\n                result = recipe(base);\n                if (result === void 0) result = base;\n                if (result === NOTHING) result = void 0;\n                if (this.autoFreeze_) freeze(result, true);\n                if (patchListener) {\n                    const p = [];\n                    const ip = [];\n                    getPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip);\n                    patchListener(p, ip);\n                }\n                return result;\n            } else die(1, base);\n        };\n        this.produceWithPatches = (base, recipe)=>{\n            if (typeof base === \"function\") {\n                return (state, ...args)=>this.produceWithPatches(state, (draft)=>base(draft, ...args));\n            }\n            let patches, inversePatches;\n            const result = this.produce(base, recipe, (p, ip)=>{\n                patches = p;\n                inversePatches = ip;\n            });\n            return [\n                result,\n                patches,\n                inversePatches\n            ];\n        };\n        if (typeof config?.autoFreeze === \"boolean\") this.setAutoFreeze(config.autoFreeze);\n        if (typeof config?.useStrictShallowCopy === \"boolean\") this.setUseStrictShallowCopy(config.useStrictShallowCopy);\n    }\n    createDraft(base) {\n        if (!isDraftable(base)) die(8);\n        if (isDraft(base)) base = current(base);\n        const scope = enterScope(this);\n        const proxy = createProxy(base, void 0);\n        proxy[DRAFT_STATE].isManual_ = true;\n        leaveScope(scope);\n        return proxy;\n    }\n    finishDraft(draft, patchListener) {\n        const state = draft && draft[DRAFT_STATE];\n        if (!state || !state.isManual_) die(9);\n        const { scope_: scope } = state;\n        usePatchesInScope(scope, patchListener);\n        return processResult(void 0, scope);\n    }\n    /**\n   * Pass true to automatically freeze all copies created by Immer.\n   *\n   * By default, auto-freezing is enabled.\n   */ setAutoFreeze(value) {\n        this.autoFreeze_ = value;\n    }\n    /**\n   * Pass true to enable strict shallow copy.\n   *\n   * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n   */ setUseStrictShallowCopy(value) {\n        this.useStrictShallowCopy_ = value;\n    }\n    applyPatches(base, patches) {\n        let i;\n        for(i = patches.length - 1; i >= 0; i--){\n            const patch = patches[i];\n            if (patch.path.length === 0 && patch.op === \"replace\") {\n                base = patch.value;\n                break;\n            }\n        }\n        if (i > -1) {\n            patches = patches.slice(i + 1);\n        }\n        const applyPatchesImpl = getPlugin(\"Patches\").applyPatches_;\n        if (isDraft(base)) {\n            return applyPatchesImpl(base, patches);\n        }\n        return this.produce(base, (draft)=>applyPatchesImpl(draft, patches));\n    }\n};\nfunction createProxy(value, parent) {\n    const draft = isMap(value) ? getPlugin(\"MapSet\").proxyMap_(value, parent) : isSet(value) ? getPlugin(\"MapSet\").proxySet_(value, parent) : createProxyProxy(value, parent);\n    const scope = parent ? parent.scope_ : getCurrentScope();\n    scope.drafts_.push(draft);\n    return draft;\n}\n// src/core/current.ts\nfunction current(value) {\n    if (!isDraft(value)) die(10, value);\n    return currentImpl(value);\n}\nfunction currentImpl(value) {\n    if (!isDraftable(value) || isFrozen(value)) return value;\n    const state = value[DRAFT_STATE];\n    let copy;\n    if (state) {\n        if (!state.modified_) return state.base_;\n        state.finalized_ = true;\n        copy = shallowCopy(value, state.scope_.immer_.useStrictShallowCopy_);\n    } else {\n        copy = shallowCopy(value, true);\n    }\n    each(copy, (key, childValue)=>{\n        set(copy, key, currentImpl(childValue));\n    });\n    if (state) {\n        state.finalized_ = false;\n    }\n    return copy;\n}\n// src/plugins/patches.ts\nfunction enablePatches() {\n    const errorOffset = 16;\n    if (true) {\n        errors.push('Sets cannot have \"replace\" patches.', function(op) {\n            return \"Unsupported patch operation: \" + op;\n        }, function(path) {\n            return \"Cannot apply patch, path doesn't resolve: \" + path;\n        }, \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\");\n    }\n    const REPLACE = \"replace\";\n    const ADD = \"add\";\n    const REMOVE = \"remove\";\n    function generatePatches_(state, basePath, patches, inversePatches) {\n        switch(state.type_){\n            case 0 /* Object */ :\n            case 2 /* Map */ :\n                return generatePatchesFromAssigned(state, basePath, patches, inversePatches);\n            case 1 /* Array */ :\n                return generateArrayPatches(state, basePath, patches, inversePatches);\n            case 3 /* Set */ :\n                return generateSetPatches(state, basePath, patches, inversePatches);\n        }\n    }\n    function generateArrayPatches(state, basePath, patches, inversePatches) {\n        let { base_, assigned_ } = state;\n        let copy_ = state.copy_;\n        if (copy_.length < base_.length) {\n            ;\n            [base_, copy_] = [\n                copy_,\n                base_\n            ];\n            [patches, inversePatches] = [\n                inversePatches,\n                patches\n            ];\n        }\n        for(let i = 0; i < base_.length; i++){\n            if (assigned_[i] && copy_[i] !== base_[i]) {\n                const path = basePath.concat([\n                    i\n                ]);\n                patches.push({\n                    op: REPLACE,\n                    path,\n                    // Need to maybe clone it, as it can in fact be the original value\n                    // due to the base/copy inversion at the start of this function\n                    value: clonePatchValueIfNeeded(copy_[i])\n                });\n                inversePatches.push({\n                    op: REPLACE,\n                    path,\n                    value: clonePatchValueIfNeeded(base_[i])\n                });\n            }\n        }\n        for(let i = base_.length; i < copy_.length; i++){\n            const path = basePath.concat([\n                i\n            ]);\n            patches.push({\n                op: ADD,\n                path,\n                // Need to maybe clone it, as it can in fact be the original value\n                // due to the base/copy inversion at the start of this function\n                value: clonePatchValueIfNeeded(copy_[i])\n            });\n        }\n        for(let i = copy_.length - 1; base_.length <= i; --i){\n            const path = basePath.concat([\n                i\n            ]);\n            inversePatches.push({\n                op: REMOVE,\n                path\n            });\n        }\n    }\n    function generatePatchesFromAssigned(state, basePath, patches, inversePatches) {\n        const { base_, copy_ } = state;\n        each(state.assigned_, (key, assignedValue)=>{\n            const origValue = get(base_, key);\n            const value = get(copy_, key);\n            const op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD;\n            if (origValue === value && op === REPLACE) return;\n            const path = basePath.concat(key);\n            patches.push(op === REMOVE ? {\n                op,\n                path\n            } : {\n                op,\n                path,\n                value\n            });\n            inversePatches.push(op === ADD ? {\n                op: REMOVE,\n                path\n            } : op === REMOVE ? {\n                op: ADD,\n                path,\n                value: clonePatchValueIfNeeded(origValue)\n            } : {\n                op: REPLACE,\n                path,\n                value: clonePatchValueIfNeeded(origValue)\n            });\n        });\n    }\n    function generateSetPatches(state, basePath, patches, inversePatches) {\n        let { base_, copy_ } = state;\n        let i = 0;\n        base_.forEach((value)=>{\n            if (!copy_.has(value)) {\n                const path = basePath.concat([\n                    i\n                ]);\n                patches.push({\n                    op: REMOVE,\n                    path,\n                    value\n                });\n                inversePatches.unshift({\n                    op: ADD,\n                    path,\n                    value\n                });\n            }\n            i++;\n        });\n        i = 0;\n        copy_.forEach((value)=>{\n            if (!base_.has(value)) {\n                const path = basePath.concat([\n                    i\n                ]);\n                patches.push({\n                    op: ADD,\n                    path,\n                    value\n                });\n                inversePatches.unshift({\n                    op: REMOVE,\n                    path,\n                    value\n                });\n            }\n            i++;\n        });\n    }\n    function generateReplacementPatches_(baseValue, replacement, patches, inversePatches) {\n        patches.push({\n            op: REPLACE,\n            path: [],\n            value: replacement === NOTHING ? void 0 : replacement\n        });\n        inversePatches.push({\n            op: REPLACE,\n            path: [],\n            value: baseValue\n        });\n    }\n    function applyPatches_(draft, patches) {\n        patches.forEach((patch)=>{\n            const { path, op } = patch;\n            let base = draft;\n            for(let i = 0; i < path.length - 1; i++){\n                const parentType = getArchtype(base);\n                let p = path[i];\n                if (typeof p !== \"string\" && typeof p !== \"number\") {\n                    p = \"\" + p;\n                }\n                if ((parentType === 0 /* Object */  || parentType === 1 /* Array */ ) && (p === \"__proto__\" || p === \"constructor\")) die(errorOffset + 3);\n                if (typeof base === \"function\" && p === \"prototype\") die(errorOffset + 3);\n                base = get(base, p);\n                if (typeof base !== \"object\") die(errorOffset + 2, path.join(\"/\"));\n            }\n            const type = getArchtype(base);\n            const value = deepClonePatchValue(patch.value);\n            const key = path[path.length - 1];\n            switch(op){\n                case REPLACE:\n                    switch(type){\n                        case 2 /* Map */ :\n                            return base.set(key, value);\n                        case 3 /* Set */ :\n                            die(errorOffset);\n                        default:\n                            return base[key] = value;\n                    }\n                case ADD:\n                    switch(type){\n                        case 1 /* Array */ :\n                            return key === \"-\" ? base.push(value) : base.splice(key, 0, value);\n                        case 2 /* Map */ :\n                            return base.set(key, value);\n                        case 3 /* Set */ :\n                            return base.add(value);\n                        default:\n                            return base[key] = value;\n                    }\n                case REMOVE:\n                    switch(type){\n                        case 1 /* Array */ :\n                            return base.splice(key, 1);\n                        case 2 /* Map */ :\n                            return base.delete(key);\n                        case 3 /* Set */ :\n                            return base.delete(patch.value);\n                        default:\n                            return delete base[key];\n                    }\n                default:\n                    die(errorOffset + 1, op);\n            }\n        });\n        return draft;\n    }\n    function deepClonePatchValue(obj) {\n        if (!isDraftable(obj)) return obj;\n        if (Array.isArray(obj)) return obj.map(deepClonePatchValue);\n        if (isMap(obj)) return new Map(Array.from(obj.entries()).map(([k, v])=>[\n                k,\n                deepClonePatchValue(v)\n            ]));\n        if (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue));\n        const cloned = Object.create(getPrototypeOf(obj));\n        for(const key in obj)cloned[key] = deepClonePatchValue(obj[key]);\n        if (has(obj, DRAFTABLE)) cloned[DRAFTABLE] = obj[DRAFTABLE];\n        return cloned;\n    }\n    function clonePatchValueIfNeeded(obj) {\n        if (isDraft(obj)) {\n            return deepClonePatchValue(obj);\n        } else return obj;\n    }\n    loadPlugin(\"Patches\", {\n        applyPatches_,\n        generatePatches_,\n        generateReplacementPatches_\n    });\n}\n// src/plugins/mapset.ts\nfunction enableMapSet() {\n    class DraftMap extends Map {\n        constructor(target, parent){\n            super();\n            this[DRAFT_STATE] = {\n                type_: 2 /* Map */ ,\n                parent_: parent,\n                scope_: parent ? parent.scope_ : getCurrentScope(),\n                modified_: false,\n                finalized_: false,\n                copy_: void 0,\n                assigned_: void 0,\n                base_: target,\n                draft_: this,\n                isManual_: false,\n                revoked_: false\n            };\n        }\n        get size() {\n            return latest(this[DRAFT_STATE]).size;\n        }\n        has(key) {\n            return latest(this[DRAFT_STATE]).has(key);\n        }\n        set(key, value) {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            if (!latest(state).has(key) || latest(state).get(key) !== value) {\n                prepareMapCopy(state);\n                markChanged(state);\n                state.assigned_.set(key, true);\n                state.copy_.set(key, value);\n                state.assigned_.set(key, true);\n            }\n            return this;\n        }\n        delete(key) {\n            if (!this.has(key)) {\n                return false;\n            }\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            prepareMapCopy(state);\n            markChanged(state);\n            if (state.base_.has(key)) {\n                state.assigned_.set(key, false);\n            } else {\n                state.assigned_.delete(key);\n            }\n            state.copy_.delete(key);\n            return true;\n        }\n        clear() {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            if (latest(state).size) {\n                prepareMapCopy(state);\n                markChanged(state);\n                state.assigned_ = /* @__PURE__ */ new Map();\n                each(state.base_, (key)=>{\n                    state.assigned_.set(key, false);\n                });\n                state.copy_.clear();\n            }\n        }\n        forEach(cb, thisArg) {\n            const state = this[DRAFT_STATE];\n            latest(state).forEach((_value, key, _map)=>{\n                cb.call(thisArg, this.get(key), key, this);\n            });\n        }\n        get(key) {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            const value = latest(state).get(key);\n            if (state.finalized_ || !isDraftable(value)) {\n                return value;\n            }\n            if (value !== state.base_.get(key)) {\n                return value;\n            }\n            const draft = createProxy(value, state);\n            prepareMapCopy(state);\n            state.copy_.set(key, draft);\n            return draft;\n        }\n        keys() {\n            return latest(this[DRAFT_STATE]).keys();\n        }\n        values() {\n            const iterator = this.keys();\n            return {\n                [Symbol.iterator]: ()=>this.values(),\n                next: ()=>{\n                    const r = iterator.next();\n                    if (r.done) return r;\n                    const value = this.get(r.value);\n                    return {\n                        done: false,\n                        value\n                    };\n                }\n            };\n        }\n        entries() {\n            const iterator = this.keys();\n            return {\n                [Symbol.iterator]: ()=>this.entries(),\n                next: ()=>{\n                    const r = iterator.next();\n                    if (r.done) return r;\n                    const value = this.get(r.value);\n                    return {\n                        done: false,\n                        value: [\n                            r.value,\n                            value\n                        ]\n                    };\n                }\n            };\n        }\n        [(DRAFT_STATE, Symbol.iterator)]() {\n            return this.entries();\n        }\n    }\n    function proxyMap_(target, parent) {\n        return new DraftMap(target, parent);\n    }\n    function prepareMapCopy(state) {\n        if (!state.copy_) {\n            state.assigned_ = /* @__PURE__ */ new Map();\n            state.copy_ = new Map(state.base_);\n        }\n    }\n    class DraftSet extends Set {\n        constructor(target, parent){\n            super();\n            this[DRAFT_STATE] = {\n                type_: 3 /* Set */ ,\n                parent_: parent,\n                scope_: parent ? parent.scope_ : getCurrentScope(),\n                modified_: false,\n                finalized_: false,\n                copy_: void 0,\n                base_: target,\n                draft_: this,\n                drafts_: /* @__PURE__ */ new Map(),\n                revoked_: false,\n                isManual_: false\n            };\n        }\n        get size() {\n            return latest(this[DRAFT_STATE]).size;\n        }\n        has(value) {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            if (!state.copy_) {\n                return state.base_.has(value);\n            }\n            if (state.copy_.has(value)) return true;\n            if (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value))) return true;\n            return false;\n        }\n        add(value) {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            if (!this.has(value)) {\n                prepareSetCopy(state);\n                markChanged(state);\n                state.copy_.add(value);\n            }\n            return this;\n        }\n        delete(value) {\n            if (!this.has(value)) {\n                return false;\n            }\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            prepareSetCopy(state);\n            markChanged(state);\n            return state.copy_.delete(value) || (state.drafts_.has(value) ? state.copy_.delete(state.drafts_.get(value)) : /* istanbul ignore next */ false);\n        }\n        clear() {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            if (latest(state).size) {\n                prepareSetCopy(state);\n                markChanged(state);\n                state.copy_.clear();\n            }\n        }\n        values() {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            prepareSetCopy(state);\n            return state.copy_.values();\n        }\n        entries() {\n            const state = this[DRAFT_STATE];\n            assertUnrevoked(state);\n            prepareSetCopy(state);\n            return state.copy_.entries();\n        }\n        keys() {\n            return this.values();\n        }\n        [(DRAFT_STATE, Symbol.iterator)]() {\n            return this.values();\n        }\n        forEach(cb, thisArg) {\n            const iterator = this.values();\n            let result = iterator.next();\n            while(!result.done){\n                cb.call(thisArg, result.value, result.value, this);\n                result = iterator.next();\n            }\n        }\n    }\n    function proxySet_(target, parent) {\n        return new DraftSet(target, parent);\n    }\n    function prepareSetCopy(state) {\n        if (!state.copy_) {\n            state.copy_ = /* @__PURE__ */ new Set();\n            state.base_.forEach((value)=>{\n                if (isDraftable(value)) {\n                    const draft = createProxy(value, state);\n                    state.drafts_.set(value, draft);\n                    state.copy_.add(draft);\n                } else {\n                    state.copy_.add(value);\n                }\n            });\n        }\n    }\n    function assertUnrevoked(state) {\n        if (state.revoked_) die(3, JSON.stringify(latest(state)));\n    }\n    loadPlugin(\"MapSet\", {\n        proxyMap_,\n        proxySet_\n    });\n}\n// src/immer.ts\nvar immer = new Immer2();\nvar produce = immer.produce;\nvar produceWithPatches = immer.produceWithPatches.bind(immer);\nvar setAutoFreeze = immer.setAutoFreeze.bind(immer);\nvar setUseStrictShallowCopy = immer.setUseStrictShallowCopy.bind(immer);\nvar applyPatches = immer.applyPatches.bind(immer);\nvar createDraft = immer.createDraft.bind(immer);\nvar finishDraft = immer.finishDraft.bind(immer);\nfunction castDraft(value) {\n    return value;\n}\nfunction castImmutable(value) {\n    return value;\n}\n //# sourceMappingURL=immer.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/immer/dist/immer.mjs\n");

/***/ })

};
;