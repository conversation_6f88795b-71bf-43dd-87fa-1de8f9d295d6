"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/resize-observer-polyfill";
exports.ids = ["vendor-chunks/resize-observer-polyfill"];
exports.modules = {

/***/ "(ssr)/./node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js":
/*!*************************************************************************!*\
  !*** ./node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\r\n * A collection of shims that provide minimal functionality of the ES6 collections.\r\n *\r\n * These implementations are not meant to be used outside of the ResizeObserver\r\n * modules as they cover only a limited range of use cases.\r\n */ /* eslint-disable require-jsdoc, valid-jsdoc */ var MapShim = function() {\n    if (typeof Map !== \"undefined\") {\n        return Map;\n    }\n    /**\r\n     * Returns index in provided array that matches the specified key.\r\n     *\r\n     * @param {Array<Array>} arr\r\n     * @param {*} key\r\n     * @returns {number}\r\n     */ function getIndex(arr, key) {\n        var result = -1;\n        arr.some(function(entry, index) {\n            if (entry[0] === key) {\n                result = index;\n                return true;\n            }\n            return false;\n        });\n        return result;\n    }\n    return /** @class */ function() {\n        function class_1() {\n            this.__entries__ = [];\n        }\n        Object.defineProperty(class_1.prototype, \"size\", {\n            /**\r\n             * @returns {boolean}\r\n             */ get: function() {\n                return this.__entries__.length;\n            },\n            enumerable: true,\n            configurable: true\n        });\n        /**\r\n         * @param {*} key\r\n         * @returns {*}\r\n         */ class_1.prototype.get = function(key) {\n            var index = getIndex(this.__entries__, key);\n            var entry = this.__entries__[index];\n            return entry && entry[1];\n        };\n        /**\r\n         * @param {*} key\r\n         * @param {*} value\r\n         * @returns {void}\r\n         */ class_1.prototype.set = function(key, value) {\n            var index = getIndex(this.__entries__, key);\n            if (~index) {\n                this.__entries__[index][1] = value;\n            } else {\n                this.__entries__.push([\n                    key,\n                    value\n                ]);\n            }\n        };\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */ class_1.prototype.delete = function(key) {\n            var entries = this.__entries__;\n            var index = getIndex(entries, key);\n            if (~index) {\n                entries.splice(index, 1);\n            }\n        };\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */ class_1.prototype.has = function(key) {\n            return !!~getIndex(this.__entries__, key);\n        };\n        /**\r\n         * @returns {void}\r\n         */ class_1.prototype.clear = function() {\n            this.__entries__.splice(0);\n        };\n        /**\r\n         * @param {Function} callback\r\n         * @param {*} [ctx=null]\r\n         * @returns {void}\r\n         */ class_1.prototype.forEach = function(callback, ctx) {\n            if (ctx === void 0) {\n                ctx = null;\n            }\n            for(var _i = 0, _a = this.__entries__; _i < _a.length; _i++){\n                var entry = _a[_i];\n                callback.call(ctx, entry[1], entry[0]);\n            }\n        };\n        return class_1;\n    }();\n}();\n/**\r\n * Detects whether window and document objects are available in current environment.\r\n */ var isBrowser =  false && 0;\n// Returns global object of a current environment.\nvar global$1 = function() {\n    if (typeof global !== \"undefined\" && global.Math === Math) {\n        return global;\n    }\n    if (typeof self !== \"undefined\" && self.Math === Math) {\n        return self;\n    }\n    if (false) {}\n    // eslint-disable-next-line no-new-func\n    return Function(\"return this\")();\n}();\n/**\r\n * A shim for the requestAnimationFrame which falls back to the setTimeout if\r\n * first one is not supported.\r\n *\r\n * @returns {number} Requests' identifier.\r\n */ var requestAnimationFrame$1 = function() {\n    if (typeof requestAnimationFrame === \"function\") {\n        // It's required to use a bounded function because IE sometimes throws\n        // an \"Invalid calling object\" error if rAF is invoked without the global\n        // object on the left hand side.\n        return requestAnimationFrame.bind(global$1);\n    }\n    return function(callback) {\n        return setTimeout(function() {\n            return callback(Date.now());\n        }, 1000 / 60);\n    };\n}();\n// Defines minimum timeout before adding a trailing call.\nvar trailingTimeout = 2;\n/**\r\n * Creates a wrapper function which ensures that provided callback will be\r\n * invoked only once during the specified delay period.\r\n *\r\n * @param {Function} callback - Function to be invoked after the delay period.\r\n * @param {number} delay - Delay after which to invoke callback.\r\n * @returns {Function}\r\n */ function throttle(callback, delay) {\n    var leadingCall = false, trailingCall = false, lastCallTime = 0;\n    /**\r\n     * Invokes the original callback function and schedules new invocation if\r\n     * the \"proxy\" was called during current request.\r\n     *\r\n     * @returns {void}\r\n     */ function resolvePending() {\n        if (leadingCall) {\n            leadingCall = false;\n            callback();\n        }\n        if (trailingCall) {\n            proxy();\n        }\n    }\n    /**\r\n     * Callback invoked after the specified delay. It will further postpone\r\n     * invocation of the original function delegating it to the\r\n     * requestAnimationFrame.\r\n     *\r\n     * @returns {void}\r\n     */ function timeoutCallback() {\n        requestAnimationFrame$1(resolvePending);\n    }\n    /**\r\n     * Schedules invocation of the original function.\r\n     *\r\n     * @returns {void}\r\n     */ function proxy() {\n        var timeStamp = Date.now();\n        if (leadingCall) {\n            // Reject immediately following calls.\n            if (timeStamp - lastCallTime < trailingTimeout) {\n                return;\n            }\n            // Schedule new call to be in invoked when the pending one is resolved.\n            // This is important for \"transitions\" which never actually start\n            // immediately so there is a chance that we might miss one if change\n            // happens amids the pending invocation.\n            trailingCall = true;\n        } else {\n            leadingCall = true;\n            trailingCall = false;\n            setTimeout(timeoutCallback, delay);\n        }\n        lastCallTime = timeStamp;\n    }\n    return proxy;\n}\n// Minimum delay before invoking the update of observers.\nvar REFRESH_DELAY = 20;\n// A list of substrings of CSS properties used to find transition events that\n// might affect dimensions of observed elements.\nvar transitionKeys = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\",\n    \"width\",\n    \"height\",\n    \"size\",\n    \"weight\"\n];\n// Check if MutationObserver is available.\nvar mutationObserverSupported = typeof MutationObserver !== \"undefined\";\n/**\r\n * Singleton controller class which handles updates of ResizeObserver instances.\r\n */ var ResizeObserverController = /** @class */ function() {\n    /**\r\n     * Creates a new instance of ResizeObserverController.\r\n     *\r\n     * @private\r\n     */ function ResizeObserverController() {\n        /**\r\n         * Indicates whether DOM listeners have been added.\r\n         *\r\n         * @private {boolean}\r\n         */ this.connected_ = false;\n        /**\r\n         * Tells that controller has subscribed for Mutation Events.\r\n         *\r\n         * @private {boolean}\r\n         */ this.mutationEventsAdded_ = false;\n        /**\r\n         * Keeps reference to the instance of MutationObserver.\r\n         *\r\n         * @private {MutationObserver}\r\n         */ this.mutationsObserver_ = null;\n        /**\r\n         * A list of connected observers.\r\n         *\r\n         * @private {Array<ResizeObserverSPI>}\r\n         */ this.observers_ = [];\n        this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);\n        this.refresh = throttle(this.refresh.bind(this), REFRESH_DELAY);\n    }\n    /**\r\n     * Adds observer to observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be added.\r\n     * @returns {void}\r\n     */ ResizeObserverController.prototype.addObserver = function(observer) {\n        if (!~this.observers_.indexOf(observer)) {\n            this.observers_.push(observer);\n        }\n        // Add listeners if they haven't been added yet.\n        if (!this.connected_) {\n            this.connect_();\n        }\n    };\n    /**\r\n     * Removes observer from observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be removed.\r\n     * @returns {void}\r\n     */ ResizeObserverController.prototype.removeObserver = function(observer) {\n        var observers = this.observers_;\n        var index = observers.indexOf(observer);\n        // Remove observer if it's present in registry.\n        if (~index) {\n            observers.splice(index, 1);\n        }\n        // Remove listeners if controller has no connected observers.\n        if (!observers.length && this.connected_) {\n            this.disconnect_();\n        }\n    };\n    /**\r\n     * Invokes the update of observers. It will continue running updates insofar\r\n     * it detects changes.\r\n     *\r\n     * @returns {void}\r\n     */ ResizeObserverController.prototype.refresh = function() {\n        var changesDetected = this.updateObservers_();\n        // Continue running updates if changes have been detected as there might\n        // be future ones caused by CSS transitions.\n        if (changesDetected) {\n            this.refresh();\n        }\n    };\n    /**\r\n     * Updates every observer from observers list and notifies them of queued\r\n     * entries.\r\n     *\r\n     * @private\r\n     * @returns {boolean} Returns \"true\" if any observer has detected changes in\r\n     *      dimensions of it's elements.\r\n     */ ResizeObserverController.prototype.updateObservers_ = function() {\n        // Collect observers that have active observations.\n        var activeObservers = this.observers_.filter(function(observer) {\n            return observer.gatherActive(), observer.hasActive();\n        });\n        // Deliver notifications in a separate cycle in order to avoid any\n        // collisions between observers, e.g. when multiple instances of\n        // ResizeObserver are tracking the same element and the callback of one\n        // of them changes content dimensions of the observed target. Sometimes\n        // this may result in notifications being blocked for the rest of observers.\n        activeObservers.forEach(function(observer) {\n            return observer.broadcastActive();\n        });\n        return activeObservers.length > 0;\n    };\n    /**\r\n     * Initializes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */ ResizeObserverController.prototype.connect_ = function() {\n        // Do nothing if running in a non-browser environment or if listeners\n        // have been already added.\n        if (!isBrowser || this.connected_) {\n            return;\n        }\n        // Subscription to the \"Transitionend\" event is used as a workaround for\n        // delayed transitions. This way it's possible to capture at least the\n        // final state of an element.\n        document.addEventListener(\"transitionend\", this.onTransitionEnd_);\n        window.addEventListener(\"resize\", this.refresh);\n        if (mutationObserverSupported) {\n            this.mutationsObserver_ = new MutationObserver(this.refresh);\n            this.mutationsObserver_.observe(document, {\n                attributes: true,\n                childList: true,\n                characterData: true,\n                subtree: true\n            });\n        } else {\n            document.addEventListener(\"DOMSubtreeModified\", this.refresh);\n            this.mutationEventsAdded_ = true;\n        }\n        this.connected_ = true;\n    };\n    /**\r\n     * Removes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */ ResizeObserverController.prototype.disconnect_ = function() {\n        // Do nothing if running in a non-browser environment or if listeners\n        // have been already removed.\n        if (!isBrowser || !this.connected_) {\n            return;\n        }\n        document.removeEventListener(\"transitionend\", this.onTransitionEnd_);\n        window.removeEventListener(\"resize\", this.refresh);\n        if (this.mutationsObserver_) {\n            this.mutationsObserver_.disconnect();\n        }\n        if (this.mutationEventsAdded_) {\n            document.removeEventListener(\"DOMSubtreeModified\", this.refresh);\n        }\n        this.mutationsObserver_ = null;\n        this.mutationEventsAdded_ = false;\n        this.connected_ = false;\n    };\n    /**\r\n     * \"Transitionend\" event handler.\r\n     *\r\n     * @private\r\n     * @param {TransitionEvent} event\r\n     * @returns {void}\r\n     */ ResizeObserverController.prototype.onTransitionEnd_ = function(_a) {\n        var _b = _a.propertyName, propertyName = _b === void 0 ? \"\" : _b;\n        // Detect whether transition may affect dimensions of an element.\n        var isReflowProperty = transitionKeys.some(function(key) {\n            return !!~propertyName.indexOf(key);\n        });\n        if (isReflowProperty) {\n            this.refresh();\n        }\n    };\n    /**\r\n     * Returns instance of the ResizeObserverController.\r\n     *\r\n     * @returns {ResizeObserverController}\r\n     */ ResizeObserverController.getInstance = function() {\n        if (!this.instance_) {\n            this.instance_ = new ResizeObserverController();\n        }\n        return this.instance_;\n    };\n    /**\r\n     * Holds reference to the controller's instance.\r\n     *\r\n     * @private {ResizeObserverController}\r\n     */ ResizeObserverController.instance_ = null;\n    return ResizeObserverController;\n}();\n/**\r\n * Defines non-writable/enumerable properties of the provided target object.\r\n *\r\n * @param {Object} target - Object for which to define properties.\r\n * @param {Object} props - Properties to be defined.\r\n * @returns {Object} Target object.\r\n */ var defineConfigurable = function(target, props) {\n    for(var _i = 0, _a = Object.keys(props); _i < _a.length; _i++){\n        var key = _a[_i];\n        Object.defineProperty(target, key, {\n            value: props[key],\n            enumerable: false,\n            writable: false,\n            configurable: true\n        });\n    }\n    return target;\n};\n/**\r\n * Returns the global object associated with provided element.\r\n *\r\n * @param {Object} target\r\n * @returns {Object}\r\n */ var getWindowOf = function(target) {\n    // Assume that the element is an instance of Node, which means that it\n    // has the \"ownerDocument\" property from which we can retrieve a\n    // corresponding global object.\n    var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView;\n    // Return the local global object if it's not possible extract one from\n    // provided element.\n    return ownerGlobal || global$1;\n};\n// Placeholder of an empty content rectangle.\nvar emptyRect = createRectInit(0, 0, 0, 0);\n/**\r\n * Converts provided string to a number.\r\n *\r\n * @param {number|string} value\r\n * @returns {number}\r\n */ function toFloat(value) {\n    return parseFloat(value) || 0;\n}\n/**\r\n * Extracts borders size from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @param {...string} positions - Borders positions (top, right, ...)\r\n * @returns {number}\r\n */ function getBordersSize(styles) {\n    var positions = [];\n    for(var _i = 1; _i < arguments.length; _i++){\n        positions[_i - 1] = arguments[_i];\n    }\n    return positions.reduce(function(size, position) {\n        var value = styles[\"border-\" + position + \"-width\"];\n        return size + toFloat(value);\n    }, 0);\n}\n/**\r\n * Extracts paddings sizes from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @returns {Object} Paddings box.\r\n */ function getPaddings(styles) {\n    var positions = [\n        \"top\",\n        \"right\",\n        \"bottom\",\n        \"left\"\n    ];\n    var paddings = {};\n    for(var _i = 0, positions_1 = positions; _i < positions_1.length; _i++){\n        var position = positions_1[_i];\n        var value = styles[\"padding-\" + position];\n        paddings[position] = toFloat(value);\n    }\n    return paddings;\n}\n/**\r\n * Calculates content rectangle of provided SVG element.\r\n *\r\n * @param {SVGGraphicsElement} target - Element content rectangle of which needs\r\n *      to be calculated.\r\n * @returns {DOMRectInit}\r\n */ function getSVGContentRect(target) {\n    var bbox = target.getBBox();\n    return createRectInit(0, 0, bbox.width, bbox.height);\n}\n/**\r\n * Calculates content rectangle of provided HTMLElement.\r\n *\r\n * @param {HTMLElement} target - Element for which to calculate the content rectangle.\r\n * @returns {DOMRectInit}\r\n */ function getHTMLElementContentRect(target) {\n    // Client width & height properties can't be\n    // used exclusively as they provide rounded values.\n    var clientWidth = target.clientWidth, clientHeight = target.clientHeight;\n    // By this condition we can catch all non-replaced inline, hidden and\n    // detached elements. Though elements with width & height properties less\n    // than 0.5 will be discarded as well.\n    //\n    // Without it we would need to implement separate methods for each of\n    // those cases and it's not possible to perform a precise and performance\n    // effective test for hidden elements. E.g. even jQuery's ':visible' filter\n    // gives wrong results for elements with width & height less than 0.5.\n    if (!clientWidth && !clientHeight) {\n        return emptyRect;\n    }\n    var styles = getWindowOf(target).getComputedStyle(target);\n    var paddings = getPaddings(styles);\n    var horizPad = paddings.left + paddings.right;\n    var vertPad = paddings.top + paddings.bottom;\n    // Computed styles of width & height are being used because they are the\n    // only dimensions available to JS that contain non-rounded values. It could\n    // be possible to utilize the getBoundingClientRect if only it's data wasn't\n    // affected by CSS transformations let alone paddings, borders and scroll bars.\n    var width = toFloat(styles.width), height = toFloat(styles.height);\n    // Width & height include paddings and borders when the 'border-box' box\n    // model is applied (except for IE).\n    if (styles.boxSizing === \"border-box\") {\n        // Following conditions are required to handle Internet Explorer which\n        // doesn't include paddings and borders to computed CSS dimensions.\n        //\n        // We can say that if CSS dimensions + paddings are equal to the \"client\"\n        // properties then it's either IE, and thus we don't need to subtract\n        // anything, or an element merely doesn't have paddings/borders styles.\n        if (Math.round(width + horizPad) !== clientWidth) {\n            width -= getBordersSize(styles, \"left\", \"right\") + horizPad;\n        }\n        if (Math.round(height + vertPad) !== clientHeight) {\n            height -= getBordersSize(styles, \"top\", \"bottom\") + vertPad;\n        }\n    }\n    // Following steps can't be applied to the document's root element as its\n    // client[Width/Height] properties represent viewport area of the window.\n    // Besides, it's as well not necessary as the <html> itself neither has\n    // rendered scroll bars nor it can be clipped.\n    if (!isDocumentElement(target)) {\n        // In some browsers (only in Firefox, actually) CSS width & height\n        // include scroll bars size which can be removed at this step as scroll\n        // bars are the only difference between rounded dimensions + paddings\n        // and \"client\" properties, though that is not always true in Chrome.\n        var vertScrollbar = Math.round(width + horizPad) - clientWidth;\n        var horizScrollbar = Math.round(height + vertPad) - clientHeight;\n        // Chrome has a rather weird rounding of \"client\" properties.\n        // E.g. for an element with content width of 314.2px it sometimes gives\n        // the client width of 315px and for the width of 314.7px it may give\n        // 314px. And it doesn't happen all the time. So just ignore this delta\n        // as a non-relevant.\n        if (Math.abs(vertScrollbar) !== 1) {\n            width -= vertScrollbar;\n        }\n        if (Math.abs(horizScrollbar) !== 1) {\n            height -= horizScrollbar;\n        }\n    }\n    return createRectInit(paddings.left, paddings.top, width, height);\n}\n/**\r\n * Checks whether provided element is an instance of the SVGGraphicsElement.\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */ var isSVGGraphicsElement = function() {\n    // Some browsers, namely IE and Edge, don't have the SVGGraphicsElement\n    // interface.\n    if (typeof SVGGraphicsElement !== \"undefined\") {\n        return function(target) {\n            return target instanceof getWindowOf(target).SVGGraphicsElement;\n        };\n    }\n    // If it's so, then check that element is at least an instance of the\n    // SVGElement and that it has the \"getBBox\" method.\n    // eslint-disable-next-line no-extra-parens\n    return function(target) {\n        return target instanceof getWindowOf(target).SVGElement && typeof target.getBBox === \"function\";\n    };\n}();\n/**\r\n * Checks whether provided element is a document element (<html>).\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */ function isDocumentElement(target) {\n    return target === getWindowOf(target).document.documentElement;\n}\n/**\r\n * Calculates an appropriate content rectangle for provided html or svg element.\r\n *\r\n * @param {Element} target - Element content rectangle of which needs to be calculated.\r\n * @returns {DOMRectInit}\r\n */ function getContentRect(target) {\n    if (!isBrowser) {\n        return emptyRect;\n    }\n    if (isSVGGraphicsElement(target)) {\n        return getSVGContentRect(target);\n    }\n    return getHTMLElementContentRect(target);\n}\n/**\r\n * Creates rectangle with an interface of the DOMRectReadOnly.\r\n * Spec: https://drafts.fxtf.org/geometry/#domrectreadonly\r\n *\r\n * @param {DOMRectInit} rectInit - Object with rectangle's x/y coordinates and dimensions.\r\n * @returns {DOMRectReadOnly}\r\n */ function createReadOnlyRect(_a) {\n    var x = _a.x, y = _a.y, width = _a.width, height = _a.height;\n    // If DOMRectReadOnly is available use it as a prototype for the rectangle.\n    var Constr = typeof DOMRectReadOnly !== \"undefined\" ? DOMRectReadOnly : Object;\n    var rect = Object.create(Constr.prototype);\n    // Rectangle's properties are not writable and non-enumerable.\n    defineConfigurable(rect, {\n        x: x,\n        y: y,\n        width: width,\n        height: height,\n        top: y,\n        right: x + width,\n        bottom: height + y,\n        left: x\n    });\n    return rect;\n}\n/**\r\n * Creates DOMRectInit object based on the provided dimensions and the x/y coordinates.\r\n * Spec: https://drafts.fxtf.org/geometry/#dictdef-domrectinit\r\n *\r\n * @param {number} x - X coordinate.\r\n * @param {number} y - Y coordinate.\r\n * @param {number} width - Rectangle's width.\r\n * @param {number} height - Rectangle's height.\r\n * @returns {DOMRectInit}\r\n */ function createRectInit(x, y, width, height) {\n    return {\n        x: x,\n        y: y,\n        width: width,\n        height: height\n    };\n}\n/**\r\n * Class that is responsible for computations of the content rectangle of\r\n * provided DOM element and for keeping track of it's changes.\r\n */ var ResizeObservation = /** @class */ function() {\n    /**\r\n     * Creates an instance of ResizeObservation.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     */ function ResizeObservation(target) {\n        /**\r\n         * Broadcasted width of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */ this.broadcastWidth = 0;\n        /**\r\n         * Broadcasted height of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */ this.broadcastHeight = 0;\n        /**\r\n         * Reference to the last observed content rectangle.\r\n         *\r\n         * @private {DOMRectInit}\r\n         */ this.contentRect_ = createRectInit(0, 0, 0, 0);\n        this.target = target;\n    }\n    /**\r\n     * Updates content rectangle and tells whether it's width or height properties\r\n     * have changed since the last broadcast.\r\n     *\r\n     * @returns {boolean}\r\n     */ ResizeObservation.prototype.isActive = function() {\n        var rect = getContentRect(this.target);\n        this.contentRect_ = rect;\n        return rect.width !== this.broadcastWidth || rect.height !== this.broadcastHeight;\n    };\n    /**\r\n     * Updates 'broadcastWidth' and 'broadcastHeight' properties with a data\r\n     * from the corresponding properties of the last observed content rectangle.\r\n     *\r\n     * @returns {DOMRectInit} Last observed content rectangle.\r\n     */ ResizeObservation.prototype.broadcastRect = function() {\n        var rect = this.contentRect_;\n        this.broadcastWidth = rect.width;\n        this.broadcastHeight = rect.height;\n        return rect;\n    };\n    return ResizeObservation;\n}();\nvar ResizeObserverEntry = /** @class */ function() {\n    /**\r\n     * Creates an instance of ResizeObserverEntry.\r\n     *\r\n     * @param {Element} target - Element that is being observed.\r\n     * @param {DOMRectInit} rectInit - Data of the element's content rectangle.\r\n     */ function ResizeObserverEntry(target, rectInit) {\n        var contentRect = createReadOnlyRect(rectInit);\n        // According to the specification following properties are not writable\n        // and are also not enumerable in the native implementation.\n        //\n        // Property accessors are not being used as they'd require to define a\n        // private WeakMap storage which may cause memory leaks in browsers that\n        // don't support this type of collections.\n        defineConfigurable(this, {\n            target: target,\n            contentRect: contentRect\n        });\n    }\n    return ResizeObserverEntry;\n}();\nvar ResizeObserverSPI = /** @class */ function() {\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback function that is invoked\r\n     *      when one of the observed elements changes it's content dimensions.\r\n     * @param {ResizeObserverController} controller - Controller instance which\r\n     *      is responsible for the updates of observer.\r\n     * @param {ResizeObserver} callbackCtx - Reference to the public\r\n     *      ResizeObserver instance which will be passed to callback function.\r\n     */ function ResizeObserverSPI(callback, controller, callbackCtx) {\n        /**\r\n         * Collection of resize observations that have detected changes in dimensions\r\n         * of elements.\r\n         *\r\n         * @private {Array<ResizeObservation>}\r\n         */ this.activeObservations_ = [];\n        /**\r\n         * Registry of the ResizeObservation instances.\r\n         *\r\n         * @private {Map<Element, ResizeObservation>}\r\n         */ this.observations_ = new MapShim();\n        if (typeof callback !== \"function\") {\n            throw new TypeError(\"The callback provided as parameter 1 is not a function.\");\n        }\n        this.callback_ = callback;\n        this.controller_ = controller;\n        this.callbackCtx_ = callbackCtx;\n    }\n    /**\r\n     * Starts observing provided element.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     * @returns {void}\r\n     */ ResizeObserverSPI.prototype.observe = function(target) {\n        if (!arguments.length) {\n            throw new TypeError(\"1 argument required, but only 0 present.\");\n        }\n        // Do nothing if current environment doesn't have the Element interface.\n        if (typeof Element === \"undefined\" || !(Element instanceof Object)) {\n            return;\n        }\n        if (!(target instanceof getWindowOf(target).Element)) {\n            throw new TypeError('parameter 1 is not of type \"Element\".');\n        }\n        var observations = this.observations_;\n        // Do nothing if element is already being observed.\n        if (observations.has(target)) {\n            return;\n        }\n        observations.set(target, new ResizeObservation(target));\n        this.controller_.addObserver(this);\n        // Force the update of observations.\n        this.controller_.refresh();\n    };\n    /**\r\n     * Stops observing provided element.\r\n     *\r\n     * @param {Element} target - Element to stop observing.\r\n     * @returns {void}\r\n     */ ResizeObserverSPI.prototype.unobserve = function(target) {\n        if (!arguments.length) {\n            throw new TypeError(\"1 argument required, but only 0 present.\");\n        }\n        // Do nothing if current environment doesn't have the Element interface.\n        if (typeof Element === \"undefined\" || !(Element instanceof Object)) {\n            return;\n        }\n        if (!(target instanceof getWindowOf(target).Element)) {\n            throw new TypeError('parameter 1 is not of type \"Element\".');\n        }\n        var observations = this.observations_;\n        // Do nothing if element is not being observed.\n        if (!observations.has(target)) {\n            return;\n        }\n        observations.delete(target);\n        if (!observations.size) {\n            this.controller_.removeObserver(this);\n        }\n    };\n    /**\r\n     * Stops observing all elements.\r\n     *\r\n     * @returns {void}\r\n     */ ResizeObserverSPI.prototype.disconnect = function() {\n        this.clearActive();\n        this.observations_.clear();\n        this.controller_.removeObserver(this);\n    };\n    /**\r\n     * Collects observation instances the associated element of which has changed\r\n     * it's content rectangle.\r\n     *\r\n     * @returns {void}\r\n     */ ResizeObserverSPI.prototype.gatherActive = function() {\n        var _this = this;\n        this.clearActive();\n        this.observations_.forEach(function(observation) {\n            if (observation.isActive()) {\n                _this.activeObservations_.push(observation);\n            }\n        });\n    };\n    /**\r\n     * Invokes initial callback function with a list of ResizeObserverEntry\r\n     * instances collected from active resize observations.\r\n     *\r\n     * @returns {void}\r\n     */ ResizeObserverSPI.prototype.broadcastActive = function() {\n        // Do nothing if observer doesn't have active observations.\n        if (!this.hasActive()) {\n            return;\n        }\n        var ctx = this.callbackCtx_;\n        // Create ResizeObserverEntry instance for every active observation.\n        var entries = this.activeObservations_.map(function(observation) {\n            return new ResizeObserverEntry(observation.target, observation.broadcastRect());\n        });\n        this.callback_.call(ctx, entries, ctx);\n        this.clearActive();\n    };\n    /**\r\n     * Clears the collection of active observations.\r\n     *\r\n     * @returns {void}\r\n     */ ResizeObserverSPI.prototype.clearActive = function() {\n        this.activeObservations_.splice(0);\n    };\n    /**\r\n     * Tells whether observer has active observations.\r\n     *\r\n     * @returns {boolean}\r\n     */ ResizeObserverSPI.prototype.hasActive = function() {\n        return this.activeObservations_.length > 0;\n    };\n    return ResizeObserverSPI;\n}();\n// Registry of internal observers. If WeakMap is not available use current shim\n// for the Map collection as it has all required methods and because WeakMap\n// can't be fully polyfilled anyway.\nvar observers = typeof WeakMap !== \"undefined\" ? new WeakMap() : new MapShim();\n/**\r\n * ResizeObserver API. Encapsulates the ResizeObserver SPI implementation\r\n * exposing only those methods and properties that are defined in the spec.\r\n */ var ResizeObserver = /** @class */ function() {\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback that is invoked when\r\n     *      dimensions of the observed elements change.\r\n     */ function ResizeObserver(callback) {\n        if (!(this instanceof ResizeObserver)) {\n            throw new TypeError(\"Cannot call a class as a function.\");\n        }\n        if (!arguments.length) {\n            throw new TypeError(\"1 argument required, but only 0 present.\");\n        }\n        var controller = ResizeObserverController.getInstance();\n        var observer = new ResizeObserverSPI(callback, controller, this);\n        observers.set(this, observer);\n    }\n    return ResizeObserver;\n}();\n// Expose public methods of ResizeObserver.\n[\n    \"observe\",\n    \"unobserve\",\n    \"disconnect\"\n].forEach(function(method) {\n    ResizeObserver.prototype[method] = function() {\n        var _a;\n        return (_a = observers.get(this))[method].apply(_a, arguments);\n    };\n});\nvar index = function() {\n    // Export existing implementation if available.\n    if (typeof global$1.ResizeObserver !== \"undefined\") {\n        return global$1.ResizeObserver;\n    }\n    return ResizeObserver;\n}();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (index);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVzaXplLW9ic2VydmVyLXBvbHlmaWxsL2Rpc3QvUmVzaXplT2JzZXJ2ZXIuZXMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOzs7OztDQUtDLEdBQ0QsNkNBQTZDLEdBQzdDLElBQUlBLFVBQVU7SUFDVixJQUFJLE9BQU9DLFFBQVEsYUFBYTtRQUM1QixPQUFPQTtJQUNYO0lBQ0E7Ozs7OztLQU1DLEdBQ0QsU0FBU0MsU0FBU0MsR0FBRyxFQUFFQyxHQUFHO1FBQ3RCLElBQUlDLFNBQVMsQ0FBQztRQUNkRixJQUFJRyxJQUFJLENBQUMsU0FBVUMsS0FBSyxFQUFFQyxLQUFLO1lBQzNCLElBQUlELEtBQUssQ0FBQyxFQUFFLEtBQUtILEtBQUs7Z0JBQ2xCQyxTQUFTRztnQkFDVCxPQUFPO1lBQ1g7WUFDQSxPQUFPO1FBQ1g7UUFDQSxPQUFPSDtJQUNYO0lBQ0EsT0FBTyxXQUFXLEdBQUk7UUFDbEIsU0FBU0k7WUFDTCxJQUFJLENBQUNDLFdBQVcsR0FBRyxFQUFFO1FBQ3pCO1FBQ0FDLE9BQU9DLGNBQWMsQ0FBQ0gsUUFBUUksU0FBUyxFQUFFLFFBQVE7WUFDN0M7O2FBRUMsR0FDREMsS0FBSztnQkFDRCxPQUFPLElBQUksQ0FBQ0osV0FBVyxDQUFDSyxNQUFNO1lBQ2xDO1lBQ0FDLFlBQVk7WUFDWkMsY0FBYztRQUNsQjtRQUNBOzs7U0FHQyxHQUNEUixRQUFRSSxTQUFTLENBQUNDLEdBQUcsR0FBRyxTQUFVVixHQUFHO1lBQ2pDLElBQUlJLFFBQVFOLFNBQVMsSUFBSSxDQUFDUSxXQUFXLEVBQUVOO1lBQ3ZDLElBQUlHLFFBQVEsSUFBSSxDQUFDRyxXQUFXLENBQUNGLE1BQU07WUFDbkMsT0FBT0QsU0FBU0EsS0FBSyxDQUFDLEVBQUU7UUFDNUI7UUFDQTs7OztTQUlDLEdBQ0RFLFFBQVFJLFNBQVMsQ0FBQ0ssR0FBRyxHQUFHLFNBQVVkLEdBQUcsRUFBRWUsS0FBSztZQUN4QyxJQUFJWCxRQUFRTixTQUFTLElBQUksQ0FBQ1EsV0FBVyxFQUFFTjtZQUN2QyxJQUFJLENBQUNJLE9BQU87Z0JBQ1IsSUFBSSxDQUFDRSxXQUFXLENBQUNGLE1BQU0sQ0FBQyxFQUFFLEdBQUdXO1lBQ2pDLE9BQ0s7Z0JBQ0QsSUFBSSxDQUFDVCxXQUFXLENBQUNVLElBQUksQ0FBQztvQkFBQ2hCO29CQUFLZTtpQkFBTTtZQUN0QztRQUNKO1FBQ0E7OztTQUdDLEdBQ0RWLFFBQVFJLFNBQVMsQ0FBQ1EsTUFBTSxHQUFHLFNBQVVqQixHQUFHO1lBQ3BDLElBQUlrQixVQUFVLElBQUksQ0FBQ1osV0FBVztZQUM5QixJQUFJRixRQUFRTixTQUFTb0IsU0FBU2xCO1lBQzlCLElBQUksQ0FBQ0ksT0FBTztnQkFDUmMsUUFBUUMsTUFBTSxDQUFDZixPQUFPO1lBQzFCO1FBQ0o7UUFDQTs7O1NBR0MsR0FDREMsUUFBUUksU0FBUyxDQUFDVyxHQUFHLEdBQUcsU0FBVXBCLEdBQUc7WUFDakMsT0FBTyxDQUFDLENBQUMsQ0FBQ0YsU0FBUyxJQUFJLENBQUNRLFdBQVcsRUFBRU47UUFDekM7UUFDQTs7U0FFQyxHQUNESyxRQUFRSSxTQUFTLENBQUNZLEtBQUssR0FBRztZQUN0QixJQUFJLENBQUNmLFdBQVcsQ0FBQ2EsTUFBTSxDQUFDO1FBQzVCO1FBQ0E7Ozs7U0FJQyxHQUNEZCxRQUFRSSxTQUFTLENBQUNhLE9BQU8sR0FBRyxTQUFVQyxRQUFRLEVBQUVDLEdBQUc7WUFDL0MsSUFBSUEsUUFBUSxLQUFLLEdBQUc7Z0JBQUVBLE1BQU07WUFBTTtZQUNsQyxJQUFLLElBQUlDLEtBQUssR0FBR0MsS0FBSyxJQUFJLENBQUNwQixXQUFXLEVBQUVtQixLQUFLQyxHQUFHZixNQUFNLEVBQUVjLEtBQU07Z0JBQzFELElBQUl0QixRQUFRdUIsRUFBRSxDQUFDRCxHQUFHO2dCQUNsQkYsU0FBU0ksSUFBSSxDQUFDSCxLQUFLckIsS0FBSyxDQUFDLEVBQUUsRUFBRUEsS0FBSyxDQUFDLEVBQUU7WUFDekM7UUFDSjtRQUNBLE9BQU9FO0lBQ1g7QUFDSjtBQUVBOztDQUVDLEdBQ0QsSUFBSXVCLFlBQVksTUFBcUQsSUFBZUUsQ0FBNEJEO0FBRWhILGtEQUFrRDtBQUNsRCxJQUFJRSxXQUFXO0lBQ1gsSUFBSSxPQUFPQyxXQUFXLGVBQWVBLE9BQU9DLElBQUksS0FBS0EsTUFBTTtRQUN2RCxPQUFPRDtJQUNYO0lBQ0EsSUFBSSxPQUFPRSxTQUFTLGVBQWVBLEtBQUtELElBQUksS0FBS0EsTUFBTTtRQUNuRCxPQUFPQztJQUNYO0lBQ0EsSUFBSSxLQUFxREQsRUFBRSxFQUUxRDtJQUNELHVDQUF1QztJQUN2QyxPQUFPRSxTQUFTO0FBQ3BCO0FBRUE7Ozs7O0NBS0MsR0FDRCxJQUFJQywwQkFBMEI7SUFDMUIsSUFBSSxPQUFPQywwQkFBMEIsWUFBWTtRQUM3QyxzRUFBc0U7UUFDdEUseUVBQXlFO1FBQ3pFLGdDQUFnQztRQUNoQyxPQUFPQSxzQkFBc0JDLElBQUksQ0FBQ1A7SUFDdEM7SUFDQSxPQUFPLFNBQVVSLFFBQVE7UUFBSSxPQUFPZ0IsV0FBVztZQUFjLE9BQU9oQixTQUFTaUIsS0FBS0MsR0FBRztRQUFLLEdBQUcsT0FBTztJQUFLO0FBQzdHO0FBRUEseURBQXlEO0FBQ3pELElBQUlDLGtCQUFrQjtBQUN0Qjs7Ozs7OztDQU9DLEdBQ0QsU0FBU0MsU0FBVXBCLFFBQVEsRUFBRXFCLEtBQUs7SUFDOUIsSUFBSUMsY0FBYyxPQUFPQyxlQUFlLE9BQU9DLGVBQWU7SUFDOUQ7Ozs7O0tBS0MsR0FDRCxTQUFTQztRQUNMLElBQUlILGFBQWE7WUFDYkEsY0FBYztZQUNkdEI7UUFDSjtRQUNBLElBQUl1QixjQUFjO1lBQ2RHO1FBQ0o7SUFDSjtJQUNBOzs7Ozs7S0FNQyxHQUNELFNBQVNDO1FBQ0xkLHdCQUF3Qlk7SUFDNUI7SUFDQTs7OztLQUlDLEdBQ0QsU0FBU0M7UUFDTCxJQUFJRSxZQUFZWCxLQUFLQyxHQUFHO1FBQ3hCLElBQUlJLGFBQWE7WUFDYixzQ0FBc0M7WUFDdEMsSUFBSU0sWUFBWUosZUFBZUwsaUJBQWlCO2dCQUM1QztZQUNKO1lBQ0EsdUVBQXVFO1lBQ3ZFLGlFQUFpRTtZQUNqRSxvRUFBb0U7WUFDcEUsd0NBQXdDO1lBQ3hDSSxlQUFlO1FBQ25CLE9BQ0s7WUFDREQsY0FBYztZQUNkQyxlQUFlO1lBQ2ZQLFdBQVdXLGlCQUFpQk47UUFDaEM7UUFDQUcsZUFBZUk7SUFDbkI7SUFDQSxPQUFPRjtBQUNYO0FBRUEseURBQXlEO0FBQ3pELElBQUlHLGdCQUFnQjtBQUNwQiw2RUFBNkU7QUFDN0UsZ0RBQWdEO0FBQ2hELElBQUlDLGlCQUFpQjtJQUFDO0lBQU87SUFBUztJQUFVO0lBQVE7SUFBUztJQUFVO0lBQVE7Q0FBUztBQUM1RiwwQ0FBMEM7QUFDMUMsSUFBSUMsNEJBQTRCLE9BQU9DLHFCQUFxQjtBQUM1RDs7Q0FFQyxHQUNELElBQUlDLDJCQUEyQixXQUFXLEdBQUk7SUFDMUM7Ozs7S0FJQyxHQUNELFNBQVNBO1FBQ0w7Ozs7U0FJQyxHQUNELElBQUksQ0FBQ0MsVUFBVSxHQUFHO1FBQ2xCOzs7O1NBSUMsR0FDRCxJQUFJLENBQUNDLG9CQUFvQixHQUFHO1FBQzVCOzs7O1NBSUMsR0FDRCxJQUFJLENBQUNDLGtCQUFrQixHQUFHO1FBQzFCOzs7O1NBSUMsR0FDRCxJQUFJLENBQUNDLFVBQVUsR0FBRyxFQUFFO1FBQ3BCLElBQUksQ0FBQ0MsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDQSxnQkFBZ0IsQ0FBQ3ZCLElBQUksQ0FBQyxJQUFJO1FBQ3ZELElBQUksQ0FBQ3dCLE9BQU8sR0FBR25CLFNBQVMsSUFBSSxDQUFDbUIsT0FBTyxDQUFDeEIsSUFBSSxDQUFDLElBQUksR0FBR2M7SUFDckQ7SUFDQTs7Ozs7S0FLQyxHQUNESSx5QkFBeUIvQyxTQUFTLENBQUNzRCxXQUFXLEdBQUcsU0FBVUMsUUFBUTtRQUMvRCxJQUFJLENBQUMsQ0FBQyxJQUFJLENBQUNKLFVBQVUsQ0FBQ0ssT0FBTyxDQUFDRCxXQUFXO1lBQ3JDLElBQUksQ0FBQ0osVUFBVSxDQUFDNUMsSUFBSSxDQUFDZ0Q7UUFDekI7UUFDQSxnREFBZ0Q7UUFDaEQsSUFBSSxDQUFDLElBQUksQ0FBQ1AsVUFBVSxFQUFFO1lBQ2xCLElBQUksQ0FBQ1MsUUFBUTtRQUNqQjtJQUNKO0lBQ0E7Ozs7O0tBS0MsR0FDRFYseUJBQXlCL0MsU0FBUyxDQUFDMEQsY0FBYyxHQUFHLFNBQVVILFFBQVE7UUFDbEUsSUFBSUksWUFBWSxJQUFJLENBQUNSLFVBQVU7UUFDL0IsSUFBSXhELFFBQVFnRSxVQUFVSCxPQUFPLENBQUNEO1FBQzlCLCtDQUErQztRQUMvQyxJQUFJLENBQUM1RCxPQUFPO1lBQ1JnRSxVQUFVakQsTUFBTSxDQUFDZixPQUFPO1FBQzVCO1FBQ0EsNkRBQTZEO1FBQzdELElBQUksQ0FBQ2dFLFVBQVV6RCxNQUFNLElBQUksSUFBSSxDQUFDOEMsVUFBVSxFQUFFO1lBQ3RDLElBQUksQ0FBQ1ksV0FBVztRQUNwQjtJQUNKO0lBQ0E7Ozs7O0tBS0MsR0FDRGIseUJBQXlCL0MsU0FBUyxDQUFDcUQsT0FBTyxHQUFHO1FBQ3pDLElBQUlRLGtCQUFrQixJQUFJLENBQUNDLGdCQUFnQjtRQUMzQyx3RUFBd0U7UUFDeEUsNENBQTRDO1FBQzVDLElBQUlELGlCQUFpQjtZQUNqQixJQUFJLENBQUNSLE9BQU87UUFDaEI7SUFDSjtJQUNBOzs7Ozs7O0tBT0MsR0FDRE4seUJBQXlCL0MsU0FBUyxDQUFDOEQsZ0JBQWdCLEdBQUc7UUFDbEQsbURBQW1EO1FBQ25ELElBQUlDLGtCQUFrQixJQUFJLENBQUNaLFVBQVUsQ0FBQ2EsTUFBTSxDQUFDLFNBQVVULFFBQVE7WUFDM0QsT0FBT0EsU0FBU1UsWUFBWSxJQUFJVixTQUFTVyxTQUFTO1FBQ3REO1FBQ0Esa0VBQWtFO1FBQ2xFLGdFQUFnRTtRQUNoRSx1RUFBdUU7UUFDdkUsdUVBQXVFO1FBQ3ZFLDRFQUE0RTtRQUM1RUgsZ0JBQWdCbEQsT0FBTyxDQUFDLFNBQVUwQyxRQUFRO1lBQUksT0FBT0EsU0FBU1ksZUFBZTtRQUFJO1FBQ2pGLE9BQU9KLGdCQUFnQjdELE1BQU0sR0FBRztJQUNwQztJQUNBOzs7OztLQUtDLEdBQ0Q2Qyx5QkFBeUIvQyxTQUFTLENBQUN5RCxRQUFRLEdBQUc7UUFDMUMscUVBQXFFO1FBQ3JFLDJCQUEyQjtRQUMzQixJQUFJLENBQUN0QyxhQUFhLElBQUksQ0FBQzZCLFVBQVUsRUFBRTtZQUMvQjtRQUNKO1FBQ0Esd0VBQXdFO1FBQ3hFLHNFQUFzRTtRQUN0RSw2QkFBNkI7UUFDN0I1QixTQUFTZ0QsZ0JBQWdCLENBQUMsaUJBQWlCLElBQUksQ0FBQ2hCLGdCQUFnQjtRQUNoRS9CLE9BQU8rQyxnQkFBZ0IsQ0FBQyxVQUFVLElBQUksQ0FBQ2YsT0FBTztRQUM5QyxJQUFJUiwyQkFBMkI7WUFDM0IsSUFBSSxDQUFDSyxrQkFBa0IsR0FBRyxJQUFJSixpQkFBaUIsSUFBSSxDQUFDTyxPQUFPO1lBQzNELElBQUksQ0FBQ0gsa0JBQWtCLENBQUNtQixPQUFPLENBQUNqRCxVQUFVO2dCQUN0Q2tELFlBQVk7Z0JBQ1pDLFdBQVc7Z0JBQ1hDLGVBQWU7Z0JBQ2ZDLFNBQVM7WUFDYjtRQUNKLE9BQ0s7WUFDRHJELFNBQVNnRCxnQkFBZ0IsQ0FBQyxzQkFBc0IsSUFBSSxDQUFDZixPQUFPO1lBQzVELElBQUksQ0FBQ0osb0JBQW9CLEdBQUc7UUFDaEM7UUFDQSxJQUFJLENBQUNELFVBQVUsR0FBRztJQUN0QjtJQUNBOzs7OztLQUtDLEdBQ0RELHlCQUF5Qi9DLFNBQVMsQ0FBQzRELFdBQVcsR0FBRztRQUM3QyxxRUFBcUU7UUFDckUsNkJBQTZCO1FBQzdCLElBQUksQ0FBQ3pDLGFBQWEsQ0FBQyxJQUFJLENBQUM2QixVQUFVLEVBQUU7WUFDaEM7UUFDSjtRQUNBNUIsU0FBU3NELG1CQUFtQixDQUFDLGlCQUFpQixJQUFJLENBQUN0QixnQkFBZ0I7UUFDbkUvQixPQUFPcUQsbUJBQW1CLENBQUMsVUFBVSxJQUFJLENBQUNyQixPQUFPO1FBQ2pELElBQUksSUFBSSxDQUFDSCxrQkFBa0IsRUFBRTtZQUN6QixJQUFJLENBQUNBLGtCQUFrQixDQUFDeUIsVUFBVTtRQUN0QztRQUNBLElBQUksSUFBSSxDQUFDMUIsb0JBQW9CLEVBQUU7WUFDM0I3QixTQUFTc0QsbUJBQW1CLENBQUMsc0JBQXNCLElBQUksQ0FBQ3JCLE9BQU87UUFDbkU7UUFDQSxJQUFJLENBQUNILGtCQUFrQixHQUFHO1FBQzFCLElBQUksQ0FBQ0Qsb0JBQW9CLEdBQUc7UUFDNUIsSUFBSSxDQUFDRCxVQUFVLEdBQUc7SUFDdEI7SUFDQTs7Ozs7O0tBTUMsR0FDREQseUJBQXlCL0MsU0FBUyxDQUFDb0QsZ0JBQWdCLEdBQUcsU0FBVW5DLEVBQUU7UUFDOUQsSUFBSTJELEtBQUszRCxHQUFHNEQsWUFBWSxFQUFFQSxlQUFlRCxPQUFPLEtBQUssSUFBSSxLQUFLQTtRQUM5RCxpRUFBaUU7UUFDakUsSUFBSUUsbUJBQW1CbEMsZUFBZW5ELElBQUksQ0FBQyxTQUFVRixHQUFHO1lBQ3BELE9BQU8sQ0FBQyxDQUFDLENBQUNzRixhQUFhckIsT0FBTyxDQUFDakU7UUFDbkM7UUFDQSxJQUFJdUYsa0JBQWtCO1lBQ2xCLElBQUksQ0FBQ3pCLE9BQU87UUFDaEI7SUFDSjtJQUNBOzs7O0tBSUMsR0FDRE4seUJBQXlCZ0MsV0FBVyxHQUFHO1FBQ25DLElBQUksQ0FBQyxJQUFJLENBQUNDLFNBQVMsRUFBRTtZQUNqQixJQUFJLENBQUNBLFNBQVMsR0FBRyxJQUFJakM7UUFDekI7UUFDQSxPQUFPLElBQUksQ0FBQ2lDLFNBQVM7SUFDekI7SUFDQTs7OztLQUlDLEdBQ0RqQyx5QkFBeUJpQyxTQUFTLEdBQUc7SUFDckMsT0FBT2pDO0FBQ1g7QUFFQTs7Ozs7O0NBTUMsR0FDRCxJQUFJa0MscUJBQXNCLFNBQVVDLE1BQU0sRUFBRUMsS0FBSztJQUM3QyxJQUFLLElBQUluRSxLQUFLLEdBQUdDLEtBQUtuQixPQUFPc0YsSUFBSSxDQUFDRCxRQUFRbkUsS0FBS0MsR0FBR2YsTUFBTSxFQUFFYyxLQUFNO1FBQzVELElBQUl6QixNQUFNMEIsRUFBRSxDQUFDRCxHQUFHO1FBQ2hCbEIsT0FBT0MsY0FBYyxDQUFDbUYsUUFBUTNGLEtBQUs7WUFDL0JlLE9BQU82RSxLQUFLLENBQUM1RixJQUFJO1lBQ2pCWSxZQUFZO1lBQ1prRixVQUFVO1lBQ1ZqRixjQUFjO1FBQ2xCO0lBQ0o7SUFDQSxPQUFPOEU7QUFDWDtBQUVBOzs7OztDQUtDLEdBQ0QsSUFBSUksY0FBZSxTQUFVSixNQUFNO0lBQy9CLHNFQUFzRTtJQUN0RSxnRUFBZ0U7SUFDaEUsK0JBQStCO0lBQy9CLElBQUlLLGNBQWNMLFVBQVVBLE9BQU9NLGFBQWEsSUFBSU4sT0FBT00sYUFBYSxDQUFDQyxXQUFXO0lBQ3BGLHVFQUF1RTtJQUN2RSxvQkFBb0I7SUFDcEIsT0FBT0YsZUFBZWpFO0FBQzFCO0FBRUEsNkNBQTZDO0FBQzdDLElBQUlvRSxZQUFZQyxlQUFlLEdBQUcsR0FBRyxHQUFHO0FBQ3hDOzs7OztDQUtDLEdBQ0QsU0FBU0MsUUFBUXRGLEtBQUs7SUFDbEIsT0FBT3VGLFdBQVd2RixVQUFVO0FBQ2hDO0FBQ0E7Ozs7OztDQU1DLEdBQ0QsU0FBU3dGLGVBQWVDLE1BQU07SUFDMUIsSUFBSUMsWUFBWSxFQUFFO0lBQ2xCLElBQUssSUFBSWhGLEtBQUssR0FBR0EsS0FBS2lGLFVBQVUvRixNQUFNLEVBQUVjLEtBQU07UUFDMUNnRixTQUFTLENBQUNoRixLQUFLLEVBQUUsR0FBR2lGLFNBQVMsQ0FBQ2pGLEdBQUc7SUFDckM7SUFDQSxPQUFPZ0YsVUFBVUUsTUFBTSxDQUFDLFNBQVVDLElBQUksRUFBRUMsUUFBUTtRQUM1QyxJQUFJOUYsUUFBUXlGLE1BQU0sQ0FBQyxZQUFZSyxXQUFXLFNBQVM7UUFDbkQsT0FBT0QsT0FBT1AsUUFBUXRGO0lBQzFCLEdBQUc7QUFDUDtBQUNBOzs7OztDQUtDLEdBQ0QsU0FBUytGLFlBQVlOLE1BQU07SUFDdkIsSUFBSUMsWUFBWTtRQUFDO1FBQU87UUFBUztRQUFVO0tBQU87SUFDbEQsSUFBSU0sV0FBVyxDQUFDO0lBQ2hCLElBQUssSUFBSXRGLEtBQUssR0FBR3VGLGNBQWNQLFdBQVdoRixLQUFLdUYsWUFBWXJHLE1BQU0sRUFBRWMsS0FBTTtRQUNyRSxJQUFJb0YsV0FBV0csV0FBVyxDQUFDdkYsR0FBRztRQUM5QixJQUFJVixRQUFReUYsTUFBTSxDQUFDLGFBQWFLLFNBQVM7UUFDekNFLFFBQVEsQ0FBQ0YsU0FBUyxHQUFHUixRQUFRdEY7SUFDakM7SUFDQSxPQUFPZ0c7QUFDWDtBQUNBOzs7Ozs7Q0FNQyxHQUNELFNBQVNFLGtCQUFrQnRCLE1BQU07SUFDN0IsSUFBSXVCLE9BQU92QixPQUFPd0IsT0FBTztJQUN6QixPQUFPZixlQUFlLEdBQUcsR0FBR2MsS0FBS0UsS0FBSyxFQUFFRixLQUFLRyxNQUFNO0FBQ3ZEO0FBQ0E7Ozs7O0NBS0MsR0FDRCxTQUFTQywwQkFBMEIzQixNQUFNO0lBQ3JDLDRDQUE0QztJQUM1QyxtREFBbUQ7SUFDbkQsSUFBSTRCLGNBQWM1QixPQUFPNEIsV0FBVyxFQUFFQyxlQUFlN0IsT0FBTzZCLFlBQVk7SUFDeEUscUVBQXFFO0lBQ3JFLHlFQUF5RTtJQUN6RSxzQ0FBc0M7SUFDdEMsRUFBRTtJQUNGLHFFQUFxRTtJQUNyRSx5RUFBeUU7SUFDekUsMkVBQTJFO0lBQzNFLHNFQUFzRTtJQUN0RSxJQUFJLENBQUNELGVBQWUsQ0FBQ0MsY0FBYztRQUMvQixPQUFPckI7SUFDWDtJQUNBLElBQUlLLFNBQVNULFlBQVlKLFFBQVE4QixnQkFBZ0IsQ0FBQzlCO0lBQ2xELElBQUlvQixXQUFXRCxZQUFZTjtJQUMzQixJQUFJa0IsV0FBV1gsU0FBU1ksSUFBSSxHQUFHWixTQUFTYSxLQUFLO0lBQzdDLElBQUlDLFVBQVVkLFNBQVNlLEdBQUcsR0FBR2YsU0FBU2dCLE1BQU07SUFDNUMsd0VBQXdFO0lBQ3hFLDRFQUE0RTtJQUM1RSw0RUFBNEU7SUFDNUUsK0VBQStFO0lBQy9FLElBQUlYLFFBQVFmLFFBQVFHLE9BQU9ZLEtBQUssR0FBR0MsU0FBU2hCLFFBQVFHLE9BQU9hLE1BQU07SUFDakUsd0VBQXdFO0lBQ3hFLG9DQUFvQztJQUNwQyxJQUFJYixPQUFPd0IsU0FBUyxLQUFLLGNBQWM7UUFDbkMsc0VBQXNFO1FBQ3RFLG1FQUFtRTtRQUNuRSxFQUFFO1FBQ0YseUVBQXlFO1FBQ3pFLHFFQUFxRTtRQUNyRSx1RUFBdUU7UUFDdkUsSUFBSS9GLEtBQUtnRyxLQUFLLENBQUNiLFFBQVFNLGNBQWNILGFBQWE7WUFDOUNILFNBQVNiLGVBQWVDLFFBQVEsUUFBUSxXQUFXa0I7UUFDdkQ7UUFDQSxJQUFJekYsS0FBS2dHLEtBQUssQ0FBQ1osU0FBU1EsYUFBYUwsY0FBYztZQUMvQ0gsVUFBVWQsZUFBZUMsUUFBUSxPQUFPLFlBQVlxQjtRQUN4RDtJQUNKO0lBQ0EseUVBQXlFO0lBQ3pFLHlFQUF5RTtJQUN6RSx1RUFBdUU7SUFDdkUsOENBQThDO0lBQzlDLElBQUksQ0FBQ0ssa0JBQWtCdkMsU0FBUztRQUM1QixrRUFBa0U7UUFDbEUsdUVBQXVFO1FBQ3ZFLHFFQUFxRTtRQUNyRSxxRUFBcUU7UUFDckUsSUFBSXdDLGdCQUFnQmxHLEtBQUtnRyxLQUFLLENBQUNiLFFBQVFNLFlBQVlIO1FBQ25ELElBQUlhLGlCQUFpQm5HLEtBQUtnRyxLQUFLLENBQUNaLFNBQVNRLFdBQVdMO1FBQ3BELDZEQUE2RDtRQUM3RCx1RUFBdUU7UUFDdkUscUVBQXFFO1FBQ3JFLHVFQUF1RTtRQUN2RSxxQkFBcUI7UUFDckIsSUFBSXZGLEtBQUtvRyxHQUFHLENBQUNGLG1CQUFtQixHQUFHO1lBQy9CZixTQUFTZTtRQUNiO1FBQ0EsSUFBSWxHLEtBQUtvRyxHQUFHLENBQUNELG9CQUFvQixHQUFHO1lBQ2hDZixVQUFVZTtRQUNkO0lBQ0o7SUFDQSxPQUFPaEMsZUFBZVcsU0FBU1ksSUFBSSxFQUFFWixTQUFTZSxHQUFHLEVBQUVWLE9BQU9DO0FBQzlEO0FBQ0E7Ozs7O0NBS0MsR0FDRCxJQUFJaUIsdUJBQXVCO0lBQ3ZCLHVFQUF1RTtJQUN2RSxhQUFhO0lBQ2IsSUFBSSxPQUFPQyx1QkFBdUIsYUFBYTtRQUMzQyxPQUFPLFNBQVU1QyxNQUFNO1lBQUksT0FBT0Esa0JBQWtCSSxZQUFZSixRQUFRNEMsa0JBQWtCO1FBQUU7SUFDaEc7SUFDQSxxRUFBcUU7SUFDckUsbURBQW1EO0lBQ25ELDJDQUEyQztJQUMzQyxPQUFPLFNBQVU1QyxNQUFNO1FBQUksT0FBUUEsa0JBQWtCSSxZQUFZSixRQUFRNkMsVUFBVSxJQUMvRSxPQUFPN0MsT0FBT3dCLE9BQU8sS0FBSztJQUFhO0FBQy9DO0FBQ0E7Ozs7O0NBS0MsR0FDRCxTQUFTZSxrQkFBa0J2QyxNQUFNO0lBQzdCLE9BQU9BLFdBQVdJLFlBQVlKLFFBQVE5RCxRQUFRLENBQUM0RyxlQUFlO0FBQ2xFO0FBQ0E7Ozs7O0NBS0MsR0FDRCxTQUFTQyxlQUFlL0MsTUFBTTtJQUMxQixJQUFJLENBQUMvRCxXQUFXO1FBQ1osT0FBT3VFO0lBQ1g7SUFDQSxJQUFJbUMscUJBQXFCM0MsU0FBUztRQUM5QixPQUFPc0Isa0JBQWtCdEI7SUFDN0I7SUFDQSxPQUFPMkIsMEJBQTBCM0I7QUFDckM7QUFDQTs7Ozs7O0NBTUMsR0FDRCxTQUFTZ0QsbUJBQW1CakgsRUFBRTtJQUMxQixJQUFJa0gsSUFBSWxILEdBQUdrSCxDQUFDLEVBQUVDLElBQUluSCxHQUFHbUgsQ0FBQyxFQUFFekIsUUFBUTFGLEdBQUcwRixLQUFLLEVBQUVDLFNBQVMzRixHQUFHMkYsTUFBTTtJQUM1RCwyRUFBMkU7SUFDM0UsSUFBSXlCLFNBQVMsT0FBT0Msb0JBQW9CLGNBQWNBLGtCQUFrQnhJO0lBQ3hFLElBQUl5SSxPQUFPekksT0FBTzBJLE1BQU0sQ0FBQ0gsT0FBT3JJLFNBQVM7SUFDekMsOERBQThEO0lBQzlEaUYsbUJBQW1Cc0QsTUFBTTtRQUNyQkosR0FBR0E7UUFBR0MsR0FBR0E7UUFBR3pCLE9BQU9BO1FBQU9DLFFBQVFBO1FBQ2xDUyxLQUFLZTtRQUNMakIsT0FBT2dCLElBQUl4QjtRQUNYVyxRQUFRVixTQUFTd0I7UUFDakJsQixNQUFNaUI7SUFDVjtJQUNBLE9BQU9JO0FBQ1g7QUFDQTs7Ozs7Ozs7O0NBU0MsR0FDRCxTQUFTNUMsZUFBZXdDLENBQUMsRUFBRUMsQ0FBQyxFQUFFekIsS0FBSyxFQUFFQyxNQUFNO0lBQ3ZDLE9BQU87UUFBRXVCLEdBQUdBO1FBQUdDLEdBQUdBO1FBQUd6QixPQUFPQTtRQUFPQyxRQUFRQTtJQUFPO0FBQ3REO0FBRUE7OztDQUdDLEdBQ0QsSUFBSTZCLG9CQUFvQixXQUFXLEdBQUk7SUFDbkM7Ozs7S0FJQyxHQUNELFNBQVNBLGtCQUFrQnZELE1BQU07UUFDN0I7Ozs7U0FJQyxHQUNELElBQUksQ0FBQ3dELGNBQWMsR0FBRztRQUN0Qjs7OztTQUlDLEdBQ0QsSUFBSSxDQUFDQyxlQUFlLEdBQUc7UUFDdkI7Ozs7U0FJQyxHQUNELElBQUksQ0FBQ0MsWUFBWSxHQUFHakQsZUFBZSxHQUFHLEdBQUcsR0FBRztRQUM1QyxJQUFJLENBQUNULE1BQU0sR0FBR0E7SUFDbEI7SUFDQTs7Ozs7S0FLQyxHQUNEdUQsa0JBQWtCekksU0FBUyxDQUFDNkksUUFBUSxHQUFHO1FBQ25DLElBQUlOLE9BQU9OLGVBQWUsSUFBSSxDQUFDL0MsTUFBTTtRQUNyQyxJQUFJLENBQUMwRCxZQUFZLEdBQUdMO1FBQ3BCLE9BQVFBLEtBQUs1QixLQUFLLEtBQUssSUFBSSxDQUFDK0IsY0FBYyxJQUN0Q0gsS0FBSzNCLE1BQU0sS0FBSyxJQUFJLENBQUMrQixlQUFlO0lBQzVDO0lBQ0E7Ozs7O0tBS0MsR0FDREYsa0JBQWtCekksU0FBUyxDQUFDOEksYUFBYSxHQUFHO1FBQ3hDLElBQUlQLE9BQU8sSUFBSSxDQUFDSyxZQUFZO1FBQzVCLElBQUksQ0FBQ0YsY0FBYyxHQUFHSCxLQUFLNUIsS0FBSztRQUNoQyxJQUFJLENBQUNnQyxlQUFlLEdBQUdKLEtBQUszQixNQUFNO1FBQ2xDLE9BQU8yQjtJQUNYO0lBQ0EsT0FBT0U7QUFDWDtBQUVBLElBQUlNLHNCQUFzQixXQUFXLEdBQUk7SUFDckM7Ozs7O0tBS0MsR0FDRCxTQUFTQSxvQkFBb0I3RCxNQUFNLEVBQUU4RCxRQUFRO1FBQ3pDLElBQUlDLGNBQWNmLG1CQUFtQmM7UUFDckMsdUVBQXVFO1FBQ3ZFLDREQUE0RDtRQUM1RCxFQUFFO1FBQ0Ysc0VBQXNFO1FBQ3RFLHdFQUF3RTtRQUN4RSwwQ0FBMEM7UUFDMUMvRCxtQkFBbUIsSUFBSSxFQUFFO1lBQUVDLFFBQVFBO1lBQVErRCxhQUFhQTtRQUFZO0lBQ3hFO0lBQ0EsT0FBT0Y7QUFDWDtBQUVBLElBQUlHLG9CQUFvQixXQUFXLEdBQUk7SUFDbkM7Ozs7Ozs7OztLQVNDLEdBQ0QsU0FBU0Esa0JBQWtCcEksUUFBUSxFQUFFcUksVUFBVSxFQUFFQyxXQUFXO1FBQ3hEOzs7OztTQUtDLEdBQ0QsSUFBSSxDQUFDQyxtQkFBbUIsR0FBRyxFQUFFO1FBQzdCOzs7O1NBSUMsR0FDRCxJQUFJLENBQUNDLGFBQWEsR0FBRyxJQUFJbks7UUFDekIsSUFBSSxPQUFPMkIsYUFBYSxZQUFZO1lBQ2hDLE1BQU0sSUFBSXlJLFVBQVU7UUFDeEI7UUFDQSxJQUFJLENBQUNDLFNBQVMsR0FBRzFJO1FBQ2pCLElBQUksQ0FBQzJJLFdBQVcsR0FBR047UUFDbkIsSUFBSSxDQUFDTyxZQUFZLEdBQUdOO0lBQ3hCO0lBQ0E7Ozs7O0tBS0MsR0FDREYsa0JBQWtCbEosU0FBUyxDQUFDcUUsT0FBTyxHQUFHLFNBQVVhLE1BQU07UUFDbEQsSUFBSSxDQUFDZSxVQUFVL0YsTUFBTSxFQUFFO1lBQ25CLE1BQU0sSUFBSXFKLFVBQVU7UUFDeEI7UUFDQSx3RUFBd0U7UUFDeEUsSUFBSSxPQUFPSSxZQUFZLGVBQWUsQ0FBRUEsQ0FBQUEsbUJBQW1CN0osTUFBSyxHQUFJO1lBQ2hFO1FBQ0o7UUFDQSxJQUFJLENBQUVvRixDQUFBQSxrQkFBa0JJLFlBQVlKLFFBQVF5RSxPQUFPLEdBQUc7WUFDbEQsTUFBTSxJQUFJSixVQUFVO1FBQ3hCO1FBQ0EsSUFBSUssZUFBZSxJQUFJLENBQUNOLGFBQWE7UUFDckMsbURBQW1EO1FBQ25ELElBQUlNLGFBQWFqSixHQUFHLENBQUN1RSxTQUFTO1lBQzFCO1FBQ0o7UUFDQTBFLGFBQWF2SixHQUFHLENBQUM2RSxRQUFRLElBQUl1RCxrQkFBa0J2RDtRQUMvQyxJQUFJLENBQUN1RSxXQUFXLENBQUNuRyxXQUFXLENBQUMsSUFBSTtRQUNqQyxvQ0FBb0M7UUFDcEMsSUFBSSxDQUFDbUcsV0FBVyxDQUFDcEcsT0FBTztJQUM1QjtJQUNBOzs7OztLQUtDLEdBQ0Q2RixrQkFBa0JsSixTQUFTLENBQUM2SixTQUFTLEdBQUcsU0FBVTNFLE1BQU07UUFDcEQsSUFBSSxDQUFDZSxVQUFVL0YsTUFBTSxFQUFFO1lBQ25CLE1BQU0sSUFBSXFKLFVBQVU7UUFDeEI7UUFDQSx3RUFBd0U7UUFDeEUsSUFBSSxPQUFPSSxZQUFZLGVBQWUsQ0FBRUEsQ0FBQUEsbUJBQW1CN0osTUFBSyxHQUFJO1lBQ2hFO1FBQ0o7UUFDQSxJQUFJLENBQUVvRixDQUFBQSxrQkFBa0JJLFlBQVlKLFFBQVF5RSxPQUFPLEdBQUc7WUFDbEQsTUFBTSxJQUFJSixVQUFVO1FBQ3hCO1FBQ0EsSUFBSUssZUFBZSxJQUFJLENBQUNOLGFBQWE7UUFDckMsK0NBQStDO1FBQy9DLElBQUksQ0FBQ00sYUFBYWpKLEdBQUcsQ0FBQ3VFLFNBQVM7WUFDM0I7UUFDSjtRQUNBMEUsYUFBYXBKLE1BQU0sQ0FBQzBFO1FBQ3BCLElBQUksQ0FBQzBFLGFBQWF6RCxJQUFJLEVBQUU7WUFDcEIsSUFBSSxDQUFDc0QsV0FBVyxDQUFDL0YsY0FBYyxDQUFDLElBQUk7UUFDeEM7SUFDSjtJQUNBOzs7O0tBSUMsR0FDRHdGLGtCQUFrQmxKLFNBQVMsQ0FBQzJFLFVBQVUsR0FBRztRQUNyQyxJQUFJLENBQUNtRixXQUFXO1FBQ2hCLElBQUksQ0FBQ1IsYUFBYSxDQUFDMUksS0FBSztRQUN4QixJQUFJLENBQUM2SSxXQUFXLENBQUMvRixjQUFjLENBQUMsSUFBSTtJQUN4QztJQUNBOzs7OztLQUtDLEdBQ0R3RixrQkFBa0JsSixTQUFTLENBQUNpRSxZQUFZLEdBQUc7UUFDdkMsSUFBSThGLFFBQVEsSUFBSTtRQUNoQixJQUFJLENBQUNELFdBQVc7UUFDaEIsSUFBSSxDQUFDUixhQUFhLENBQUN6SSxPQUFPLENBQUMsU0FBVW1KLFdBQVc7WUFDNUMsSUFBSUEsWUFBWW5CLFFBQVEsSUFBSTtnQkFDeEJrQixNQUFNVixtQkFBbUIsQ0FBQzlJLElBQUksQ0FBQ3lKO1lBQ25DO1FBQ0o7SUFDSjtJQUNBOzs7OztLQUtDLEdBQ0RkLGtCQUFrQmxKLFNBQVMsQ0FBQ21FLGVBQWUsR0FBRztRQUMxQywyREFBMkQ7UUFDM0QsSUFBSSxDQUFDLElBQUksQ0FBQ0QsU0FBUyxJQUFJO1lBQ25CO1FBQ0o7UUFDQSxJQUFJbkQsTUFBTSxJQUFJLENBQUMySSxZQUFZO1FBQzNCLG9FQUFvRTtRQUNwRSxJQUFJakosVUFBVSxJQUFJLENBQUM0SSxtQkFBbUIsQ0FBQ1ksR0FBRyxDQUFDLFNBQVVELFdBQVc7WUFDNUQsT0FBTyxJQUFJakIsb0JBQW9CaUIsWUFBWTlFLE1BQU0sRUFBRThFLFlBQVlsQixhQUFhO1FBQ2hGO1FBQ0EsSUFBSSxDQUFDVSxTQUFTLENBQUN0SSxJQUFJLENBQUNILEtBQUtOLFNBQVNNO1FBQ2xDLElBQUksQ0FBQytJLFdBQVc7SUFDcEI7SUFDQTs7OztLQUlDLEdBQ0RaLGtCQUFrQmxKLFNBQVMsQ0FBQzhKLFdBQVcsR0FBRztRQUN0QyxJQUFJLENBQUNULG1CQUFtQixDQUFDM0ksTUFBTSxDQUFDO0lBQ3BDO0lBQ0E7Ozs7S0FJQyxHQUNEd0ksa0JBQWtCbEosU0FBUyxDQUFDa0UsU0FBUyxHQUFHO1FBQ3BDLE9BQU8sSUFBSSxDQUFDbUYsbUJBQW1CLENBQUNuSixNQUFNLEdBQUc7SUFDN0M7SUFDQSxPQUFPZ0o7QUFDWDtBQUVBLCtFQUErRTtBQUMvRSw0RUFBNEU7QUFDNUUsb0NBQW9DO0FBQ3BDLElBQUl2RixZQUFZLE9BQU91RyxZQUFZLGNBQWMsSUFBSUEsWUFBWSxJQUFJL0s7QUFDckU7OztDQUdDLEdBQ0QsSUFBSWdMLGlCQUFpQixXQUFXLEdBQUk7SUFDaEM7Ozs7O0tBS0MsR0FDRCxTQUFTQSxlQUFlckosUUFBUTtRQUM1QixJQUFJLENBQUUsS0FBSSxZQUFZcUosY0FBYSxHQUFJO1lBQ25DLE1BQU0sSUFBSVosVUFBVTtRQUN4QjtRQUNBLElBQUksQ0FBQ3RELFVBQVUvRixNQUFNLEVBQUU7WUFDbkIsTUFBTSxJQUFJcUosVUFBVTtRQUN4QjtRQUNBLElBQUlKLGFBQWFwRyx5QkFBeUJnQyxXQUFXO1FBQ3JELElBQUl4QixXQUFXLElBQUkyRixrQkFBa0JwSSxVQUFVcUksWUFBWSxJQUFJO1FBQy9EeEYsVUFBVXRELEdBQUcsQ0FBQyxJQUFJLEVBQUVrRDtJQUN4QjtJQUNBLE9BQU80RztBQUNYO0FBQ0EsMkNBQTJDO0FBQzNDO0lBQ0k7SUFDQTtJQUNBO0NBQ0gsQ0FBQ3RKLE9BQU8sQ0FBQyxTQUFVdUosTUFBTTtJQUN0QkQsZUFBZW5LLFNBQVMsQ0FBQ29LLE9BQU8sR0FBRztRQUMvQixJQUFJbko7UUFDSixPQUFPLENBQUNBLEtBQUswQyxVQUFVMUQsR0FBRyxDQUFDLElBQUksRUFBRSxDQUFDbUssT0FBTyxDQUFDQyxLQUFLLENBQUNwSixJQUFJZ0Y7SUFDeEQ7QUFDSjtBQUVBLElBQUl0RyxRQUFRO0lBQ1IsK0NBQStDO0lBQy9DLElBQUksT0FBTzJCLFNBQVM2SSxjQUFjLEtBQUssYUFBYTtRQUNoRCxPQUFPN0ksU0FBUzZJLGNBQWM7SUFDbEM7SUFDQSxPQUFPQTtBQUNYO0FBRUEsaUVBQWV4SyxLQUFLQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJ1dG9vLy4vbm9kZV9tb2R1bGVzL3Jlc2l6ZS1vYnNlcnZlci1wb2x5ZmlsbC9kaXN0L1Jlc2l6ZU9ic2VydmVyLmVzLmpzP2YzNDciXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXHJcbiAqIEEgY29sbGVjdGlvbiBvZiBzaGltcyB0aGF0IHByb3ZpZGUgbWluaW1hbCBmdW5jdGlvbmFsaXR5IG9mIHRoZSBFUzYgY29sbGVjdGlvbnMuXHJcbiAqXHJcbiAqIFRoZXNlIGltcGxlbWVudGF0aW9ucyBhcmUgbm90IG1lYW50IHRvIGJlIHVzZWQgb3V0c2lkZSBvZiB0aGUgUmVzaXplT2JzZXJ2ZXJcclxuICogbW9kdWxlcyBhcyB0aGV5IGNvdmVyIG9ubHkgYSBsaW1pdGVkIHJhbmdlIG9mIHVzZSBjYXNlcy5cclxuICovXHJcbi8qIGVzbGludC1kaXNhYmxlIHJlcXVpcmUtanNkb2MsIHZhbGlkLWpzZG9jICovXHJcbnZhciBNYXBTaGltID0gKGZ1bmN0aW9uICgpIHtcclxuICAgIGlmICh0eXBlb2YgTWFwICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgIHJldHVybiBNYXA7XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqIFJldHVybnMgaW5kZXggaW4gcHJvdmlkZWQgYXJyYXkgdGhhdCBtYXRjaGVzIHRoZSBzcGVjaWZpZWQga2V5LlxyXG4gICAgICpcclxuICAgICAqIEBwYXJhbSB7QXJyYXk8QXJyYXk+fSBhcnJcclxuICAgICAqIEBwYXJhbSB7Kn0ga2V5XHJcbiAgICAgKiBAcmV0dXJucyB7bnVtYmVyfVxyXG4gICAgICovXHJcbiAgICBmdW5jdGlvbiBnZXRJbmRleChhcnIsIGtleSkge1xyXG4gICAgICAgIHZhciByZXN1bHQgPSAtMTtcclxuICAgICAgICBhcnIuc29tZShmdW5jdGlvbiAoZW50cnksIGluZGV4KSB7XHJcbiAgICAgICAgICAgIGlmIChlbnRyeVswXSA9PT0ga2V5KSB7XHJcbiAgICAgICAgICAgICAgICByZXN1bHQgPSBpbmRleDtcclxuICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgICB9KTtcclxuICAgICAgICByZXR1cm4gcmVzdWx0O1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uICgpIHtcclxuICAgICAgICBmdW5jdGlvbiBjbGFzc18xKCkge1xyXG4gICAgICAgICAgICB0aGlzLl9fZW50cmllc19fID0gW107XHJcbiAgICAgICAgfVxyXG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShjbGFzc18xLnByb3RvdHlwZSwgXCJzaXplXCIsIHtcclxuICAgICAgICAgICAgLyoqXHJcbiAgICAgICAgICAgICAqIEByZXR1cm5zIHtib29sZWFufVxyXG4gICAgICAgICAgICAgKi9cclxuICAgICAgICAgICAgZ2V0OiBmdW5jdGlvbiAoKSB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5fX2VudHJpZXNfXy5sZW5ndGg7XHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXHJcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIC8qKlxyXG4gICAgICAgICAqIEBwYXJhbSB7Kn0ga2V5XHJcbiAgICAgICAgICogQHJldHVybnMgeyp9XHJcbiAgICAgICAgICovXHJcbiAgICAgICAgY2xhc3NfMS5wcm90b3R5cGUuZ2V0ID0gZnVuY3Rpb24gKGtleSkge1xyXG4gICAgICAgICAgICB2YXIgaW5kZXggPSBnZXRJbmRleCh0aGlzLl9fZW50cmllc19fLCBrZXkpO1xyXG4gICAgICAgICAgICB2YXIgZW50cnkgPSB0aGlzLl9fZW50cmllc19fW2luZGV4XTtcclxuICAgICAgICAgICAgcmV0dXJuIGVudHJ5ICYmIGVudHJ5WzFdO1xyXG4gICAgICAgIH07XHJcbiAgICAgICAgLyoqXHJcbiAgICAgICAgICogQHBhcmFtIHsqfSBrZXlcclxuICAgICAgICAgKiBAcGFyYW0geyp9IHZhbHVlXHJcbiAgICAgICAgICogQHJldHVybnMge3ZvaWR9XHJcbiAgICAgICAgICovXHJcbiAgICAgICAgY2xhc3NfMS5wcm90b3R5cGUuc2V0ID0gZnVuY3Rpb24gKGtleSwgdmFsdWUpIHtcclxuICAgICAgICAgICAgdmFyIGluZGV4ID0gZ2V0SW5kZXgodGhpcy5fX2VudHJpZXNfXywga2V5KTtcclxuICAgICAgICAgICAgaWYgKH5pbmRleCkge1xyXG4gICAgICAgICAgICAgICAgdGhpcy5fX2VudHJpZXNfX1tpbmRleF1bMV0gPSB2YWx1ZTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgICAgIHRoaXMuX19lbnRyaWVzX18ucHVzaChba2V5LCB2YWx1ZV0pO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfTtcclxuICAgICAgICAvKipcclxuICAgICAgICAgKiBAcGFyYW0geyp9IGtleVxyXG4gICAgICAgICAqIEByZXR1cm5zIHt2b2lkfVxyXG4gICAgICAgICAqL1xyXG4gICAgICAgIGNsYXNzXzEucHJvdG90eXBlLmRlbGV0ZSA9IGZ1bmN0aW9uIChrZXkpIHtcclxuICAgICAgICAgICAgdmFyIGVudHJpZXMgPSB0aGlzLl9fZW50cmllc19fO1xyXG4gICAgICAgICAgICB2YXIgaW5kZXggPSBnZXRJbmRleChlbnRyaWVzLCBrZXkpO1xyXG4gICAgICAgICAgICBpZiAofmluZGV4KSB7XHJcbiAgICAgICAgICAgICAgICBlbnRyaWVzLnNwbGljZShpbmRleCwgMSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9O1xyXG4gICAgICAgIC8qKlxyXG4gICAgICAgICAqIEBwYXJhbSB7Kn0ga2V5XHJcbiAgICAgICAgICogQHJldHVybnMge3ZvaWR9XHJcbiAgICAgICAgICovXHJcbiAgICAgICAgY2xhc3NfMS5wcm90b3R5cGUuaGFzID0gZnVuY3Rpb24gKGtleSkge1xyXG4gICAgICAgICAgICByZXR1cm4gISF+Z2V0SW5kZXgodGhpcy5fX2VudHJpZXNfXywga2V5KTtcclxuICAgICAgICB9O1xyXG4gICAgICAgIC8qKlxyXG4gICAgICAgICAqIEByZXR1cm5zIHt2b2lkfVxyXG4gICAgICAgICAqL1xyXG4gICAgICAgIGNsYXNzXzEucHJvdG90eXBlLmNsZWFyID0gZnVuY3Rpb24gKCkge1xyXG4gICAgICAgICAgICB0aGlzLl9fZW50cmllc19fLnNwbGljZSgwKTtcclxuICAgICAgICB9O1xyXG4gICAgICAgIC8qKlxyXG4gICAgICAgICAqIEBwYXJhbSB7RnVuY3Rpb259IGNhbGxiYWNrXHJcbiAgICAgICAgICogQHBhcmFtIHsqfSBbY3R4PW51bGxdXHJcbiAgICAgICAgICogQHJldHVybnMge3ZvaWR9XHJcbiAgICAgICAgICovXHJcbiAgICAgICAgY2xhc3NfMS5wcm90b3R5cGUuZm9yRWFjaCA9IGZ1bmN0aW9uIChjYWxsYmFjaywgY3R4KSB7XHJcbiAgICAgICAgICAgIGlmIChjdHggPT09IHZvaWQgMCkgeyBjdHggPSBudWxsOyB9XHJcbiAgICAgICAgICAgIGZvciAodmFyIF9pID0gMCwgX2EgPSB0aGlzLl9fZW50cmllc19fOyBfaSA8IF9hLmxlbmd0aDsgX2krKykge1xyXG4gICAgICAgICAgICAgICAgdmFyIGVudHJ5ID0gX2FbX2ldO1xyXG4gICAgICAgICAgICAgICAgY2FsbGJhY2suY2FsbChjdHgsIGVudHJ5WzFdLCBlbnRyeVswXSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9O1xyXG4gICAgICAgIHJldHVybiBjbGFzc18xO1xyXG4gICAgfSgpKTtcclxufSkoKTtcblxuLyoqXHJcbiAqIERldGVjdHMgd2hldGhlciB3aW5kb3cgYW5kIGRvY3VtZW50IG9iamVjdHMgYXJlIGF2YWlsYWJsZSBpbiBjdXJyZW50IGVudmlyb25tZW50LlxyXG4gKi9cclxudmFyIGlzQnJvd3NlciA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHR5cGVvZiBkb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LmRvY3VtZW50ID09PSBkb2N1bWVudDtcblxuLy8gUmV0dXJucyBnbG9iYWwgb2JqZWN0IG9mIGEgY3VycmVudCBlbnZpcm9ubWVudC5cclxudmFyIGdsb2JhbCQxID0gKGZ1bmN0aW9uICgpIHtcclxuICAgIGlmICh0eXBlb2YgZ2xvYmFsICE9PSAndW5kZWZpbmVkJyAmJiBnbG9iYWwuTWF0aCA9PT0gTWF0aCkge1xyXG4gICAgICAgIHJldHVybiBnbG9iYWw7XHJcbiAgICB9XHJcbiAgICBpZiAodHlwZW9mIHNlbGYgIT09ICd1bmRlZmluZWQnICYmIHNlbGYuTWF0aCA9PT0gTWF0aCkge1xyXG4gICAgICAgIHJldHVybiBzZWxmO1xyXG4gICAgfVxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5NYXRoID09PSBNYXRoKSB7XHJcbiAgICAgICAgcmV0dXJuIHdpbmRvdztcclxuICAgIH1cclxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1uZXctZnVuY1xyXG4gICAgcmV0dXJuIEZ1bmN0aW9uKCdyZXR1cm4gdGhpcycpKCk7XHJcbn0pKCk7XG5cbi8qKlxyXG4gKiBBIHNoaW0gZm9yIHRoZSByZXF1ZXN0QW5pbWF0aW9uRnJhbWUgd2hpY2ggZmFsbHMgYmFjayB0byB0aGUgc2V0VGltZW91dCBpZlxyXG4gKiBmaXJzdCBvbmUgaXMgbm90IHN1cHBvcnRlZC5cclxuICpcclxuICogQHJldHVybnMge251bWJlcn0gUmVxdWVzdHMnIGlkZW50aWZpZXIuXHJcbiAqL1xyXG52YXIgcmVxdWVzdEFuaW1hdGlvbkZyYW1lJDEgPSAoZnVuY3Rpb24gKCkge1xyXG4gICAgaWYgKHR5cGVvZiByZXF1ZXN0QW5pbWF0aW9uRnJhbWUgPT09ICdmdW5jdGlvbicpIHtcclxuICAgICAgICAvLyBJdCdzIHJlcXVpcmVkIHRvIHVzZSBhIGJvdW5kZWQgZnVuY3Rpb24gYmVjYXVzZSBJRSBzb21ldGltZXMgdGhyb3dzXHJcbiAgICAgICAgLy8gYW4gXCJJbnZhbGlkIGNhbGxpbmcgb2JqZWN0XCIgZXJyb3IgaWYgckFGIGlzIGludm9rZWQgd2l0aG91dCB0aGUgZ2xvYmFsXHJcbiAgICAgICAgLy8gb2JqZWN0IG9uIHRoZSBsZWZ0IGhhbmQgc2lkZS5cclxuICAgICAgICByZXR1cm4gcmVxdWVzdEFuaW1hdGlvbkZyYW1lLmJpbmQoZ2xvYmFsJDEpO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIGZ1bmN0aW9uIChjYWxsYmFjaykgeyByZXR1cm4gc2V0VGltZW91dChmdW5jdGlvbiAoKSB7IHJldHVybiBjYWxsYmFjayhEYXRlLm5vdygpKTsgfSwgMTAwMCAvIDYwKTsgfTtcclxufSkoKTtcblxuLy8gRGVmaW5lcyBtaW5pbXVtIHRpbWVvdXQgYmVmb3JlIGFkZGluZyBhIHRyYWlsaW5nIGNhbGwuXHJcbnZhciB0cmFpbGluZ1RpbWVvdXQgPSAyO1xyXG4vKipcclxuICogQ3JlYXRlcyBhIHdyYXBwZXIgZnVuY3Rpb24gd2hpY2ggZW5zdXJlcyB0aGF0IHByb3ZpZGVkIGNhbGxiYWNrIHdpbGwgYmVcclxuICogaW52b2tlZCBvbmx5IG9uY2UgZHVyaW5nIHRoZSBzcGVjaWZpZWQgZGVsYXkgcGVyaW9kLlxyXG4gKlxyXG4gKiBAcGFyYW0ge0Z1bmN0aW9ufSBjYWxsYmFjayAtIEZ1bmN0aW9uIHRvIGJlIGludm9rZWQgYWZ0ZXIgdGhlIGRlbGF5IHBlcmlvZC5cclxuICogQHBhcmFtIHtudW1iZXJ9IGRlbGF5IC0gRGVsYXkgYWZ0ZXIgd2hpY2ggdG8gaW52b2tlIGNhbGxiYWNrLlxyXG4gKiBAcmV0dXJucyB7RnVuY3Rpb259XHJcbiAqL1xyXG5mdW5jdGlvbiB0aHJvdHRsZSAoY2FsbGJhY2ssIGRlbGF5KSB7XHJcbiAgICB2YXIgbGVhZGluZ0NhbGwgPSBmYWxzZSwgdHJhaWxpbmdDYWxsID0gZmFsc2UsIGxhc3RDYWxsVGltZSA9IDA7XHJcbiAgICAvKipcclxuICAgICAqIEludm9rZXMgdGhlIG9yaWdpbmFsIGNhbGxiYWNrIGZ1bmN0aW9uIGFuZCBzY2hlZHVsZXMgbmV3IGludm9jYXRpb24gaWZcclxuICAgICAqIHRoZSBcInByb3h5XCIgd2FzIGNhbGxlZCBkdXJpbmcgY3VycmVudCByZXF1ZXN0LlxyXG4gICAgICpcclxuICAgICAqIEByZXR1cm5zIHt2b2lkfVxyXG4gICAgICovXHJcbiAgICBmdW5jdGlvbiByZXNvbHZlUGVuZGluZygpIHtcclxuICAgICAgICBpZiAobGVhZGluZ0NhbGwpIHtcclxuICAgICAgICAgICAgbGVhZGluZ0NhbGwgPSBmYWxzZTtcclxuICAgICAgICAgICAgY2FsbGJhY2soKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHRyYWlsaW5nQ2FsbCkge1xyXG4gICAgICAgICAgICBwcm94eSgpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIC8qKlxyXG4gICAgICogQ2FsbGJhY2sgaW52b2tlZCBhZnRlciB0aGUgc3BlY2lmaWVkIGRlbGF5LiBJdCB3aWxsIGZ1cnRoZXIgcG9zdHBvbmVcclxuICAgICAqIGludm9jYXRpb24gb2YgdGhlIG9yaWdpbmFsIGZ1bmN0aW9uIGRlbGVnYXRpbmcgaXQgdG8gdGhlXHJcbiAgICAgKiByZXF1ZXN0QW5pbWF0aW9uRnJhbWUuXHJcbiAgICAgKlxyXG4gICAgICogQHJldHVybnMge3ZvaWR9XHJcbiAgICAgKi9cclxuICAgIGZ1bmN0aW9uIHRpbWVvdXRDYWxsYmFjaygpIHtcclxuICAgICAgICByZXF1ZXN0QW5pbWF0aW9uRnJhbWUkMShyZXNvbHZlUGVuZGluZyk7XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqIFNjaGVkdWxlcyBpbnZvY2F0aW9uIG9mIHRoZSBvcmlnaW5hbCBmdW5jdGlvbi5cclxuICAgICAqXHJcbiAgICAgKiBAcmV0dXJucyB7dm9pZH1cclxuICAgICAqL1xyXG4gICAgZnVuY3Rpb24gcHJveHkoKSB7XHJcbiAgICAgICAgdmFyIHRpbWVTdGFtcCA9IERhdGUubm93KCk7XHJcbiAgICAgICAgaWYgKGxlYWRpbmdDYWxsKSB7XHJcbiAgICAgICAgICAgIC8vIFJlamVjdCBpbW1lZGlhdGVseSBmb2xsb3dpbmcgY2FsbHMuXHJcbiAgICAgICAgICAgIGlmICh0aW1lU3RhbXAgLSBsYXN0Q2FsbFRpbWUgPCB0cmFpbGluZ1RpbWVvdXQpIHtcclxuICAgICAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAvLyBTY2hlZHVsZSBuZXcgY2FsbCB0byBiZSBpbiBpbnZva2VkIHdoZW4gdGhlIHBlbmRpbmcgb25lIGlzIHJlc29sdmVkLlxyXG4gICAgICAgICAgICAvLyBUaGlzIGlzIGltcG9ydGFudCBmb3IgXCJ0cmFuc2l0aW9uc1wiIHdoaWNoIG5ldmVyIGFjdHVhbGx5IHN0YXJ0XHJcbiAgICAgICAgICAgIC8vIGltbWVkaWF0ZWx5IHNvIHRoZXJlIGlzIGEgY2hhbmNlIHRoYXQgd2UgbWlnaHQgbWlzcyBvbmUgaWYgY2hhbmdlXHJcbiAgICAgICAgICAgIC8vIGhhcHBlbnMgYW1pZHMgdGhlIHBlbmRpbmcgaW52b2NhdGlvbi5cclxuICAgICAgICAgICAgdHJhaWxpbmdDYWxsID0gdHJ1ZTtcclxuICAgICAgICB9XHJcbiAgICAgICAgZWxzZSB7XHJcbiAgICAgICAgICAgIGxlYWRpbmdDYWxsID0gdHJ1ZTtcclxuICAgICAgICAgICAgdHJhaWxpbmdDYWxsID0gZmFsc2U7XHJcbiAgICAgICAgICAgIHNldFRpbWVvdXQodGltZW91dENhbGxiYWNrLCBkZWxheSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGxhc3RDYWxsVGltZSA9IHRpbWVTdGFtcDtcclxuICAgIH1cclxuICAgIHJldHVybiBwcm94eTtcclxufVxuXG4vLyBNaW5pbXVtIGRlbGF5IGJlZm9yZSBpbnZva2luZyB0aGUgdXBkYXRlIG9mIG9ic2VydmVycy5cclxudmFyIFJFRlJFU0hfREVMQVkgPSAyMDtcclxuLy8gQSBsaXN0IG9mIHN1YnN0cmluZ3Mgb2YgQ1NTIHByb3BlcnRpZXMgdXNlZCB0byBmaW5kIHRyYW5zaXRpb24gZXZlbnRzIHRoYXRcclxuLy8gbWlnaHQgYWZmZWN0IGRpbWVuc2lvbnMgb2Ygb2JzZXJ2ZWQgZWxlbWVudHMuXHJcbnZhciB0cmFuc2l0aW9uS2V5cyA9IFsndG9wJywgJ3JpZ2h0JywgJ2JvdHRvbScsICdsZWZ0JywgJ3dpZHRoJywgJ2hlaWdodCcsICdzaXplJywgJ3dlaWdodCddO1xyXG4vLyBDaGVjayBpZiBNdXRhdGlvbk9ic2VydmVyIGlzIGF2YWlsYWJsZS5cclxudmFyIG11dGF0aW9uT2JzZXJ2ZXJTdXBwb3J0ZWQgPSB0eXBlb2YgTXV0YXRpb25PYnNlcnZlciAhPT0gJ3VuZGVmaW5lZCc7XHJcbi8qKlxyXG4gKiBTaW5nbGV0b24gY29udHJvbGxlciBjbGFzcyB3aGljaCBoYW5kbGVzIHVwZGF0ZXMgb2YgUmVzaXplT2JzZXJ2ZXIgaW5zdGFuY2VzLlxyXG4gKi9cclxudmFyIFJlc2l6ZU9ic2VydmVyQ29udHJvbGxlciA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uICgpIHtcclxuICAgIC8qKlxyXG4gICAgICogQ3JlYXRlcyBhIG5ldyBpbnN0YW5jZSBvZiBSZXNpemVPYnNlcnZlckNvbnRyb2xsZXIuXHJcbiAgICAgKlxyXG4gICAgICogQHByaXZhdGVcclxuICAgICAqL1xyXG4gICAgZnVuY3Rpb24gUmVzaXplT2JzZXJ2ZXJDb250cm9sbGVyKCkge1xyXG4gICAgICAgIC8qKlxyXG4gICAgICAgICAqIEluZGljYXRlcyB3aGV0aGVyIERPTSBsaXN0ZW5lcnMgaGF2ZSBiZWVuIGFkZGVkLlxyXG4gICAgICAgICAqXHJcbiAgICAgICAgICogQHByaXZhdGUge2Jvb2xlYW59XHJcbiAgICAgICAgICovXHJcbiAgICAgICAgdGhpcy5jb25uZWN0ZWRfID0gZmFsc2U7XHJcbiAgICAgICAgLyoqXHJcbiAgICAgICAgICogVGVsbHMgdGhhdCBjb250cm9sbGVyIGhhcyBzdWJzY3JpYmVkIGZvciBNdXRhdGlvbiBFdmVudHMuXHJcbiAgICAgICAgICpcclxuICAgICAgICAgKiBAcHJpdmF0ZSB7Ym9vbGVhbn1cclxuICAgICAgICAgKi9cclxuICAgICAgICB0aGlzLm11dGF0aW9uRXZlbnRzQWRkZWRfID0gZmFsc2U7XHJcbiAgICAgICAgLyoqXHJcbiAgICAgICAgICogS2VlcHMgcmVmZXJlbmNlIHRvIHRoZSBpbnN0YW5jZSBvZiBNdXRhdGlvbk9ic2VydmVyLlxyXG4gICAgICAgICAqXHJcbiAgICAgICAgICogQHByaXZhdGUge011dGF0aW9uT2JzZXJ2ZXJ9XHJcbiAgICAgICAgICovXHJcbiAgICAgICAgdGhpcy5tdXRhdGlvbnNPYnNlcnZlcl8gPSBudWxsO1xyXG4gICAgICAgIC8qKlxyXG4gICAgICAgICAqIEEgbGlzdCBvZiBjb25uZWN0ZWQgb2JzZXJ2ZXJzLlxyXG4gICAgICAgICAqXHJcbiAgICAgICAgICogQHByaXZhdGUge0FycmF5PFJlc2l6ZU9ic2VydmVyU1BJPn1cclxuICAgICAgICAgKi9cclxuICAgICAgICB0aGlzLm9ic2VydmVyc18gPSBbXTtcclxuICAgICAgICB0aGlzLm9uVHJhbnNpdGlvbkVuZF8gPSB0aGlzLm9uVHJhbnNpdGlvbkVuZF8uYmluZCh0aGlzKTtcclxuICAgICAgICB0aGlzLnJlZnJlc2ggPSB0aHJvdHRsZSh0aGlzLnJlZnJlc2guYmluZCh0aGlzKSwgUkVGUkVTSF9ERUxBWSk7XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqIEFkZHMgb2JzZXJ2ZXIgdG8gb2JzZXJ2ZXJzIGxpc3QuXHJcbiAgICAgKlxyXG4gICAgICogQHBhcmFtIHtSZXNpemVPYnNlcnZlclNQSX0gb2JzZXJ2ZXIgLSBPYnNlcnZlciB0byBiZSBhZGRlZC5cclxuICAgICAqIEByZXR1cm5zIHt2b2lkfVxyXG4gICAgICovXHJcbiAgICBSZXNpemVPYnNlcnZlckNvbnRyb2xsZXIucHJvdG90eXBlLmFkZE9ic2VydmVyID0gZnVuY3Rpb24gKG9ic2VydmVyKSB7XHJcbiAgICAgICAgaWYgKCF+dGhpcy5vYnNlcnZlcnNfLmluZGV4T2Yob2JzZXJ2ZXIpKSB7XHJcbiAgICAgICAgICAgIHRoaXMub2JzZXJ2ZXJzXy5wdXNoKG9ic2VydmVyKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgLy8gQWRkIGxpc3RlbmVycyBpZiB0aGV5IGhhdmVuJ3QgYmVlbiBhZGRlZCB5ZXQuXHJcbiAgICAgICAgaWYgKCF0aGlzLmNvbm5lY3RlZF8pIHtcclxuICAgICAgICAgICAgdGhpcy5jb25uZWN0XygpO1xyXG4gICAgICAgIH1cclxuICAgIH07XHJcbiAgICAvKipcclxuICAgICAqIFJlbW92ZXMgb2JzZXJ2ZXIgZnJvbSBvYnNlcnZlcnMgbGlzdC5cclxuICAgICAqXHJcbiAgICAgKiBAcGFyYW0ge1Jlc2l6ZU9ic2VydmVyU1BJfSBvYnNlcnZlciAtIE9ic2VydmVyIHRvIGJlIHJlbW92ZWQuXHJcbiAgICAgKiBAcmV0dXJucyB7dm9pZH1cclxuICAgICAqL1xyXG4gICAgUmVzaXplT2JzZXJ2ZXJDb250cm9sbGVyLnByb3RvdHlwZS5yZW1vdmVPYnNlcnZlciA9IGZ1bmN0aW9uIChvYnNlcnZlcikge1xyXG4gICAgICAgIHZhciBvYnNlcnZlcnMgPSB0aGlzLm9ic2VydmVyc187XHJcbiAgICAgICAgdmFyIGluZGV4ID0gb2JzZXJ2ZXJzLmluZGV4T2Yob2JzZXJ2ZXIpO1xyXG4gICAgICAgIC8vIFJlbW92ZSBvYnNlcnZlciBpZiBpdCdzIHByZXNlbnQgaW4gcmVnaXN0cnkuXHJcbiAgICAgICAgaWYgKH5pbmRleCkge1xyXG4gICAgICAgICAgICBvYnNlcnZlcnMuc3BsaWNlKGluZGV4LCAxKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgLy8gUmVtb3ZlIGxpc3RlbmVycyBpZiBjb250cm9sbGVyIGhhcyBubyBjb25uZWN0ZWQgb2JzZXJ2ZXJzLlxyXG4gICAgICAgIGlmICghb2JzZXJ2ZXJzLmxlbmd0aCAmJiB0aGlzLmNvbm5lY3RlZF8pIHtcclxuICAgICAgICAgICAgdGhpcy5kaXNjb25uZWN0XygpO1xyXG4gICAgICAgIH1cclxuICAgIH07XHJcbiAgICAvKipcclxuICAgICAqIEludm9rZXMgdGhlIHVwZGF0ZSBvZiBvYnNlcnZlcnMuIEl0IHdpbGwgY29udGludWUgcnVubmluZyB1cGRhdGVzIGluc29mYXJcclxuICAgICAqIGl0IGRldGVjdHMgY2hhbmdlcy5cclxuICAgICAqXHJcbiAgICAgKiBAcmV0dXJucyB7dm9pZH1cclxuICAgICAqL1xyXG4gICAgUmVzaXplT2JzZXJ2ZXJDb250cm9sbGVyLnByb3RvdHlwZS5yZWZyZXNoID0gZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIHZhciBjaGFuZ2VzRGV0ZWN0ZWQgPSB0aGlzLnVwZGF0ZU9ic2VydmVyc18oKTtcclxuICAgICAgICAvLyBDb250aW51ZSBydW5uaW5nIHVwZGF0ZXMgaWYgY2hhbmdlcyBoYXZlIGJlZW4gZGV0ZWN0ZWQgYXMgdGhlcmUgbWlnaHRcclxuICAgICAgICAvLyBiZSBmdXR1cmUgb25lcyBjYXVzZWQgYnkgQ1NTIHRyYW5zaXRpb25zLlxyXG4gICAgICAgIGlmIChjaGFuZ2VzRGV0ZWN0ZWQpIHtcclxuICAgICAgICAgICAgdGhpcy5yZWZyZXNoKCk7XHJcbiAgICAgICAgfVxyXG4gICAgfTtcclxuICAgIC8qKlxyXG4gICAgICogVXBkYXRlcyBldmVyeSBvYnNlcnZlciBmcm9tIG9ic2VydmVycyBsaXN0IGFuZCBub3RpZmllcyB0aGVtIG9mIHF1ZXVlZFxyXG4gICAgICogZW50cmllcy5cclxuICAgICAqXHJcbiAgICAgKiBAcHJpdmF0ZVxyXG4gICAgICogQHJldHVybnMge2Jvb2xlYW59IFJldHVybnMgXCJ0cnVlXCIgaWYgYW55IG9ic2VydmVyIGhhcyBkZXRlY3RlZCBjaGFuZ2VzIGluXHJcbiAgICAgKiAgICAgIGRpbWVuc2lvbnMgb2YgaXQncyBlbGVtZW50cy5cclxuICAgICAqL1xyXG4gICAgUmVzaXplT2JzZXJ2ZXJDb250cm9sbGVyLnByb3RvdHlwZS51cGRhdGVPYnNlcnZlcnNfID0gZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIC8vIENvbGxlY3Qgb2JzZXJ2ZXJzIHRoYXQgaGF2ZSBhY3RpdmUgb2JzZXJ2YXRpb25zLlxyXG4gICAgICAgIHZhciBhY3RpdmVPYnNlcnZlcnMgPSB0aGlzLm9ic2VydmVyc18uZmlsdGVyKGZ1bmN0aW9uIChvYnNlcnZlcikge1xyXG4gICAgICAgICAgICByZXR1cm4gb2JzZXJ2ZXIuZ2F0aGVyQWN0aXZlKCksIG9ic2VydmVyLmhhc0FjdGl2ZSgpO1xyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIC8vIERlbGl2ZXIgbm90aWZpY2F0aW9ucyBpbiBhIHNlcGFyYXRlIGN5Y2xlIGluIG9yZGVyIHRvIGF2b2lkIGFueVxyXG4gICAgICAgIC8vIGNvbGxpc2lvbnMgYmV0d2VlbiBvYnNlcnZlcnMsIGUuZy4gd2hlbiBtdWx0aXBsZSBpbnN0YW5jZXMgb2ZcclxuICAgICAgICAvLyBSZXNpemVPYnNlcnZlciBhcmUgdHJhY2tpbmcgdGhlIHNhbWUgZWxlbWVudCBhbmQgdGhlIGNhbGxiYWNrIG9mIG9uZVxyXG4gICAgICAgIC8vIG9mIHRoZW0gY2hhbmdlcyBjb250ZW50IGRpbWVuc2lvbnMgb2YgdGhlIG9ic2VydmVkIHRhcmdldC4gU29tZXRpbWVzXHJcbiAgICAgICAgLy8gdGhpcyBtYXkgcmVzdWx0IGluIG5vdGlmaWNhdGlvbnMgYmVpbmcgYmxvY2tlZCBmb3IgdGhlIHJlc3Qgb2Ygb2JzZXJ2ZXJzLlxyXG4gICAgICAgIGFjdGl2ZU9ic2VydmVycy5mb3JFYWNoKGZ1bmN0aW9uIChvYnNlcnZlcikgeyByZXR1cm4gb2JzZXJ2ZXIuYnJvYWRjYXN0QWN0aXZlKCk7IH0pO1xyXG4gICAgICAgIHJldHVybiBhY3RpdmVPYnNlcnZlcnMubGVuZ3RoID4gMDtcclxuICAgIH07XHJcbiAgICAvKipcclxuICAgICAqIEluaXRpYWxpemVzIERPTSBsaXN0ZW5lcnMuXHJcbiAgICAgKlxyXG4gICAgICogQHByaXZhdGVcclxuICAgICAqIEByZXR1cm5zIHt2b2lkfVxyXG4gICAgICovXHJcbiAgICBSZXNpemVPYnNlcnZlckNvbnRyb2xsZXIucHJvdG90eXBlLmNvbm5lY3RfID0gZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIC8vIERvIG5vdGhpbmcgaWYgcnVubmluZyBpbiBhIG5vbi1icm93c2VyIGVudmlyb25tZW50IG9yIGlmIGxpc3RlbmVyc1xyXG4gICAgICAgIC8vIGhhdmUgYmVlbiBhbHJlYWR5IGFkZGVkLlxyXG4gICAgICAgIGlmICghaXNCcm93c2VyIHx8IHRoaXMuY29ubmVjdGVkXykge1xyXG4gICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC8vIFN1YnNjcmlwdGlvbiB0byB0aGUgXCJUcmFuc2l0aW9uZW5kXCIgZXZlbnQgaXMgdXNlZCBhcyBhIHdvcmthcm91bmQgZm9yXHJcbiAgICAgICAgLy8gZGVsYXllZCB0cmFuc2l0aW9ucy4gVGhpcyB3YXkgaXQncyBwb3NzaWJsZSB0byBjYXB0dXJlIGF0IGxlYXN0IHRoZVxyXG4gICAgICAgIC8vIGZpbmFsIHN0YXRlIG9mIGFuIGVsZW1lbnQuXHJcbiAgICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigndHJhbnNpdGlvbmVuZCcsIHRoaXMub25UcmFuc2l0aW9uRW5kXyk7XHJcbiAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMucmVmcmVzaCk7XHJcbiAgICAgICAgaWYgKG11dGF0aW9uT2JzZXJ2ZXJTdXBwb3J0ZWQpIHtcclxuICAgICAgICAgICAgdGhpcy5tdXRhdGlvbnNPYnNlcnZlcl8gPSBuZXcgTXV0YXRpb25PYnNlcnZlcih0aGlzLnJlZnJlc2gpO1xyXG4gICAgICAgICAgICB0aGlzLm11dGF0aW9uc09ic2VydmVyXy5vYnNlcnZlKGRvY3VtZW50LCB7XHJcbiAgICAgICAgICAgICAgICBhdHRyaWJ1dGVzOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgY2hpbGRMaXN0OiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgY2hhcmFjdGVyRGF0YTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgIHN1YnRyZWU6IHRydWVcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdET01TdWJ0cmVlTW9kaWZpZWQnLCB0aGlzLnJlZnJlc2gpO1xyXG4gICAgICAgICAgICB0aGlzLm11dGF0aW9uRXZlbnRzQWRkZWRfID0gdHJ1ZTtcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy5jb25uZWN0ZWRfID0gdHJ1ZTtcclxuICAgIH07XHJcbiAgICAvKipcclxuICAgICAqIFJlbW92ZXMgRE9NIGxpc3RlbmVycy5cclxuICAgICAqXHJcbiAgICAgKiBAcHJpdmF0ZVxyXG4gICAgICogQHJldHVybnMge3ZvaWR9XHJcbiAgICAgKi9cclxuICAgIFJlc2l6ZU9ic2VydmVyQ29udHJvbGxlci5wcm90b3R5cGUuZGlzY29ubmVjdF8gPSBmdW5jdGlvbiAoKSB7XHJcbiAgICAgICAgLy8gRG8gbm90aGluZyBpZiBydW5uaW5nIGluIGEgbm9uLWJyb3dzZXIgZW52aXJvbm1lbnQgb3IgaWYgbGlzdGVuZXJzXHJcbiAgICAgICAgLy8gaGF2ZSBiZWVuIGFscmVhZHkgcmVtb3ZlZC5cclxuICAgICAgICBpZiAoIWlzQnJvd3NlciB8fCAhdGhpcy5jb25uZWN0ZWRfKSB7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigndHJhbnNpdGlvbmVuZCcsIHRoaXMub25UcmFuc2l0aW9uRW5kXyk7XHJcbiAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMucmVmcmVzaCk7XHJcbiAgICAgICAgaWYgKHRoaXMubXV0YXRpb25zT2JzZXJ2ZXJfKSB7XHJcbiAgICAgICAgICAgIHRoaXMubXV0YXRpb25zT2JzZXJ2ZXJfLmRpc2Nvbm5lY3QoKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHRoaXMubXV0YXRpb25FdmVudHNBZGRlZF8pIHtcclxuICAgICAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignRE9NU3VidHJlZU1vZGlmaWVkJywgdGhpcy5yZWZyZXNoKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgdGhpcy5tdXRhdGlvbnNPYnNlcnZlcl8gPSBudWxsO1xyXG4gICAgICAgIHRoaXMubXV0YXRpb25FdmVudHNBZGRlZF8gPSBmYWxzZTtcclxuICAgICAgICB0aGlzLmNvbm5lY3RlZF8gPSBmYWxzZTtcclxuICAgIH07XHJcbiAgICAvKipcclxuICAgICAqIFwiVHJhbnNpdGlvbmVuZFwiIGV2ZW50IGhhbmRsZXIuXHJcbiAgICAgKlxyXG4gICAgICogQHByaXZhdGVcclxuICAgICAqIEBwYXJhbSB7VHJhbnNpdGlvbkV2ZW50fSBldmVudFxyXG4gICAgICogQHJldHVybnMge3ZvaWR9XHJcbiAgICAgKi9cclxuICAgIFJlc2l6ZU9ic2VydmVyQ29udHJvbGxlci5wcm90b3R5cGUub25UcmFuc2l0aW9uRW5kXyA9IGZ1bmN0aW9uIChfYSkge1xyXG4gICAgICAgIHZhciBfYiA9IF9hLnByb3BlcnR5TmFtZSwgcHJvcGVydHlOYW1lID0gX2IgPT09IHZvaWQgMCA/ICcnIDogX2I7XHJcbiAgICAgICAgLy8gRGV0ZWN0IHdoZXRoZXIgdHJhbnNpdGlvbiBtYXkgYWZmZWN0IGRpbWVuc2lvbnMgb2YgYW4gZWxlbWVudC5cclxuICAgICAgICB2YXIgaXNSZWZsb3dQcm9wZXJ0eSA9IHRyYW5zaXRpb25LZXlzLnNvbWUoZnVuY3Rpb24gKGtleSkge1xyXG4gICAgICAgICAgICByZXR1cm4gISF+cHJvcGVydHlOYW1lLmluZGV4T2Yoa2V5KTtcclxuICAgICAgICB9KTtcclxuICAgICAgICBpZiAoaXNSZWZsb3dQcm9wZXJ0eSkge1xyXG4gICAgICAgICAgICB0aGlzLnJlZnJlc2goKTtcclxuICAgICAgICB9XHJcbiAgICB9O1xyXG4gICAgLyoqXHJcbiAgICAgKiBSZXR1cm5zIGluc3RhbmNlIG9mIHRoZSBSZXNpemVPYnNlcnZlckNvbnRyb2xsZXIuXHJcbiAgICAgKlxyXG4gICAgICogQHJldHVybnMge1Jlc2l6ZU9ic2VydmVyQ29udHJvbGxlcn1cclxuICAgICAqL1xyXG4gICAgUmVzaXplT2JzZXJ2ZXJDb250cm9sbGVyLmdldEluc3RhbmNlID0gZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIGlmICghdGhpcy5pbnN0YW5jZV8pIHtcclxuICAgICAgICAgICAgdGhpcy5pbnN0YW5jZV8gPSBuZXcgUmVzaXplT2JzZXJ2ZXJDb250cm9sbGVyKCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiB0aGlzLmluc3RhbmNlXztcclxuICAgIH07XHJcbiAgICAvKipcclxuICAgICAqIEhvbGRzIHJlZmVyZW5jZSB0byB0aGUgY29udHJvbGxlcidzIGluc3RhbmNlLlxyXG4gICAgICpcclxuICAgICAqIEBwcml2YXRlIHtSZXNpemVPYnNlcnZlckNvbnRyb2xsZXJ9XHJcbiAgICAgKi9cclxuICAgIFJlc2l6ZU9ic2VydmVyQ29udHJvbGxlci5pbnN0YW5jZV8gPSBudWxsO1xyXG4gICAgcmV0dXJuIFJlc2l6ZU9ic2VydmVyQ29udHJvbGxlcjtcclxufSgpKTtcblxuLyoqXHJcbiAqIERlZmluZXMgbm9uLXdyaXRhYmxlL2VudW1lcmFibGUgcHJvcGVydGllcyBvZiB0aGUgcHJvdmlkZWQgdGFyZ2V0IG9iamVjdC5cclxuICpcclxuICogQHBhcmFtIHtPYmplY3R9IHRhcmdldCAtIE9iamVjdCBmb3Igd2hpY2ggdG8gZGVmaW5lIHByb3BlcnRpZXMuXHJcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIFByb3BlcnRpZXMgdG8gYmUgZGVmaW5lZC5cclxuICogQHJldHVybnMge09iamVjdH0gVGFyZ2V0IG9iamVjdC5cclxuICovXHJcbnZhciBkZWZpbmVDb25maWd1cmFibGUgPSAoZnVuY3Rpb24gKHRhcmdldCwgcHJvcHMpIHtcclxuICAgIGZvciAodmFyIF9pID0gMCwgX2EgPSBPYmplY3Qua2V5cyhwcm9wcyk7IF9pIDwgX2EubGVuZ3RoOyBfaSsrKSB7XHJcbiAgICAgICAgdmFyIGtleSA9IF9hW19pXTtcclxuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodGFyZ2V0LCBrZXksIHtcclxuICAgICAgICAgICAgdmFsdWU6IHByb3BzW2tleV0sXHJcbiAgICAgICAgICAgIGVudW1lcmFibGU6IGZhbHNlLFxyXG4gICAgICAgICAgICB3cml0YWJsZTogZmFsc2UsXHJcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxyXG4gICAgICAgIH0pO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIHRhcmdldDtcclxufSk7XG5cbi8qKlxyXG4gKiBSZXR1cm5zIHRoZSBnbG9iYWwgb2JqZWN0IGFzc29jaWF0ZWQgd2l0aCBwcm92aWRlZCBlbGVtZW50LlxyXG4gKlxyXG4gKiBAcGFyYW0ge09iamVjdH0gdGFyZ2V0XHJcbiAqIEByZXR1cm5zIHtPYmplY3R9XHJcbiAqL1xyXG52YXIgZ2V0V2luZG93T2YgPSAoZnVuY3Rpb24gKHRhcmdldCkge1xyXG4gICAgLy8gQXNzdW1lIHRoYXQgdGhlIGVsZW1lbnQgaXMgYW4gaW5zdGFuY2Ugb2YgTm9kZSwgd2hpY2ggbWVhbnMgdGhhdCBpdFxyXG4gICAgLy8gaGFzIHRoZSBcIm93bmVyRG9jdW1lbnRcIiBwcm9wZXJ0eSBmcm9tIHdoaWNoIHdlIGNhbiByZXRyaWV2ZSBhXHJcbiAgICAvLyBjb3JyZXNwb25kaW5nIGdsb2JhbCBvYmplY3QuXHJcbiAgICB2YXIgb3duZXJHbG9iYWwgPSB0YXJnZXQgJiYgdGFyZ2V0Lm93bmVyRG9jdW1lbnQgJiYgdGFyZ2V0Lm93bmVyRG9jdW1lbnQuZGVmYXVsdFZpZXc7XHJcbiAgICAvLyBSZXR1cm4gdGhlIGxvY2FsIGdsb2JhbCBvYmplY3QgaWYgaXQncyBub3QgcG9zc2libGUgZXh0cmFjdCBvbmUgZnJvbVxyXG4gICAgLy8gcHJvdmlkZWQgZWxlbWVudC5cclxuICAgIHJldHVybiBvd25lckdsb2JhbCB8fCBnbG9iYWwkMTtcclxufSk7XG5cbi8vIFBsYWNlaG9sZGVyIG9mIGFuIGVtcHR5IGNvbnRlbnQgcmVjdGFuZ2xlLlxyXG52YXIgZW1wdHlSZWN0ID0gY3JlYXRlUmVjdEluaXQoMCwgMCwgMCwgMCk7XHJcbi8qKlxyXG4gKiBDb252ZXJ0cyBwcm92aWRlZCBzdHJpbmcgdG8gYSBudW1iZXIuXHJcbiAqXHJcbiAqIEBwYXJhbSB7bnVtYmVyfHN0cmluZ30gdmFsdWVcclxuICogQHJldHVybnMge251bWJlcn1cclxuICovXHJcbmZ1bmN0aW9uIHRvRmxvYXQodmFsdWUpIHtcclxuICAgIHJldHVybiBwYXJzZUZsb2F0KHZhbHVlKSB8fCAwO1xyXG59XHJcbi8qKlxyXG4gKiBFeHRyYWN0cyBib3JkZXJzIHNpemUgZnJvbSBwcm92aWRlZCBzdHlsZXMuXHJcbiAqXHJcbiAqIEBwYXJhbSB7Q1NTU3R5bGVEZWNsYXJhdGlvbn0gc3R5bGVzXHJcbiAqIEBwYXJhbSB7Li4uc3RyaW5nfSBwb3NpdGlvbnMgLSBCb3JkZXJzIHBvc2l0aW9ucyAodG9wLCByaWdodCwgLi4uKVxyXG4gKiBAcmV0dXJucyB7bnVtYmVyfVxyXG4gKi9cclxuZnVuY3Rpb24gZ2V0Qm9yZGVyc1NpemUoc3R5bGVzKSB7XHJcbiAgICB2YXIgcG9zaXRpb25zID0gW107XHJcbiAgICBmb3IgKHZhciBfaSA9IDE7IF9pIDwgYXJndW1lbnRzLmxlbmd0aDsgX2krKykge1xyXG4gICAgICAgIHBvc2l0aW9uc1tfaSAtIDFdID0gYXJndW1lbnRzW19pXTtcclxuICAgIH1cclxuICAgIHJldHVybiBwb3NpdGlvbnMucmVkdWNlKGZ1bmN0aW9uIChzaXplLCBwb3NpdGlvbikge1xyXG4gICAgICAgIHZhciB2YWx1ZSA9IHN0eWxlc1snYm9yZGVyLScgKyBwb3NpdGlvbiArICctd2lkdGgnXTtcclxuICAgICAgICByZXR1cm4gc2l6ZSArIHRvRmxvYXQodmFsdWUpO1xyXG4gICAgfSwgMCk7XHJcbn1cclxuLyoqXHJcbiAqIEV4dHJhY3RzIHBhZGRpbmdzIHNpemVzIGZyb20gcHJvdmlkZWQgc3R5bGVzLlxyXG4gKlxyXG4gKiBAcGFyYW0ge0NTU1N0eWxlRGVjbGFyYXRpb259IHN0eWxlc1xyXG4gKiBAcmV0dXJucyB7T2JqZWN0fSBQYWRkaW5ncyBib3guXHJcbiAqL1xyXG5mdW5jdGlvbiBnZXRQYWRkaW5ncyhzdHlsZXMpIHtcclxuICAgIHZhciBwb3NpdGlvbnMgPSBbJ3RvcCcsICdyaWdodCcsICdib3R0b20nLCAnbGVmdCddO1xyXG4gICAgdmFyIHBhZGRpbmdzID0ge307XHJcbiAgICBmb3IgKHZhciBfaSA9IDAsIHBvc2l0aW9uc18xID0gcG9zaXRpb25zOyBfaSA8IHBvc2l0aW9uc18xLmxlbmd0aDsgX2krKykge1xyXG4gICAgICAgIHZhciBwb3NpdGlvbiA9IHBvc2l0aW9uc18xW19pXTtcclxuICAgICAgICB2YXIgdmFsdWUgPSBzdHlsZXNbJ3BhZGRpbmctJyArIHBvc2l0aW9uXTtcclxuICAgICAgICBwYWRkaW5nc1twb3NpdGlvbl0gPSB0b0Zsb2F0KHZhbHVlKTtcclxuICAgIH1cclxuICAgIHJldHVybiBwYWRkaW5ncztcclxufVxyXG4vKipcclxuICogQ2FsY3VsYXRlcyBjb250ZW50IHJlY3RhbmdsZSBvZiBwcm92aWRlZCBTVkcgZWxlbWVudC5cclxuICpcclxuICogQHBhcmFtIHtTVkdHcmFwaGljc0VsZW1lbnR9IHRhcmdldCAtIEVsZW1lbnQgY29udGVudCByZWN0YW5nbGUgb2Ygd2hpY2ggbmVlZHNcclxuICogICAgICB0byBiZSBjYWxjdWxhdGVkLlxyXG4gKiBAcmV0dXJucyB7RE9NUmVjdEluaXR9XHJcbiAqL1xyXG5mdW5jdGlvbiBnZXRTVkdDb250ZW50UmVjdCh0YXJnZXQpIHtcclxuICAgIHZhciBiYm94ID0gdGFyZ2V0LmdldEJCb3goKTtcclxuICAgIHJldHVybiBjcmVhdGVSZWN0SW5pdCgwLCAwLCBiYm94LndpZHRoLCBiYm94LmhlaWdodCk7XHJcbn1cclxuLyoqXHJcbiAqIENhbGN1bGF0ZXMgY29udGVudCByZWN0YW5nbGUgb2YgcHJvdmlkZWQgSFRNTEVsZW1lbnQuXHJcbiAqXHJcbiAqIEBwYXJhbSB7SFRNTEVsZW1lbnR9IHRhcmdldCAtIEVsZW1lbnQgZm9yIHdoaWNoIHRvIGNhbGN1bGF0ZSB0aGUgY29udGVudCByZWN0YW5nbGUuXHJcbiAqIEByZXR1cm5zIHtET01SZWN0SW5pdH1cclxuICovXHJcbmZ1bmN0aW9uIGdldEhUTUxFbGVtZW50Q29udGVudFJlY3QodGFyZ2V0KSB7XHJcbiAgICAvLyBDbGllbnQgd2lkdGggJiBoZWlnaHQgcHJvcGVydGllcyBjYW4ndCBiZVxyXG4gICAgLy8gdXNlZCBleGNsdXNpdmVseSBhcyB0aGV5IHByb3ZpZGUgcm91bmRlZCB2YWx1ZXMuXHJcbiAgICB2YXIgY2xpZW50V2lkdGggPSB0YXJnZXQuY2xpZW50V2lkdGgsIGNsaWVudEhlaWdodCA9IHRhcmdldC5jbGllbnRIZWlnaHQ7XHJcbiAgICAvLyBCeSB0aGlzIGNvbmRpdGlvbiB3ZSBjYW4gY2F0Y2ggYWxsIG5vbi1yZXBsYWNlZCBpbmxpbmUsIGhpZGRlbiBhbmRcclxuICAgIC8vIGRldGFjaGVkIGVsZW1lbnRzLiBUaG91Z2ggZWxlbWVudHMgd2l0aCB3aWR0aCAmIGhlaWdodCBwcm9wZXJ0aWVzIGxlc3NcclxuICAgIC8vIHRoYW4gMC41IHdpbGwgYmUgZGlzY2FyZGVkIGFzIHdlbGwuXHJcbiAgICAvL1xyXG4gICAgLy8gV2l0aG91dCBpdCB3ZSB3b3VsZCBuZWVkIHRvIGltcGxlbWVudCBzZXBhcmF0ZSBtZXRob2RzIGZvciBlYWNoIG9mXHJcbiAgICAvLyB0aG9zZSBjYXNlcyBhbmQgaXQncyBub3QgcG9zc2libGUgdG8gcGVyZm9ybSBhIHByZWNpc2UgYW5kIHBlcmZvcm1hbmNlXHJcbiAgICAvLyBlZmZlY3RpdmUgdGVzdCBmb3IgaGlkZGVuIGVsZW1lbnRzLiBFLmcuIGV2ZW4galF1ZXJ5J3MgJzp2aXNpYmxlJyBmaWx0ZXJcclxuICAgIC8vIGdpdmVzIHdyb25nIHJlc3VsdHMgZm9yIGVsZW1lbnRzIHdpdGggd2lkdGggJiBoZWlnaHQgbGVzcyB0aGFuIDAuNS5cclxuICAgIGlmICghY2xpZW50V2lkdGggJiYgIWNsaWVudEhlaWdodCkge1xyXG4gICAgICAgIHJldHVybiBlbXB0eVJlY3Q7XHJcbiAgICB9XHJcbiAgICB2YXIgc3R5bGVzID0gZ2V0V2luZG93T2YodGFyZ2V0KS5nZXRDb21wdXRlZFN0eWxlKHRhcmdldCk7XHJcbiAgICB2YXIgcGFkZGluZ3MgPSBnZXRQYWRkaW5ncyhzdHlsZXMpO1xyXG4gICAgdmFyIGhvcml6UGFkID0gcGFkZGluZ3MubGVmdCArIHBhZGRpbmdzLnJpZ2h0O1xyXG4gICAgdmFyIHZlcnRQYWQgPSBwYWRkaW5ncy50b3AgKyBwYWRkaW5ncy5ib3R0b207XHJcbiAgICAvLyBDb21wdXRlZCBzdHlsZXMgb2Ygd2lkdGggJiBoZWlnaHQgYXJlIGJlaW5nIHVzZWQgYmVjYXVzZSB0aGV5IGFyZSB0aGVcclxuICAgIC8vIG9ubHkgZGltZW5zaW9ucyBhdmFpbGFibGUgdG8gSlMgdGhhdCBjb250YWluIG5vbi1yb3VuZGVkIHZhbHVlcy4gSXQgY291bGRcclxuICAgIC8vIGJlIHBvc3NpYmxlIHRvIHV0aWxpemUgdGhlIGdldEJvdW5kaW5nQ2xpZW50UmVjdCBpZiBvbmx5IGl0J3MgZGF0YSB3YXNuJ3RcclxuICAgIC8vIGFmZmVjdGVkIGJ5IENTUyB0cmFuc2Zvcm1hdGlvbnMgbGV0IGFsb25lIHBhZGRpbmdzLCBib3JkZXJzIGFuZCBzY3JvbGwgYmFycy5cclxuICAgIHZhciB3aWR0aCA9IHRvRmxvYXQoc3R5bGVzLndpZHRoKSwgaGVpZ2h0ID0gdG9GbG9hdChzdHlsZXMuaGVpZ2h0KTtcclxuICAgIC8vIFdpZHRoICYgaGVpZ2h0IGluY2x1ZGUgcGFkZGluZ3MgYW5kIGJvcmRlcnMgd2hlbiB0aGUgJ2JvcmRlci1ib3gnIGJveFxyXG4gICAgLy8gbW9kZWwgaXMgYXBwbGllZCAoZXhjZXB0IGZvciBJRSkuXHJcbiAgICBpZiAoc3R5bGVzLmJveFNpemluZyA9PT0gJ2JvcmRlci1ib3gnKSB7XHJcbiAgICAgICAgLy8gRm9sbG93aW5nIGNvbmRpdGlvbnMgYXJlIHJlcXVpcmVkIHRvIGhhbmRsZSBJbnRlcm5ldCBFeHBsb3JlciB3aGljaFxyXG4gICAgICAgIC8vIGRvZXNuJ3QgaW5jbHVkZSBwYWRkaW5ncyBhbmQgYm9yZGVycyB0byBjb21wdXRlZCBDU1MgZGltZW5zaW9ucy5cclxuICAgICAgICAvL1xyXG4gICAgICAgIC8vIFdlIGNhbiBzYXkgdGhhdCBpZiBDU1MgZGltZW5zaW9ucyArIHBhZGRpbmdzIGFyZSBlcXVhbCB0byB0aGUgXCJjbGllbnRcIlxyXG4gICAgICAgIC8vIHByb3BlcnRpZXMgdGhlbiBpdCdzIGVpdGhlciBJRSwgYW5kIHRodXMgd2UgZG9uJ3QgbmVlZCB0byBzdWJ0cmFjdFxyXG4gICAgICAgIC8vIGFueXRoaW5nLCBvciBhbiBlbGVtZW50IG1lcmVseSBkb2Vzbid0IGhhdmUgcGFkZGluZ3MvYm9yZGVycyBzdHlsZXMuXHJcbiAgICAgICAgaWYgKE1hdGgucm91bmQod2lkdGggKyBob3JpelBhZCkgIT09IGNsaWVudFdpZHRoKSB7XHJcbiAgICAgICAgICAgIHdpZHRoIC09IGdldEJvcmRlcnNTaXplKHN0eWxlcywgJ2xlZnQnLCAncmlnaHQnKSArIGhvcml6UGFkO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoTWF0aC5yb3VuZChoZWlnaHQgKyB2ZXJ0UGFkKSAhPT0gY2xpZW50SGVpZ2h0KSB7XHJcbiAgICAgICAgICAgIGhlaWdodCAtPSBnZXRCb3JkZXJzU2l6ZShzdHlsZXMsICd0b3AnLCAnYm90dG9tJykgKyB2ZXJ0UGFkO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIC8vIEZvbGxvd2luZyBzdGVwcyBjYW4ndCBiZSBhcHBsaWVkIHRvIHRoZSBkb2N1bWVudCdzIHJvb3QgZWxlbWVudCBhcyBpdHNcclxuICAgIC8vIGNsaWVudFtXaWR0aC9IZWlnaHRdIHByb3BlcnRpZXMgcmVwcmVzZW50IHZpZXdwb3J0IGFyZWEgb2YgdGhlIHdpbmRvdy5cclxuICAgIC8vIEJlc2lkZXMsIGl0J3MgYXMgd2VsbCBub3QgbmVjZXNzYXJ5IGFzIHRoZSA8aHRtbD4gaXRzZWxmIG5laXRoZXIgaGFzXHJcbiAgICAvLyByZW5kZXJlZCBzY3JvbGwgYmFycyBub3IgaXQgY2FuIGJlIGNsaXBwZWQuXHJcbiAgICBpZiAoIWlzRG9jdW1lbnRFbGVtZW50KHRhcmdldCkpIHtcclxuICAgICAgICAvLyBJbiBzb21lIGJyb3dzZXJzIChvbmx5IGluIEZpcmVmb3gsIGFjdHVhbGx5KSBDU1Mgd2lkdGggJiBoZWlnaHRcclxuICAgICAgICAvLyBpbmNsdWRlIHNjcm9sbCBiYXJzIHNpemUgd2hpY2ggY2FuIGJlIHJlbW92ZWQgYXQgdGhpcyBzdGVwIGFzIHNjcm9sbFxyXG4gICAgICAgIC8vIGJhcnMgYXJlIHRoZSBvbmx5IGRpZmZlcmVuY2UgYmV0d2VlbiByb3VuZGVkIGRpbWVuc2lvbnMgKyBwYWRkaW5nc1xyXG4gICAgICAgIC8vIGFuZCBcImNsaWVudFwiIHByb3BlcnRpZXMsIHRob3VnaCB0aGF0IGlzIG5vdCBhbHdheXMgdHJ1ZSBpbiBDaHJvbWUuXHJcbiAgICAgICAgdmFyIHZlcnRTY3JvbGxiYXIgPSBNYXRoLnJvdW5kKHdpZHRoICsgaG9yaXpQYWQpIC0gY2xpZW50V2lkdGg7XHJcbiAgICAgICAgdmFyIGhvcml6U2Nyb2xsYmFyID0gTWF0aC5yb3VuZChoZWlnaHQgKyB2ZXJ0UGFkKSAtIGNsaWVudEhlaWdodDtcclxuICAgICAgICAvLyBDaHJvbWUgaGFzIGEgcmF0aGVyIHdlaXJkIHJvdW5kaW5nIG9mIFwiY2xpZW50XCIgcHJvcGVydGllcy5cclxuICAgICAgICAvLyBFLmcuIGZvciBhbiBlbGVtZW50IHdpdGggY29udGVudCB3aWR0aCBvZiAzMTQuMnB4IGl0IHNvbWV0aW1lcyBnaXZlc1xyXG4gICAgICAgIC8vIHRoZSBjbGllbnQgd2lkdGggb2YgMzE1cHggYW5kIGZvciB0aGUgd2lkdGggb2YgMzE0LjdweCBpdCBtYXkgZ2l2ZVxyXG4gICAgICAgIC8vIDMxNHB4LiBBbmQgaXQgZG9lc24ndCBoYXBwZW4gYWxsIHRoZSB0aW1lLiBTbyBqdXN0IGlnbm9yZSB0aGlzIGRlbHRhXHJcbiAgICAgICAgLy8gYXMgYSBub24tcmVsZXZhbnQuXHJcbiAgICAgICAgaWYgKE1hdGguYWJzKHZlcnRTY3JvbGxiYXIpICE9PSAxKSB7XHJcbiAgICAgICAgICAgIHdpZHRoIC09IHZlcnRTY3JvbGxiYXI7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChNYXRoLmFicyhob3JpelNjcm9sbGJhcikgIT09IDEpIHtcclxuICAgICAgICAgICAgaGVpZ2h0IC09IGhvcml6U2Nyb2xsYmFyO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHJldHVybiBjcmVhdGVSZWN0SW5pdChwYWRkaW5ncy5sZWZ0LCBwYWRkaW5ncy50b3AsIHdpZHRoLCBoZWlnaHQpO1xyXG59XHJcbi8qKlxyXG4gKiBDaGVja3Mgd2hldGhlciBwcm92aWRlZCBlbGVtZW50IGlzIGFuIGluc3RhbmNlIG9mIHRoZSBTVkdHcmFwaGljc0VsZW1lbnQuXHJcbiAqXHJcbiAqIEBwYXJhbSB7RWxlbWVudH0gdGFyZ2V0IC0gRWxlbWVudCB0byBiZSBjaGVja2VkLlxyXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cclxuICovXHJcbnZhciBpc1NWR0dyYXBoaWNzRWxlbWVudCA9IChmdW5jdGlvbiAoKSB7XHJcbiAgICAvLyBTb21lIGJyb3dzZXJzLCBuYW1lbHkgSUUgYW5kIEVkZ2UsIGRvbid0IGhhdmUgdGhlIFNWR0dyYXBoaWNzRWxlbWVudFxyXG4gICAgLy8gaW50ZXJmYWNlLlxyXG4gICAgaWYgKHR5cGVvZiBTVkdHcmFwaGljc0VsZW1lbnQgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgcmV0dXJuIGZ1bmN0aW9uICh0YXJnZXQpIHsgcmV0dXJuIHRhcmdldCBpbnN0YW5jZW9mIGdldFdpbmRvd09mKHRhcmdldCkuU1ZHR3JhcGhpY3NFbGVtZW50OyB9O1xyXG4gICAgfVxyXG4gICAgLy8gSWYgaXQncyBzbywgdGhlbiBjaGVjayB0aGF0IGVsZW1lbnQgaXMgYXQgbGVhc3QgYW4gaW5zdGFuY2Ugb2YgdGhlXHJcbiAgICAvLyBTVkdFbGVtZW50IGFuZCB0aGF0IGl0IGhhcyB0aGUgXCJnZXRCQm94XCIgbWV0aG9kLlxyXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWV4dHJhLXBhcmVuc1xyXG4gICAgcmV0dXJuIGZ1bmN0aW9uICh0YXJnZXQpIHsgcmV0dXJuICh0YXJnZXQgaW5zdGFuY2VvZiBnZXRXaW5kb3dPZih0YXJnZXQpLlNWR0VsZW1lbnQgJiZcclxuICAgICAgICB0eXBlb2YgdGFyZ2V0LmdldEJCb3ggPT09ICdmdW5jdGlvbicpOyB9O1xyXG59KSgpO1xyXG4vKipcclxuICogQ2hlY2tzIHdoZXRoZXIgcHJvdmlkZWQgZWxlbWVudCBpcyBhIGRvY3VtZW50IGVsZW1lbnQgKDxodG1sPikuXHJcbiAqXHJcbiAqIEBwYXJhbSB7RWxlbWVudH0gdGFyZ2V0IC0gRWxlbWVudCB0byBiZSBjaGVja2VkLlxyXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cclxuICovXHJcbmZ1bmN0aW9uIGlzRG9jdW1lbnRFbGVtZW50KHRhcmdldCkge1xyXG4gICAgcmV0dXJuIHRhcmdldCA9PT0gZ2V0V2luZG93T2YodGFyZ2V0KS5kb2N1bWVudC5kb2N1bWVudEVsZW1lbnQ7XHJcbn1cclxuLyoqXHJcbiAqIENhbGN1bGF0ZXMgYW4gYXBwcm9wcmlhdGUgY29udGVudCByZWN0YW5nbGUgZm9yIHByb3ZpZGVkIGh0bWwgb3Igc3ZnIGVsZW1lbnQuXHJcbiAqXHJcbiAqIEBwYXJhbSB7RWxlbWVudH0gdGFyZ2V0IC0gRWxlbWVudCBjb250ZW50IHJlY3RhbmdsZSBvZiB3aGljaCBuZWVkcyB0byBiZSBjYWxjdWxhdGVkLlxyXG4gKiBAcmV0dXJucyB7RE9NUmVjdEluaXR9XHJcbiAqL1xyXG5mdW5jdGlvbiBnZXRDb250ZW50UmVjdCh0YXJnZXQpIHtcclxuICAgIGlmICghaXNCcm93c2VyKSB7XHJcbiAgICAgICAgcmV0dXJuIGVtcHR5UmVjdDtcclxuICAgIH1cclxuICAgIGlmIChpc1NWR0dyYXBoaWNzRWxlbWVudCh0YXJnZXQpKSB7XHJcbiAgICAgICAgcmV0dXJuIGdldFNWR0NvbnRlbnRSZWN0KHRhcmdldCk7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gZ2V0SFRNTEVsZW1lbnRDb250ZW50UmVjdCh0YXJnZXQpO1xyXG59XHJcbi8qKlxyXG4gKiBDcmVhdGVzIHJlY3RhbmdsZSB3aXRoIGFuIGludGVyZmFjZSBvZiB0aGUgRE9NUmVjdFJlYWRPbmx5LlxyXG4gKiBTcGVjOiBodHRwczovL2RyYWZ0cy5meHRmLm9yZy9nZW9tZXRyeS8jZG9tcmVjdHJlYWRvbmx5XHJcbiAqXHJcbiAqIEBwYXJhbSB7RE9NUmVjdEluaXR9IHJlY3RJbml0IC0gT2JqZWN0IHdpdGggcmVjdGFuZ2xlJ3MgeC95IGNvb3JkaW5hdGVzIGFuZCBkaW1lbnNpb25zLlxyXG4gKiBAcmV0dXJucyB7RE9NUmVjdFJlYWRPbmx5fVxyXG4gKi9cclxuZnVuY3Rpb24gY3JlYXRlUmVhZE9ubHlSZWN0KF9hKSB7XHJcbiAgICB2YXIgeCA9IF9hLngsIHkgPSBfYS55LCB3aWR0aCA9IF9hLndpZHRoLCBoZWlnaHQgPSBfYS5oZWlnaHQ7XHJcbiAgICAvLyBJZiBET01SZWN0UmVhZE9ubHkgaXMgYXZhaWxhYmxlIHVzZSBpdCBhcyBhIHByb3RvdHlwZSBmb3IgdGhlIHJlY3RhbmdsZS5cclxuICAgIHZhciBDb25zdHIgPSB0eXBlb2YgRE9NUmVjdFJlYWRPbmx5ICE9PSAndW5kZWZpbmVkJyA/IERPTVJlY3RSZWFkT25seSA6IE9iamVjdDtcclxuICAgIHZhciByZWN0ID0gT2JqZWN0LmNyZWF0ZShDb25zdHIucHJvdG90eXBlKTtcclxuICAgIC8vIFJlY3RhbmdsZSdzIHByb3BlcnRpZXMgYXJlIG5vdCB3cml0YWJsZSBhbmQgbm9uLWVudW1lcmFibGUuXHJcbiAgICBkZWZpbmVDb25maWd1cmFibGUocmVjdCwge1xyXG4gICAgICAgIHg6IHgsIHk6IHksIHdpZHRoOiB3aWR0aCwgaGVpZ2h0OiBoZWlnaHQsXHJcbiAgICAgICAgdG9wOiB5LFxyXG4gICAgICAgIHJpZ2h0OiB4ICsgd2lkdGgsXHJcbiAgICAgICAgYm90dG9tOiBoZWlnaHQgKyB5LFxyXG4gICAgICAgIGxlZnQ6IHhcclxuICAgIH0pO1xyXG4gICAgcmV0dXJuIHJlY3Q7XHJcbn1cclxuLyoqXHJcbiAqIENyZWF0ZXMgRE9NUmVjdEluaXQgb2JqZWN0IGJhc2VkIG9uIHRoZSBwcm92aWRlZCBkaW1lbnNpb25zIGFuZCB0aGUgeC95IGNvb3JkaW5hdGVzLlxyXG4gKiBTcGVjOiBodHRwczovL2RyYWZ0cy5meHRmLm9yZy9nZW9tZXRyeS8jZGljdGRlZi1kb21yZWN0aW5pdFxyXG4gKlxyXG4gKiBAcGFyYW0ge251bWJlcn0geCAtIFggY29vcmRpbmF0ZS5cclxuICogQHBhcmFtIHtudW1iZXJ9IHkgLSBZIGNvb3JkaW5hdGUuXHJcbiAqIEBwYXJhbSB7bnVtYmVyfSB3aWR0aCAtIFJlY3RhbmdsZSdzIHdpZHRoLlxyXG4gKiBAcGFyYW0ge251bWJlcn0gaGVpZ2h0IC0gUmVjdGFuZ2xlJ3MgaGVpZ2h0LlxyXG4gKiBAcmV0dXJucyB7RE9NUmVjdEluaXR9XHJcbiAqL1xyXG5mdW5jdGlvbiBjcmVhdGVSZWN0SW5pdCh4LCB5LCB3aWR0aCwgaGVpZ2h0KSB7XHJcbiAgICByZXR1cm4geyB4OiB4LCB5OiB5LCB3aWR0aDogd2lkdGgsIGhlaWdodDogaGVpZ2h0IH07XHJcbn1cblxuLyoqXHJcbiAqIENsYXNzIHRoYXQgaXMgcmVzcG9uc2libGUgZm9yIGNvbXB1dGF0aW9ucyBvZiB0aGUgY29udGVudCByZWN0YW5nbGUgb2ZcclxuICogcHJvdmlkZWQgRE9NIGVsZW1lbnQgYW5kIGZvciBrZWVwaW5nIHRyYWNrIG9mIGl0J3MgY2hhbmdlcy5cclxuICovXHJcbnZhciBSZXNpemVPYnNlcnZhdGlvbiA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uICgpIHtcclxuICAgIC8qKlxyXG4gICAgICogQ3JlYXRlcyBhbiBpbnN0YW5jZSBvZiBSZXNpemVPYnNlcnZhdGlvbi5cclxuICAgICAqXHJcbiAgICAgKiBAcGFyYW0ge0VsZW1lbnR9IHRhcmdldCAtIEVsZW1lbnQgdG8gYmUgb2JzZXJ2ZWQuXHJcbiAgICAgKi9cclxuICAgIGZ1bmN0aW9uIFJlc2l6ZU9ic2VydmF0aW9uKHRhcmdldCkge1xyXG4gICAgICAgIC8qKlxyXG4gICAgICAgICAqIEJyb2FkY2FzdGVkIHdpZHRoIG9mIGNvbnRlbnQgcmVjdGFuZ2xlLlxyXG4gICAgICAgICAqXHJcbiAgICAgICAgICogQHR5cGUge251bWJlcn1cclxuICAgICAgICAgKi9cclxuICAgICAgICB0aGlzLmJyb2FkY2FzdFdpZHRoID0gMDtcclxuICAgICAgICAvKipcclxuICAgICAgICAgKiBCcm9hZGNhc3RlZCBoZWlnaHQgb2YgY29udGVudCByZWN0YW5nbGUuXHJcbiAgICAgICAgICpcclxuICAgICAgICAgKiBAdHlwZSB7bnVtYmVyfVxyXG4gICAgICAgICAqL1xyXG4gICAgICAgIHRoaXMuYnJvYWRjYXN0SGVpZ2h0ID0gMDtcclxuICAgICAgICAvKipcclxuICAgICAgICAgKiBSZWZlcmVuY2UgdG8gdGhlIGxhc3Qgb2JzZXJ2ZWQgY29udGVudCByZWN0YW5nbGUuXHJcbiAgICAgICAgICpcclxuICAgICAgICAgKiBAcHJpdmF0ZSB7RE9NUmVjdEluaXR9XHJcbiAgICAgICAgICovXHJcbiAgICAgICAgdGhpcy5jb250ZW50UmVjdF8gPSBjcmVhdGVSZWN0SW5pdCgwLCAwLCAwLCAwKTtcclxuICAgICAgICB0aGlzLnRhcmdldCA9IHRhcmdldDtcclxuICAgIH1cclxuICAgIC8qKlxyXG4gICAgICogVXBkYXRlcyBjb250ZW50IHJlY3RhbmdsZSBhbmQgdGVsbHMgd2hldGhlciBpdCdzIHdpZHRoIG9yIGhlaWdodCBwcm9wZXJ0aWVzXHJcbiAgICAgKiBoYXZlIGNoYW5nZWQgc2luY2UgdGhlIGxhc3QgYnJvYWRjYXN0LlxyXG4gICAgICpcclxuICAgICAqIEByZXR1cm5zIHtib29sZWFufVxyXG4gICAgICovXHJcbiAgICBSZXNpemVPYnNlcnZhdGlvbi5wcm90b3R5cGUuaXNBY3RpdmUgPSBmdW5jdGlvbiAoKSB7XHJcbiAgICAgICAgdmFyIHJlY3QgPSBnZXRDb250ZW50UmVjdCh0aGlzLnRhcmdldCk7XHJcbiAgICAgICAgdGhpcy5jb250ZW50UmVjdF8gPSByZWN0O1xyXG4gICAgICAgIHJldHVybiAocmVjdC53aWR0aCAhPT0gdGhpcy5icm9hZGNhc3RXaWR0aCB8fFxyXG4gICAgICAgICAgICByZWN0LmhlaWdodCAhPT0gdGhpcy5icm9hZGNhc3RIZWlnaHQpO1xyXG4gICAgfTtcclxuICAgIC8qKlxyXG4gICAgICogVXBkYXRlcyAnYnJvYWRjYXN0V2lkdGgnIGFuZCAnYnJvYWRjYXN0SGVpZ2h0JyBwcm9wZXJ0aWVzIHdpdGggYSBkYXRhXHJcbiAgICAgKiBmcm9tIHRoZSBjb3JyZXNwb25kaW5nIHByb3BlcnRpZXMgb2YgdGhlIGxhc3Qgb2JzZXJ2ZWQgY29udGVudCByZWN0YW5nbGUuXHJcbiAgICAgKlxyXG4gICAgICogQHJldHVybnMge0RPTVJlY3RJbml0fSBMYXN0IG9ic2VydmVkIGNvbnRlbnQgcmVjdGFuZ2xlLlxyXG4gICAgICovXHJcbiAgICBSZXNpemVPYnNlcnZhdGlvbi5wcm90b3R5cGUuYnJvYWRjYXN0UmVjdCA9IGZ1bmN0aW9uICgpIHtcclxuICAgICAgICB2YXIgcmVjdCA9IHRoaXMuY29udGVudFJlY3RfO1xyXG4gICAgICAgIHRoaXMuYnJvYWRjYXN0V2lkdGggPSByZWN0LndpZHRoO1xyXG4gICAgICAgIHRoaXMuYnJvYWRjYXN0SGVpZ2h0ID0gcmVjdC5oZWlnaHQ7XHJcbiAgICAgICAgcmV0dXJuIHJlY3Q7XHJcbiAgICB9O1xyXG4gICAgcmV0dXJuIFJlc2l6ZU9ic2VydmF0aW9uO1xyXG59KCkpO1xuXG52YXIgUmVzaXplT2JzZXJ2ZXJFbnRyeSA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uICgpIHtcclxuICAgIC8qKlxyXG4gICAgICogQ3JlYXRlcyBhbiBpbnN0YW5jZSBvZiBSZXNpemVPYnNlcnZlckVudHJ5LlxyXG4gICAgICpcclxuICAgICAqIEBwYXJhbSB7RWxlbWVudH0gdGFyZ2V0IC0gRWxlbWVudCB0aGF0IGlzIGJlaW5nIG9ic2VydmVkLlxyXG4gICAgICogQHBhcmFtIHtET01SZWN0SW5pdH0gcmVjdEluaXQgLSBEYXRhIG9mIHRoZSBlbGVtZW50J3MgY29udGVudCByZWN0YW5nbGUuXHJcbiAgICAgKi9cclxuICAgIGZ1bmN0aW9uIFJlc2l6ZU9ic2VydmVyRW50cnkodGFyZ2V0LCByZWN0SW5pdCkge1xyXG4gICAgICAgIHZhciBjb250ZW50UmVjdCA9IGNyZWF0ZVJlYWRPbmx5UmVjdChyZWN0SW5pdCk7XHJcbiAgICAgICAgLy8gQWNjb3JkaW5nIHRvIHRoZSBzcGVjaWZpY2F0aW9uIGZvbGxvd2luZyBwcm9wZXJ0aWVzIGFyZSBub3Qgd3JpdGFibGVcclxuICAgICAgICAvLyBhbmQgYXJlIGFsc28gbm90IGVudW1lcmFibGUgaW4gdGhlIG5hdGl2ZSBpbXBsZW1lbnRhdGlvbi5cclxuICAgICAgICAvL1xyXG4gICAgICAgIC8vIFByb3BlcnR5IGFjY2Vzc29ycyBhcmUgbm90IGJlaW5nIHVzZWQgYXMgdGhleSdkIHJlcXVpcmUgdG8gZGVmaW5lIGFcclxuICAgICAgICAvLyBwcml2YXRlIFdlYWtNYXAgc3RvcmFnZSB3aGljaCBtYXkgY2F1c2UgbWVtb3J5IGxlYWtzIGluIGJyb3dzZXJzIHRoYXRcclxuICAgICAgICAvLyBkb24ndCBzdXBwb3J0IHRoaXMgdHlwZSBvZiBjb2xsZWN0aW9ucy5cclxuICAgICAgICBkZWZpbmVDb25maWd1cmFibGUodGhpcywgeyB0YXJnZXQ6IHRhcmdldCwgY29udGVudFJlY3Q6IGNvbnRlbnRSZWN0IH0pO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIFJlc2l6ZU9ic2VydmVyRW50cnk7XHJcbn0oKSk7XG5cbnZhciBSZXNpemVPYnNlcnZlclNQSSA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uICgpIHtcclxuICAgIC8qKlxyXG4gICAgICogQ3JlYXRlcyBhIG5ldyBpbnN0YW5jZSBvZiBSZXNpemVPYnNlcnZlci5cclxuICAgICAqXHJcbiAgICAgKiBAcGFyYW0ge1Jlc2l6ZU9ic2VydmVyQ2FsbGJhY2t9IGNhbGxiYWNrIC0gQ2FsbGJhY2sgZnVuY3Rpb24gdGhhdCBpcyBpbnZva2VkXHJcbiAgICAgKiAgICAgIHdoZW4gb25lIG9mIHRoZSBvYnNlcnZlZCBlbGVtZW50cyBjaGFuZ2VzIGl0J3MgY29udGVudCBkaW1lbnNpb25zLlxyXG4gICAgICogQHBhcmFtIHtSZXNpemVPYnNlcnZlckNvbnRyb2xsZXJ9IGNvbnRyb2xsZXIgLSBDb250cm9sbGVyIGluc3RhbmNlIHdoaWNoXHJcbiAgICAgKiAgICAgIGlzIHJlc3BvbnNpYmxlIGZvciB0aGUgdXBkYXRlcyBvZiBvYnNlcnZlci5cclxuICAgICAqIEBwYXJhbSB7UmVzaXplT2JzZXJ2ZXJ9IGNhbGxiYWNrQ3R4IC0gUmVmZXJlbmNlIHRvIHRoZSBwdWJsaWNcclxuICAgICAqICAgICAgUmVzaXplT2JzZXJ2ZXIgaW5zdGFuY2Ugd2hpY2ggd2lsbCBiZSBwYXNzZWQgdG8gY2FsbGJhY2sgZnVuY3Rpb24uXHJcbiAgICAgKi9cclxuICAgIGZ1bmN0aW9uIFJlc2l6ZU9ic2VydmVyU1BJKGNhbGxiYWNrLCBjb250cm9sbGVyLCBjYWxsYmFja0N0eCkge1xyXG4gICAgICAgIC8qKlxyXG4gICAgICAgICAqIENvbGxlY3Rpb24gb2YgcmVzaXplIG9ic2VydmF0aW9ucyB0aGF0IGhhdmUgZGV0ZWN0ZWQgY2hhbmdlcyBpbiBkaW1lbnNpb25zXHJcbiAgICAgICAgICogb2YgZWxlbWVudHMuXHJcbiAgICAgICAgICpcclxuICAgICAgICAgKiBAcHJpdmF0ZSB7QXJyYXk8UmVzaXplT2JzZXJ2YXRpb24+fVxyXG4gICAgICAgICAqL1xyXG4gICAgICAgIHRoaXMuYWN0aXZlT2JzZXJ2YXRpb25zXyA9IFtdO1xyXG4gICAgICAgIC8qKlxyXG4gICAgICAgICAqIFJlZ2lzdHJ5IG9mIHRoZSBSZXNpemVPYnNlcnZhdGlvbiBpbnN0YW5jZXMuXHJcbiAgICAgICAgICpcclxuICAgICAgICAgKiBAcHJpdmF0ZSB7TWFwPEVsZW1lbnQsIFJlc2l6ZU9ic2VydmF0aW9uPn1cclxuICAgICAgICAgKi9cclxuICAgICAgICB0aGlzLm9ic2VydmF0aW9uc18gPSBuZXcgTWFwU2hpbSgpO1xyXG4gICAgICAgIGlmICh0eXBlb2YgY2FsbGJhY2sgIT09ICdmdW5jdGlvbicpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignVGhlIGNhbGxiYWNrIHByb3ZpZGVkIGFzIHBhcmFtZXRlciAxIGlzIG5vdCBhIGZ1bmN0aW9uLicpO1xyXG4gICAgICAgIH1cclxuICAgICAgICB0aGlzLmNhbGxiYWNrXyA9IGNhbGxiYWNrO1xyXG4gICAgICAgIHRoaXMuY29udHJvbGxlcl8gPSBjb250cm9sbGVyO1xyXG4gICAgICAgIHRoaXMuY2FsbGJhY2tDdHhfID0gY2FsbGJhY2tDdHg7XHJcbiAgICB9XHJcbiAgICAvKipcclxuICAgICAqIFN0YXJ0cyBvYnNlcnZpbmcgcHJvdmlkZWQgZWxlbWVudC5cclxuICAgICAqXHJcbiAgICAgKiBAcGFyYW0ge0VsZW1lbnR9IHRhcmdldCAtIEVsZW1lbnQgdG8gYmUgb2JzZXJ2ZWQuXHJcbiAgICAgKiBAcmV0dXJucyB7dm9pZH1cclxuICAgICAqL1xyXG4gICAgUmVzaXplT2JzZXJ2ZXJTUEkucHJvdG90eXBlLm9ic2VydmUgPSBmdW5jdGlvbiAodGFyZ2V0KSB7XHJcbiAgICAgICAgaWYgKCFhcmd1bWVudHMubGVuZ3RoKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJzEgYXJndW1lbnQgcmVxdWlyZWQsIGJ1dCBvbmx5IDAgcHJlc2VudC4nKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgLy8gRG8gbm90aGluZyBpZiBjdXJyZW50IGVudmlyb25tZW50IGRvZXNuJ3QgaGF2ZSB0aGUgRWxlbWVudCBpbnRlcmZhY2UuXHJcbiAgICAgICAgaWYgKHR5cGVvZiBFbGVtZW50ID09PSAndW5kZWZpbmVkJyB8fCAhKEVsZW1lbnQgaW5zdGFuY2VvZiBPYmplY3QpKSB7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKCEodGFyZ2V0IGluc3RhbmNlb2YgZ2V0V2luZG93T2YodGFyZ2V0KS5FbGVtZW50KSkge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdwYXJhbWV0ZXIgMSBpcyBub3Qgb2YgdHlwZSBcIkVsZW1lbnRcIi4nKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgdmFyIG9ic2VydmF0aW9ucyA9IHRoaXMub2JzZXJ2YXRpb25zXztcclxuICAgICAgICAvLyBEbyBub3RoaW5nIGlmIGVsZW1lbnQgaXMgYWxyZWFkeSBiZWluZyBvYnNlcnZlZC5cclxuICAgICAgICBpZiAob2JzZXJ2YXRpb25zLmhhcyh0YXJnZXQpKSB7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgICAgb2JzZXJ2YXRpb25zLnNldCh0YXJnZXQsIG5ldyBSZXNpemVPYnNlcnZhdGlvbih0YXJnZXQpKTtcclxuICAgICAgICB0aGlzLmNvbnRyb2xsZXJfLmFkZE9ic2VydmVyKHRoaXMpO1xyXG4gICAgICAgIC8vIEZvcmNlIHRoZSB1cGRhdGUgb2Ygb2JzZXJ2YXRpb25zLlxyXG4gICAgICAgIHRoaXMuY29udHJvbGxlcl8ucmVmcmVzaCgpO1xyXG4gICAgfTtcclxuICAgIC8qKlxyXG4gICAgICogU3RvcHMgb2JzZXJ2aW5nIHByb3ZpZGVkIGVsZW1lbnQuXHJcbiAgICAgKlxyXG4gICAgICogQHBhcmFtIHtFbGVtZW50fSB0YXJnZXQgLSBFbGVtZW50IHRvIHN0b3Agb2JzZXJ2aW5nLlxyXG4gICAgICogQHJldHVybnMge3ZvaWR9XHJcbiAgICAgKi9cclxuICAgIFJlc2l6ZU9ic2VydmVyU1BJLnByb3RvdHlwZS51bm9ic2VydmUgPSBmdW5jdGlvbiAodGFyZ2V0KSB7XHJcbiAgICAgICAgaWYgKCFhcmd1bWVudHMubGVuZ3RoKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJzEgYXJndW1lbnQgcmVxdWlyZWQsIGJ1dCBvbmx5IDAgcHJlc2VudC4nKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgLy8gRG8gbm90aGluZyBpZiBjdXJyZW50IGVudmlyb25tZW50IGRvZXNuJ3QgaGF2ZSB0aGUgRWxlbWVudCBpbnRlcmZhY2UuXHJcbiAgICAgICAgaWYgKHR5cGVvZiBFbGVtZW50ID09PSAndW5kZWZpbmVkJyB8fCAhKEVsZW1lbnQgaW5zdGFuY2VvZiBPYmplY3QpKSB7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKCEodGFyZ2V0IGluc3RhbmNlb2YgZ2V0V2luZG93T2YodGFyZ2V0KS5FbGVtZW50KSkge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdwYXJhbWV0ZXIgMSBpcyBub3Qgb2YgdHlwZSBcIkVsZW1lbnRcIi4nKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgdmFyIG9ic2VydmF0aW9ucyA9IHRoaXMub2JzZXJ2YXRpb25zXztcclxuICAgICAgICAvLyBEbyBub3RoaW5nIGlmIGVsZW1lbnQgaXMgbm90IGJlaW5nIG9ic2VydmVkLlxyXG4gICAgICAgIGlmICghb2JzZXJ2YXRpb25zLmhhcyh0YXJnZXQpKSB7XHJcbiAgICAgICAgICAgIHJldHVybjtcclxuICAgICAgICB9XHJcbiAgICAgICAgb2JzZXJ2YXRpb25zLmRlbGV0ZSh0YXJnZXQpO1xyXG4gICAgICAgIGlmICghb2JzZXJ2YXRpb25zLnNpemUpIHtcclxuICAgICAgICAgICAgdGhpcy5jb250cm9sbGVyXy5yZW1vdmVPYnNlcnZlcih0aGlzKTtcclxuICAgICAgICB9XHJcbiAgICB9O1xyXG4gICAgLyoqXHJcbiAgICAgKiBTdG9wcyBvYnNlcnZpbmcgYWxsIGVsZW1lbnRzLlxyXG4gICAgICpcclxuICAgICAqIEByZXR1cm5zIHt2b2lkfVxyXG4gICAgICovXHJcbiAgICBSZXNpemVPYnNlcnZlclNQSS5wcm90b3R5cGUuZGlzY29ubmVjdCA9IGZ1bmN0aW9uICgpIHtcclxuICAgICAgICB0aGlzLmNsZWFyQWN0aXZlKCk7XHJcbiAgICAgICAgdGhpcy5vYnNlcnZhdGlvbnNfLmNsZWFyKCk7XHJcbiAgICAgICAgdGhpcy5jb250cm9sbGVyXy5yZW1vdmVPYnNlcnZlcih0aGlzKTtcclxuICAgIH07XHJcbiAgICAvKipcclxuICAgICAqIENvbGxlY3RzIG9ic2VydmF0aW9uIGluc3RhbmNlcyB0aGUgYXNzb2NpYXRlZCBlbGVtZW50IG9mIHdoaWNoIGhhcyBjaGFuZ2VkXHJcbiAgICAgKiBpdCdzIGNvbnRlbnQgcmVjdGFuZ2xlLlxyXG4gICAgICpcclxuICAgICAqIEByZXR1cm5zIHt2b2lkfVxyXG4gICAgICovXHJcbiAgICBSZXNpemVPYnNlcnZlclNQSS5wcm90b3R5cGUuZ2F0aGVyQWN0aXZlID0gZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIHZhciBfdGhpcyA9IHRoaXM7XHJcbiAgICAgICAgdGhpcy5jbGVhckFjdGl2ZSgpO1xyXG4gICAgICAgIHRoaXMub2JzZXJ2YXRpb25zXy5mb3JFYWNoKGZ1bmN0aW9uIChvYnNlcnZhdGlvbikge1xyXG4gICAgICAgICAgICBpZiAob2JzZXJ2YXRpb24uaXNBY3RpdmUoKSkge1xyXG4gICAgICAgICAgICAgICAgX3RoaXMuYWN0aXZlT2JzZXJ2YXRpb25zXy5wdXNoKG9ic2VydmF0aW9uKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG4gICAgfTtcclxuICAgIC8qKlxyXG4gICAgICogSW52b2tlcyBpbml0aWFsIGNhbGxiYWNrIGZ1bmN0aW9uIHdpdGggYSBsaXN0IG9mIFJlc2l6ZU9ic2VydmVyRW50cnlcclxuICAgICAqIGluc3RhbmNlcyBjb2xsZWN0ZWQgZnJvbSBhY3RpdmUgcmVzaXplIG9ic2VydmF0aW9ucy5cclxuICAgICAqXHJcbiAgICAgKiBAcmV0dXJucyB7dm9pZH1cclxuICAgICAqL1xyXG4gICAgUmVzaXplT2JzZXJ2ZXJTUEkucHJvdG90eXBlLmJyb2FkY2FzdEFjdGl2ZSA9IGZ1bmN0aW9uICgpIHtcclxuICAgICAgICAvLyBEbyBub3RoaW5nIGlmIG9ic2VydmVyIGRvZXNuJ3QgaGF2ZSBhY3RpdmUgb2JzZXJ2YXRpb25zLlxyXG4gICAgICAgIGlmICghdGhpcy5oYXNBY3RpdmUoKSkge1xyXG4gICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHZhciBjdHggPSB0aGlzLmNhbGxiYWNrQ3R4XztcclxuICAgICAgICAvLyBDcmVhdGUgUmVzaXplT2JzZXJ2ZXJFbnRyeSBpbnN0YW5jZSBmb3IgZXZlcnkgYWN0aXZlIG9ic2VydmF0aW9uLlxyXG4gICAgICAgIHZhciBlbnRyaWVzID0gdGhpcy5hY3RpdmVPYnNlcnZhdGlvbnNfLm1hcChmdW5jdGlvbiAob2JzZXJ2YXRpb24pIHtcclxuICAgICAgICAgICAgcmV0dXJuIG5ldyBSZXNpemVPYnNlcnZlckVudHJ5KG9ic2VydmF0aW9uLnRhcmdldCwgb2JzZXJ2YXRpb24uYnJvYWRjYXN0UmVjdCgpKTtcclxuICAgICAgICB9KTtcclxuICAgICAgICB0aGlzLmNhbGxiYWNrXy5jYWxsKGN0eCwgZW50cmllcywgY3R4KTtcclxuICAgICAgICB0aGlzLmNsZWFyQWN0aXZlKCk7XHJcbiAgICB9O1xyXG4gICAgLyoqXHJcbiAgICAgKiBDbGVhcnMgdGhlIGNvbGxlY3Rpb24gb2YgYWN0aXZlIG9ic2VydmF0aW9ucy5cclxuICAgICAqXHJcbiAgICAgKiBAcmV0dXJucyB7dm9pZH1cclxuICAgICAqL1xyXG4gICAgUmVzaXplT2JzZXJ2ZXJTUEkucHJvdG90eXBlLmNsZWFyQWN0aXZlID0gZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIHRoaXMuYWN0aXZlT2JzZXJ2YXRpb25zXy5zcGxpY2UoMCk7XHJcbiAgICB9O1xyXG4gICAgLyoqXHJcbiAgICAgKiBUZWxscyB3aGV0aGVyIG9ic2VydmVyIGhhcyBhY3RpdmUgb2JzZXJ2YXRpb25zLlxyXG4gICAgICpcclxuICAgICAqIEByZXR1cm5zIHtib29sZWFufVxyXG4gICAgICovXHJcbiAgICBSZXNpemVPYnNlcnZlclNQSS5wcm90b3R5cGUuaGFzQWN0aXZlID0gZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIHJldHVybiB0aGlzLmFjdGl2ZU9ic2VydmF0aW9uc18ubGVuZ3RoID4gMDtcclxuICAgIH07XHJcbiAgICByZXR1cm4gUmVzaXplT2JzZXJ2ZXJTUEk7XHJcbn0oKSk7XG5cbi8vIFJlZ2lzdHJ5IG9mIGludGVybmFsIG9ic2VydmVycy4gSWYgV2Vha01hcCBpcyBub3QgYXZhaWxhYmxlIHVzZSBjdXJyZW50IHNoaW1cclxuLy8gZm9yIHRoZSBNYXAgY29sbGVjdGlvbiBhcyBpdCBoYXMgYWxsIHJlcXVpcmVkIG1ldGhvZHMgYW5kIGJlY2F1c2UgV2Vha01hcFxyXG4vLyBjYW4ndCBiZSBmdWxseSBwb2x5ZmlsbGVkIGFueXdheS5cclxudmFyIG9ic2VydmVycyA9IHR5cGVvZiBXZWFrTWFwICE9PSAndW5kZWZpbmVkJyA/IG5ldyBXZWFrTWFwKCkgOiBuZXcgTWFwU2hpbSgpO1xyXG4vKipcclxuICogUmVzaXplT2JzZXJ2ZXIgQVBJLiBFbmNhcHN1bGF0ZXMgdGhlIFJlc2l6ZU9ic2VydmVyIFNQSSBpbXBsZW1lbnRhdGlvblxyXG4gKiBleHBvc2luZyBvbmx5IHRob3NlIG1ldGhvZHMgYW5kIHByb3BlcnRpZXMgdGhhdCBhcmUgZGVmaW5lZCBpbiB0aGUgc3BlYy5cclxuICovXHJcbnZhciBSZXNpemVPYnNlcnZlciA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uICgpIHtcclxuICAgIC8qKlxyXG4gICAgICogQ3JlYXRlcyBhIG5ldyBpbnN0YW5jZSBvZiBSZXNpemVPYnNlcnZlci5cclxuICAgICAqXHJcbiAgICAgKiBAcGFyYW0ge1Jlc2l6ZU9ic2VydmVyQ2FsbGJhY2t9IGNhbGxiYWNrIC0gQ2FsbGJhY2sgdGhhdCBpcyBpbnZva2VkIHdoZW5cclxuICAgICAqICAgICAgZGltZW5zaW9ucyBvZiB0aGUgb2JzZXJ2ZWQgZWxlbWVudHMgY2hhbmdlLlxyXG4gICAgICovXHJcbiAgICBmdW5jdGlvbiBSZXNpemVPYnNlcnZlcihjYWxsYmFjaykge1xyXG4gICAgICAgIGlmICghKHRoaXMgaW5zdGFuY2VvZiBSZXNpemVPYnNlcnZlcikpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignQ2Fubm90IGNhbGwgYSBjbGFzcyBhcyBhIGZ1bmN0aW9uLicpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoIWFyZ3VtZW50cy5sZW5ndGgpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcignMSBhcmd1bWVudCByZXF1aXJlZCwgYnV0IG9ubHkgMCBwcmVzZW50LicpO1xyXG4gICAgICAgIH1cclxuICAgICAgICB2YXIgY29udHJvbGxlciA9IFJlc2l6ZU9ic2VydmVyQ29udHJvbGxlci5nZXRJbnN0YW5jZSgpO1xyXG4gICAgICAgIHZhciBvYnNlcnZlciA9IG5ldyBSZXNpemVPYnNlcnZlclNQSShjYWxsYmFjaywgY29udHJvbGxlciwgdGhpcyk7XHJcbiAgICAgICAgb2JzZXJ2ZXJzLnNldCh0aGlzLCBvYnNlcnZlcik7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gUmVzaXplT2JzZXJ2ZXI7XHJcbn0oKSk7XHJcbi8vIEV4cG9zZSBwdWJsaWMgbWV0aG9kcyBvZiBSZXNpemVPYnNlcnZlci5cclxuW1xyXG4gICAgJ29ic2VydmUnLFxyXG4gICAgJ3Vub2JzZXJ2ZScsXHJcbiAgICAnZGlzY29ubmVjdCdcclxuXS5mb3JFYWNoKGZ1bmN0aW9uIChtZXRob2QpIHtcclxuICAgIFJlc2l6ZU9ic2VydmVyLnByb3RvdHlwZVttZXRob2RdID0gZnVuY3Rpb24gKCkge1xyXG4gICAgICAgIHZhciBfYTtcclxuICAgICAgICByZXR1cm4gKF9hID0gb2JzZXJ2ZXJzLmdldCh0aGlzKSlbbWV0aG9kXS5hcHBseShfYSwgYXJndW1lbnRzKTtcclxuICAgIH07XHJcbn0pO1xuXG52YXIgaW5kZXggPSAoZnVuY3Rpb24gKCkge1xyXG4gICAgLy8gRXhwb3J0IGV4aXN0aW5nIGltcGxlbWVudGF0aW9uIGlmIGF2YWlsYWJsZS5cclxuICAgIGlmICh0eXBlb2YgZ2xvYmFsJDEuUmVzaXplT2JzZXJ2ZXIgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgcmV0dXJuIGdsb2JhbCQxLlJlc2l6ZU9ic2VydmVyO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIFJlc2l6ZU9ic2VydmVyO1xyXG59KSgpO1xuXG5leHBvcnQgZGVmYXVsdCBpbmRleDtcbiJdLCJuYW1lcyI6WyJNYXBTaGltIiwiTWFwIiwiZ2V0SW5kZXgiLCJhcnIiLCJrZXkiLCJyZXN1bHQiLCJzb21lIiwiZW50cnkiLCJpbmRleCIsImNsYXNzXzEiLCJfX2VudHJpZXNfXyIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwicHJvdG90eXBlIiwiZ2V0IiwibGVuZ3RoIiwiZW51bWVyYWJsZSIsImNvbmZpZ3VyYWJsZSIsInNldCIsInZhbHVlIiwicHVzaCIsImRlbGV0ZSIsImVudHJpZXMiLCJzcGxpY2UiLCJoYXMiLCJjbGVhciIsImZvckVhY2giLCJjYWxsYmFjayIsImN0eCIsIl9pIiwiX2EiLCJjYWxsIiwiaXNCcm93c2VyIiwiZG9jdW1lbnQiLCJ3aW5kb3ciLCJnbG9iYWwkMSIsImdsb2JhbCIsIk1hdGgiLCJzZWxmIiwiRnVuY3Rpb24iLCJyZXF1ZXN0QW5pbWF0aW9uRnJhbWUkMSIsInJlcXVlc3RBbmltYXRpb25GcmFtZSIsImJpbmQiLCJzZXRUaW1lb3V0IiwiRGF0ZSIsIm5vdyIsInRyYWlsaW5nVGltZW91dCIsInRocm90dGxlIiwiZGVsYXkiLCJsZWFkaW5nQ2FsbCIsInRyYWlsaW5nQ2FsbCIsImxhc3RDYWxsVGltZSIsInJlc29sdmVQZW5kaW5nIiwicHJveHkiLCJ0aW1lb3V0Q2FsbGJhY2siLCJ0aW1lU3RhbXAiLCJSRUZSRVNIX0RFTEFZIiwidHJhbnNpdGlvbktleXMiLCJtdXRhdGlvbk9ic2VydmVyU3VwcG9ydGVkIiwiTXV0YXRpb25PYnNlcnZlciIsIlJlc2l6ZU9ic2VydmVyQ29udHJvbGxlciIsImNvbm5lY3RlZF8iLCJtdXRhdGlvbkV2ZW50c0FkZGVkXyIsIm11dGF0aW9uc09ic2VydmVyXyIsIm9ic2VydmVyc18iLCJvblRyYW5zaXRpb25FbmRfIiwicmVmcmVzaCIsImFkZE9ic2VydmVyIiwib2JzZXJ2ZXIiLCJpbmRleE9mIiwiY29ubmVjdF8iLCJyZW1vdmVPYnNlcnZlciIsIm9ic2VydmVycyIsImRpc2Nvbm5lY3RfIiwiY2hhbmdlc0RldGVjdGVkIiwidXBkYXRlT2JzZXJ2ZXJzXyIsImFjdGl2ZU9ic2VydmVycyIsImZpbHRlciIsImdhdGhlckFjdGl2ZSIsImhhc0FjdGl2ZSIsImJyb2FkY2FzdEFjdGl2ZSIsImFkZEV2ZW50TGlzdGVuZXIiLCJvYnNlcnZlIiwiYXR0cmlidXRlcyIsImNoaWxkTGlzdCIsImNoYXJhY3RlckRhdGEiLCJzdWJ0cmVlIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImRpc2Nvbm5lY3QiLCJfYiIsInByb3BlcnR5TmFtZSIsImlzUmVmbG93UHJvcGVydHkiLCJnZXRJbnN0YW5jZSIsImluc3RhbmNlXyIsImRlZmluZUNvbmZpZ3VyYWJsZSIsInRhcmdldCIsInByb3BzIiwia2V5cyIsIndyaXRhYmxlIiwiZ2V0V2luZG93T2YiLCJvd25lckdsb2JhbCIsIm93bmVyRG9jdW1lbnQiLCJkZWZhdWx0VmlldyIsImVtcHR5UmVjdCIsImNyZWF0ZVJlY3RJbml0IiwidG9GbG9hdCIsInBhcnNlRmxvYXQiLCJnZXRCb3JkZXJzU2l6ZSIsInN0eWxlcyIsInBvc2l0aW9ucyIsImFyZ3VtZW50cyIsInJlZHVjZSIsInNpemUiLCJwb3NpdGlvbiIsImdldFBhZGRpbmdzIiwicGFkZGluZ3MiLCJwb3NpdGlvbnNfMSIsImdldFNWR0NvbnRlbnRSZWN0IiwiYmJveCIsImdldEJCb3giLCJ3aWR0aCIsImhlaWdodCIsImdldEhUTUxFbGVtZW50Q29udGVudFJlY3QiLCJjbGllbnRXaWR0aCIsImNsaWVudEhlaWdodCIsImdldENvbXB1dGVkU3R5bGUiLCJob3JpelBhZCIsImxlZnQiLCJyaWdodCIsInZlcnRQYWQiLCJ0b3AiLCJib3R0b20iLCJib3hTaXppbmciLCJyb3VuZCIsImlzRG9jdW1lbnRFbGVtZW50IiwidmVydFNjcm9sbGJhciIsImhvcml6U2Nyb2xsYmFyIiwiYWJzIiwiaXNTVkdHcmFwaGljc0VsZW1lbnQiLCJTVkdHcmFwaGljc0VsZW1lbnQiLCJTVkdFbGVtZW50IiwiZG9jdW1lbnRFbGVtZW50IiwiZ2V0Q29udGVudFJlY3QiLCJjcmVhdGVSZWFkT25seVJlY3QiLCJ4IiwieSIsIkNvbnN0ciIsIkRPTVJlY3RSZWFkT25seSIsInJlY3QiLCJjcmVhdGUiLCJSZXNpemVPYnNlcnZhdGlvbiIsImJyb2FkY2FzdFdpZHRoIiwiYnJvYWRjYXN0SGVpZ2h0IiwiY29udGVudFJlY3RfIiwiaXNBY3RpdmUiLCJicm9hZGNhc3RSZWN0IiwiUmVzaXplT2JzZXJ2ZXJFbnRyeSIsInJlY3RJbml0IiwiY29udGVudFJlY3QiLCJSZXNpemVPYnNlcnZlclNQSSIsImNvbnRyb2xsZXIiLCJjYWxsYmFja0N0eCIsImFjdGl2ZU9ic2VydmF0aW9uc18iLCJvYnNlcnZhdGlvbnNfIiwiVHlwZUVycm9yIiwiY2FsbGJhY2tfIiwiY29udHJvbGxlcl8iLCJjYWxsYmFja0N0eF8iLCJFbGVtZW50Iiwib2JzZXJ2YXRpb25zIiwidW5vYnNlcnZlIiwiY2xlYXJBY3RpdmUiLCJfdGhpcyIsIm9ic2VydmF0aW9uIiwibWFwIiwiV2Vha01hcCIsIlJlc2l6ZU9ic2VydmVyIiwibWV0aG9kIiwiYXBwbHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js\n");

/***/ })

};
;