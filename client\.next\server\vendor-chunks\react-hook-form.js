"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form";
exports.ids = ["vendor-chunks/react-hook-form"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/react-hook-form/dist/index.esm.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nvar isCheckBoxInput = (element)=>element.type === \"checkbox\";\nvar isDateObject = (value1)=>value1 instanceof Date;\nvar isNullOrUndefined = (value1)=>value1 == null;\nconst isObjectType = (value1)=>typeof value1 === \"object\";\nvar isObject = (value1)=>!isNullOrUndefined(value1) && !Array.isArray(value1) && isObjectType(value1) && !isDateObject(value1);\nvar getEventValue = (event)=>isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = (name)=>name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name)=>names.has(getNodeParentName(name));\nvar isPlainObject = (tempObject)=>{\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty(\"isPrototypeOf\");\n};\nvar isWeb =  false && 0;\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    if (data instanceof Date) {\n        copy = new Date(data);\n    } else if (data instanceof Set) {\n        copy = new Set(data);\n    } else if (!(isWeb && (data instanceof Blob || data instanceof FileList)) && (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        } else {\n            for(const key in data){\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    } else {\n        return data;\n    }\n    return copy;\n}\nvar compact = (value1)=>Array.isArray(value1) ? value1.filter(Boolean) : [];\nvar isUndefined = (val)=>val === undefined;\nvar get = (object, path, defaultValue)=>{\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key)=>isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = (value1)=>typeof value1 === \"boolean\";\nvar isKey = (value1)=>/^\\w*$/.test(value1);\nvar stringToPath = (input)=>compact(input.replace(/[\"|']|\\]/g, \"\").split(/\\.|\\[/));\nvar set = (object, path, value1)=>{\n    let index = -1;\n    const tempPath = isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while(++index < length){\n        const key = tempPath[index];\n        let newValue = value1;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n        }\n        if (key === \"__proto__\") {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n    return object;\n};\nconst EVENTS = {\n    BLUR: \"blur\",\n    FOCUS_OUT: \"focusout\",\n    CHANGE: \"change\"\n};\nconst VALIDATION_MODE = {\n    onBlur: \"onBlur\",\n    onChange: \"onChange\",\n    onSubmit: \"onSubmit\",\n    onTouched: \"onTouched\",\n    all: \"all\"\n};\nconst INPUT_VALIDATION_RULES = {\n    max: \"max\",\n    min: \"min\",\n    maxLength: \"maxLength\",\n    minLength: \"minLength\",\n    pattern: \"pattern\",\n    required: \"required\",\n    validate: \"validate\"\n};\nconst HookFormContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const useFormContext = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const FormProvider = (props)=>{\n    const { children, ...data } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, {\n        value: data\n    }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true)=>{\n    const result = {\n        defaultValues: control._defaultValues\n    };\n    for(const key in formState){\n        Object.defineProperty(result, key, {\n            get: ()=>{\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            }\n        });\n    }\n    return result;\n};\nvar isEmptyObject = (value1)=>isObject(value1) && !Object.keys(value1).length;\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot)=>{\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find((key)=>_proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar convertToArrayPayload = (value1)=>Array.isArray(value1) ? value1 : [\n        value1\n    ];\nvar shouldSubscribeByName = (name, signalName, exact)=>!name || !signalName || name === signalName || convertToArrayPayload(name).some((currentName)=>currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nfunction useSubscribe(props) {\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    _props.current = props;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const subscription = !props.disabled && _props.current.subject && _props.current.subject.subscribe({\n            next: _props.current.next\n        });\n        return ()=>{\n            subscription && subscription.unsubscribe();\n        };\n    }, [\n        props.disabled\n    ]);\n}\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _mounted = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    });\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    _name.current = name;\n    useSubscribe({\n        disabled,\n        next: (value1)=>_mounted.current && shouldSubscribeByName(_name.current, value1.name, exact) && shouldRenderFormState(value1, _localProxyFormState.current, control._updateFormState) && updateFormState({\n                ...control._formState,\n                ...value1\n            }),\n        subject: control._subjects.state\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        _mounted.current = true;\n        _localProxyFormState.current.isValid && control._updateValid(true);\n        return ()=>{\n            _mounted.current = false;\n        };\n    }, [\n        control\n    ]);\n    return getProxyFormState(formState, control, _localProxyFormState.current, false);\n}\nvar isString = (value1)=>typeof value1 === \"string\";\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue)=>{\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName)=>(isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */ function useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact } = props || {};\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    _name.current = name;\n    useSubscribe({\n        disabled,\n        subject: control._subjects.values,\n        next: (formState)=>{\n            if (shouldSubscribeByName(_name.current, formState.name, exact)) {\n                updateValue(cloneObject(generateWatchOutput(_name.current, control._names, formState.values || control._formValues, false, defaultValue)));\n            }\n        }\n    });\n    const [value1, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, defaultValue));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._removeUnmounted());\n    return value1;\n}\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */ function useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value1 = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true\n    });\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value: value1,\n        ...isBoolean(props.disabled) ? {\n            disabled: props.disabled\n        } : {}\n    }));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        const updateMounted = (name, value1)=>{\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value1;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value1 = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value1);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value1);\n            }\n        }\n        return ()=>{\n            (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        isArrayField,\n        shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (get(control._fields, name)) {\n            control._updateDisabledField({\n                disabled,\n                fields: control._fields,\n                name,\n                value: get(control._fields, name)._f.value\n            });\n        }\n    }, [\n        disabled,\n        name,\n        control\n    ]);\n    return {\n        field: {\n            name,\n            value: value1,\n            ...isBoolean(disabled) || formState.disabled ? {\n                disabled: formState.disabled || disabled\n            } : {},\n            onChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>_registerProps.current.onChange({\n                    target: {\n                        value: getEventValue(event),\n                        name: name\n                    },\n                    type: EVENTS.CHANGE\n                }), [\n                name\n            ]),\n            onBlur: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>_registerProps.current.onBlur({\n                    target: {\n                        value: get(control._formValues, name),\n                        name: name\n                    },\n                    type: EVENTS.BLUR\n                }), [\n                name,\n                control\n            ]),\n            ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((elm)=>{\n                const field = get(control._fields, name);\n                if (field && elm) {\n                    field._f.ref = {\n                        focus: ()=>elm.focus(),\n                        select: ()=>elm.select(),\n                        setCustomValidity: (message)=>elm.setCustomValidity(message),\n                        reportValidity: ()=>elm.reportValidity()\n                    };\n                }\n            }, [\n                control._fields,\n                name\n            ])\n        },\n        formState,\n        fieldState: Object.defineProperties({}, {\n            invalid: {\n                enumerable: true,\n                get: ()=>!!get(formState.errors, name)\n            },\n            isDirty: {\n                enumerable: true,\n                get: ()=>!!get(formState.dirtyFields, name)\n            },\n            isTouched: {\n                enumerable: true,\n                get: ()=>!!get(formState.touchedFields, name)\n            },\n            isValidating: {\n                enumerable: true,\n                get: ()=>!!get(formState.validatingFields, name)\n            },\n            error: {\n                enumerable: true,\n                get: ()=>get(formState.errors, name)\n            }\n        })\n    };\n}\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */ const Controller = (props)=>props.render(useController(props));\nconst POST_REQUEST = \"post\";\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */ function Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event)=>{\n        let hasError = false;\n        let type = \"\";\n        await control.handleSubmit(async (data)=>{\n            const formData = new FormData();\n            let formDataJson = \"\";\n            try {\n                formDataJson = JSON.stringify(data);\n            } catch (_a) {}\n            for (const name of control._names.mount){\n                formData.append(name, get(data, name));\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers[\"Content-Type\"],\n                        encType\n                    ].some((value1)=>value1 && value1.includes(\"json\"));\n                    const response = await fetch(action, {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...encType ? {\n                                \"Content-Type\": encType\n                            } : {}\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData\n                    });\n                    if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({\n                            response\n                        });\n                        type = String(response.status);\n                    } else {\n                        onSuccess && onSuccess({\n                            response\n                        });\n                    }\n                } catch (error) {\n                    hasError = true;\n                    onError && onError({\n                        error\n                    });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false\n            });\n            props.control.setError(\"root.server\", {\n                type\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    return render ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit\n    })) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", {\n        noValidate: mounted,\n        action: action,\n        method: method,\n        encType: encType,\n        onSubmit: submit,\n        ...rest\n    }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message)=>validateAllFieldCriteria ? {\n        ...errors[name],\n        types: {\n            ...errors[name] && errors[name].types ? errors[name].types : {},\n            [type]: message || true\n        }\n    } : {};\nvar generateId = ()=>{\n    const d = typeof performance === \"undefined\" ? Date.now() : performance.now() * 1000;\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c)=>{\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == \"x\" ? r : r & 0x3 | 0x8).toString(16);\n    });\n};\nvar getFocusFieldName = (name, index, options = {})=>options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : \"\";\nvar getValidationModes = (mode)=>({\n        isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n        isOnBlur: mode === VALIDATION_MODE.onBlur,\n        isOnChange: mode === VALIDATION_MODE.onChange,\n        isOnAll: mode === VALIDATION_MODE.all,\n        isOnTouch: mode === VALIDATION_MODE.onTouched\n    });\nvar isWatched = (name, _names, isBlurEvent)=>!isBlurEvent && (_names.watchAll || _names.watch.has(name) || [\n        ..._names.watch\n    ].some((watchName)=>name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly)=>{\n    for (const key of fieldsNames || Object.keys(fields)){\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                } else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            } else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\nvar updateFieldArrayRootError = (errors, error, name)=>{\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, \"root\", error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\nvar isFileInput = (element)=>element.type === \"file\";\nvar isFunction = (value1)=>typeof value1 === \"function\";\nvar isHTMLElement = (value1)=>{\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value1 ? value1.ownerDocument : 0;\n    return value1 instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMessage = (value1)=>isString(value1);\nvar isRadioInput = (element)=>element.type === \"radio\";\nvar isRegex = (value1)=>value1 instanceof RegExp;\nconst defaultResult = {\n    value: false,\n    isValid: false\n};\nconst validResult = {\n    value: true,\n    isValid: true\n};\nvar getCheckboxValue = (options)=>{\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options.filter((option)=>option && option.checked && !option.disabled).map((option)=>option.value);\n            return {\n                value: values,\n                isValid: !!values.length\n            };\n        }\n        return options[0].checked && !options[0].disabled ? options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === \"\" ? validResult : {\n            value: options[0].value,\n            isValid: true\n        } : validResult : defaultResult;\n    }\n    return defaultResult;\n};\nconst defaultReturn = {\n    isValid: false,\n    value: null\n};\nvar getRadioValue = (options)=>Array.isArray(options) ? options.reduce((previous, option)=>option && option.checked && !option.disabled ? {\n            isValid: true,\n            value: option.value\n        } : previous, defaultReturn) : defaultReturn;\nfunction getValidateError(result, ref, type = \"validate\") {\n    if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n        return {\n            type,\n            message: isMessage(result) ? result : \"\",\n            ref\n        };\n    }\n}\nvar getValueAndMessage = (validationData)=>isObject(validationData) && !isRegex(validationData) ? validationData : {\n        value: validationData,\n        message: \"\"\n    };\nvar validateField = async (field, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray)=>{\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount, disabled } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabled) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message)=>{\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? \"\" : message || \"\");\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === \"\" || inputValue === \"\" || Array.isArray(inputValue) && !inputValue.length;\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength)=>{\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n        };\n    };\n    if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n        const { value: value1, message } = isMessage(required) ? {\n            value: !!required,\n            message: required\n        } : getValueAndMessage(required);\n        if (value1) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        } else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time)=>new Date(new Date().toDateString() + \" \" + time);\n            const isTime = ref.type == \"time\";\n            const isWeek = ref.type == \"week\";\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        } else if (isObject(validate)) {\n            let validationResult = {};\n            for(const key in validate){\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message)\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\nvar appendAt = (data, value1)=>[\n        ...data,\n        ...convertToArrayPayload(value1)\n    ];\nvar fillEmptyArray = (value1)=>Array.isArray(value1) ? value1.map(()=>undefined) : undefined;\nfunction insert(data, index, value1) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value1),\n        ...data.slice(index)\n    ];\n}\nvar moveArrayAt = (data, from, to)=>{\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\nvar prependAt = (data, value1)=>[\n        ...convertToArrayPayload(value1),\n        ...convertToArrayPayload(data)\n    ];\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [\n        ...data\n    ];\n    for (const index of indexes){\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index)=>isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b)=>a - b));\nvar swapArrayAt = (data, indexA, indexB)=>{\n    [data[indexA], data[indexB]] = [\n        data[indexB],\n        data[indexA]\n    ];\n};\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while(index < length){\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for(const key in obj){\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path) ? path : isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\nvar updateAt = (fieldValues, index, value1)=>{\n    fieldValues[index] = value1;\n    return fieldValues;\n};\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = \"id\", shouldUnregister } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    props.rules && control.register(name, props.rules);\n    useSubscribe({\n        next: ({ values, name: fieldArrayName })=>{\n            if (fieldArrayName === _name.current || !fieldArrayName) {\n                const fieldValues = get(values, _name.current);\n                if (Array.isArray(fieldValues)) {\n                    setFields(fieldValues);\n                    ids.current = fieldValues.map(generateId);\n                }\n            }\n        },\n        subject: control._subjects.array\n    });\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues)=>{\n        _actioned.current = true;\n        control._updateFieldArray(name, updatedFieldArrayValues);\n    }, [\n        control,\n        name\n    ]);\n    const append = (value1, options)=>{\n        const appendValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const prepend = (value1, options)=>{\n        const prependValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const remove = (index)=>{\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index\n        });\n    };\n    const insert$1 = (index, value1, options)=>{\n        const insertValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value1)\n        });\n    };\n    const swap = (indexA, indexB)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB\n        }, false);\n    };\n    const move = (from, to)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._updateFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to\n        }, false);\n    };\n    const update = (index, value1)=>{\n        const updateValue = cloneObject(value1);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [\n            ...updatedFieldArrayValues\n        ].map((item, i)=>!item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._updateFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue\n        }, true, false);\n    };\n    const replace = (value1)=>{\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value1));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([\n            ...updatedFieldArrayValues\n        ]);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._updateFieldArray(name, [\n            ...updatedFieldArrayValues\n        ], (data)=>data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._state.action = false;\n        isWatched(name, control._names) && control._subjects.state.next({\n            ...control._formState\n        });\n        if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted)) {\n            if (control._options.resolver) {\n                control._executeSchema([\n                    name\n                ]).then((result)=>{\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n                        error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors\n                        });\n                    }\n                });\n            } else {\n                const field = get(control._fields, name);\n                if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error)=>!isEmptyObject(error) && control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n                        }));\n                }\n            }\n        }\n        control._subjects.values.next({\n            name,\n            values: {\n                ...control._formValues\n            }\n        });\n        control._names.focus && iterateFieldsByAction(control._fields, (ref, key)=>{\n            if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n                ref.focus();\n                return 1;\n            }\n            return;\n        });\n        control._names.focus = \"\";\n        control._updateValid();\n        _actioned.current = false;\n    }, [\n        fields,\n        name,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        !get(control._formValues, name) && control._updateFieldArray(name);\n        return ()=>{\n            (control._options.shouldUnregister || shouldUnregister) && control.unregister(name);\n        };\n    }, [\n        name,\n        control,\n        keyName,\n        shouldUnregister\n    ]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [\n            updateValues,\n            name,\n            control\n        ]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [\n            updateValues,\n            name,\n            control\n        ]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [\n            updateValues,\n            name,\n            control\n        ]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [\n            updateValues,\n            name,\n            control\n        ]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [\n            updateValues,\n            name,\n            control\n        ]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [\n            updateValues,\n            name,\n            control\n        ]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [\n            updateValues,\n            name,\n            control\n        ]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [\n            updateValues,\n            name,\n            control\n        ]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>fields.map((field, index)=>({\n                    ...field,\n                    [keyName]: ids.current[index] || generateId()\n                })), [\n            fields,\n            keyName\n        ])\n    };\n}\nvar createSubject = ()=>{\n    let _observers = [];\n    const next = (value1)=>{\n        for (const observer of _observers){\n            observer.next && observer.next(value1);\n        }\n    };\n    const subscribe = (observer)=>{\n        _observers.push(observer);\n        return {\n            unsubscribe: ()=>{\n                _observers = _observers.filter((o)=>o !== observer);\n            }\n        };\n    };\n    const unsubscribe = ()=>{\n        _observers = [];\n    };\n    return {\n        get observers () {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe\n    };\n};\nvar isPrimitive = (value1)=>isNullOrUndefined(value1) || !isObjectType(value1);\nfunction deepEqual(object1, object2) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1){\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== \"ref\") {\n            const val2 = object2[key];\n            if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2) : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nvar isMultipleSelect = (element)=>element.type === `select-multiple`;\nvar isRadioOrCheckbox = (ref)=>isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = (ref)=>isHTMLElement(ref) && ref.isConnected;\nvar objectHasFunction = (data)=>{\n    for(const key in data){\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            } else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n                        ...markFieldsDirty(data[key])\n                    };\n                } else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            } else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues)=>getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nvar getFieldValueAs = (value1, { valueAsNumber, valueAsDate, setValueAs })=>isUndefined(value1) ? value1 : valueAsNumber ? value1 === \"\" ? NaN : value1 ? +value1 : value1 : valueAsDate && isString(value1) ? new Date(value1) : setValueAs ? setValueAs(value1) : value1;\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (_f.refs ? _f.refs.every((ref)=>ref.disabled) : ref.disabled) {\n        return;\n    }\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [\n            ...ref.selectedOptions\n        ].map(({ value: value1 })=>value1);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation)=>{\n    const fields = {};\n    for (const name of fieldsNames){\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [\n            ...fieldsNames\n        ],\n        fields,\n        shouldUseNativeValidation\n    };\n};\nvar getRuleValue = (rule)=>isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nconst ASYNC_FUNCTION = \"AsyncFunction\";\nvar hasPromiseValidation = (fieldReference)=>(!fieldReference || !fieldReference.validate) && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find((validateFunction)=>validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = (options)=>options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name\n        };\n    }\n    const names = name.split(\".\");\n    while(names.length){\n        const fieldName = names.join(\".\");\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return {\n                name\n            };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError\n            };\n        }\n        names.pop();\n    }\n    return {\n        name\n    };\n}\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode)=>{\n    if (mode.isOnAll) {\n        return false;\n    } else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\nvar unsetEmptyArray = (ref, name)=>!compact(get(ref, name)).length && unset(ref, name);\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false\n    };\n    let _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n    let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false\n    };\n    let _names = {\n        mount: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set()\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    };\n    const _subjects = {\n        values: createSubject(),\n        array: createSubject(),\n        state: createSubject()\n    };\n    const validationModeBeforeSubmit = getValidationModes(_options.mode);\n    const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback)=>(wait)=>{\n            clearTimeout(timer);\n            timer = setTimeout(callback, wait);\n        };\n    const _updateValid = async (shouldUpdateValid)=>{\n        if (_proxyFormState.isValid || shouldUpdateValid) {\n            const isValid = _options.resolver ? isEmptyObject((await _executeSchema()).errors) : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating)=>{\n        if (_proxyFormState.isValidating || _proxyFormState.validatingFields) {\n            (names || Array.from(_names.mount)).forEach((name)=>{\n                if (name) {\n                    isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields)\n            });\n        }\n    };\n    const _updateFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true)=>{\n        if (args && method) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if (_proxyFormState.touchedFields && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid\n            });\n        } else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error)=>{\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors\n        });\n    };\n    const _setErrors = (errors)=>{\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value1, ref)=>{\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value1) ? get(_defaultValues, name) : value1);\n            isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n            _state.mount && _updateValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender)=>{\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name\n        };\n        const disabledField = !!(get(_fields, name) && get(_fields, name)._f && get(_fields, name)._f.disabled);\n        if (!isBlurEvent || shouldDirty) {\n            if (_proxyFormState.isDirty) {\n                isPreviousDirty = _formState.isDirty;\n                _formState.isDirty = output.isDirty = _getDirty();\n                shouldUpdateField = isPreviousDirty !== output.isDirty;\n            }\n            const isCurrentFieldPristine = disabledField || deepEqual(get(_defaultValues, name), fieldValue);\n            isPreviousDirty = !!(!disabledField && get(_formState.dirtyFields, name));\n            isCurrentFieldPristine || disabledField ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n            output.dirtyFields = _formState.dirtyFields;\n            shouldUpdateField = shouldUpdateField || _proxyFormState.dirtyFields && isPreviousDirty !== !isCurrentFieldPristine;\n        }\n        if (isBlurEvent) {\n            const isPreviousFieldTouched = get(_formState.touchedFields, name);\n            if (!isPreviousFieldTouched) {\n                set(_formState.touchedFields, name, isBlurEvent);\n                output.touchedFields = _formState.touchedFields;\n                shouldUpdateField = shouldUpdateField || _proxyFormState.touchedFields && isPreviousFieldTouched !== isBlurEvent;\n            }\n        }\n        shouldUpdateField && shouldRender && _subjects.state.next(output);\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState)=>{\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = _proxyFormState.isValid && isBoolean(isValid) && _formState.isValid !== isValid;\n        if (props.delayError && error) {\n            delayErrorCallback = debounce(()=>updateErrors(name, error));\n            delayErrorCallback(props.delayError);\n        } else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...shouldUpdateValid && isBoolean(isValid) ? {\n                    isValid\n                } : {},\n                errors: _formState.errors,\n                name\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _executeSchema = async (name)=>{\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names)=>{\n        const { errors } = await _executeSchema(names);\n        if (names) {\n            for (const name of names){\n                const error = get(errors, name);\n                error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n            }\n        } else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true\n    })=>{\n        for(const name in fields){\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ], true);\n                    }\n                    const fieldError = await validateField(field, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) && await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context);\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = ()=>{\n        for (const name of _names.unMount){\n            const field = get(_fields, name);\n            field && (field._f.refs ? field._f.refs.every((ref)=>!live(ref)) : !live(field._f.ref)) && unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data)=>(name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal)=>generateWatchOutput(names, _names, {\n            ..._state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n                [names]: defaultValue\n            } : defaultValue\n        }, isGlobal, defaultValue);\n    const _getFieldArray = (name)=>compact(get(_state.mount ? _formValues : _defaultValues, name, props.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        let fieldValue = value1;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value1, fieldReference));\n                fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value1) ? \"\" : value1;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [\n                        ...fieldReference.ref.options\n                    ].forEach((optionRef)=>optionRef.selected = fieldValue.includes(optionRef.value));\n                } else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.length > 1 ? fieldReference.refs.forEach((checkboxRef)=>(!checkboxRef.defaultChecked || !checkboxRef.disabled) && (checkboxRef.checked = Array.isArray(fieldValue) ? !!fieldValue.find((data)=>data === checkboxRef.value) : fieldValue === checkboxRef.value)) : fieldReference.refs[0] && (fieldReference.refs[0].checked = !!fieldValue);\n                    } else {\n                        fieldReference.refs.forEach((radioRef)=>radioRef.checked = radioRef.value === fieldValue);\n                    }\n                } else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = \"\";\n                } else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.values.next({\n                            name,\n                            values: {\n                                ..._formValues\n                            }\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value1, options)=>{\n        for(const fieldKey in value1){\n            const fieldValue = value1[fieldKey];\n            const fieldName = `${name}.${fieldKey}`;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) || !isPrimitive(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value1);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: {\n                    ..._formValues\n                }\n            });\n            if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields) && options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue)\n                });\n            }\n        } else {\n            field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({\n            ..._formState\n        });\n        _subjects.values.next({\n            name: _state.mount ? name : undefined,\n            values: {\n                ..._formValues\n            }\n        });\n    };\n    const onChange = async (event)=>{\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const getCurrentFieldValue = ()=>target.type ? getFieldValue(field._f) : getEventValue(event);\n        const _updateIsFieldValueUpdated = (fieldValue)=>{\n            isFieldValueUpdated = Number.isNaN(fieldValue) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = getCurrentFieldValue();\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            } else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent, false);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent && _subjects.values.next({\n                name,\n                type: event.type,\n                values: {\n                    ..._formValues\n                }\n            });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid) {\n                    if (props.mode === \"onBlur\") {\n                        if (isBlurEvent) {\n                            _updateValid();\n                        }\n                    } else {\n                        _updateValid();\n                    }\n                }\n                return shouldRender && _subjects.state.next({\n                    name,\n                    ...watched ? {} : fieldState\n                });\n            }\n            !isBlurEvent && watched && _subjects.state.next({\n                ..._formState\n            });\n            if (_options.resolver) {\n                const { errors } = await _executeSchema([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            } else {\n                _updateIsValidating([\n                    name\n                ], true);\n                error = (await validateField(field, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    } else if (_proxyFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps && trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key)=>{\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {})=>{\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name ? !fieldNames.some((name)=>get(errors, name)) : isValid;\n        } else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName)=>{\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? {\n                    [fieldName]: field\n                } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _updateValid();\n        } else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...!isString(name) || _proxyFormState.isValid && isValid !== _formState.isValid ? {} : {\n                name\n            },\n            ..._options.resolver || !name ? {\n                isValid\n            } : {},\n            errors: _formState.errors\n        });\n        options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames)=>{\n        const values = {\n            ..._state.mount ? _formValues : _defaultValues\n        };\n        return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map((name)=>get(values, name));\n    };\n    const getFieldState = (name, formState)=>({\n            invalid: !!get((formState || _formState).errors, name),\n            isDirty: !!get((formState || _formState).dirtyFields, name),\n            error: get((formState || _formState).errors, name),\n            isValidating: !!get(_formState.validatingFields, name),\n            isTouched: !!get((formState || _formState).touchedFields, name)\n        });\n    const clearErrors = (name)=>{\n        name && convertToArrayPayload(name).forEach((inputName)=>unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {}\n        });\n    };\n    const setError = (name, error, options)=>{\n        const ref = (get(_fields, name, {\n            _f: {}\n        })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue)=>isFunction(name) ? _subjects.values.subscribe({\n            next: (payload)=>name(_getWatch(undefined, defaultValue), payload)\n        }) : _getWatch(name, defaultValue, true);\n    const unregister = (name, options = {})=>{\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount){\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n        }\n        _subjects.values.next({\n            values: {\n                ..._formValues\n            }\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...!options.keepDirty ? {} : {\n                isDirty: _getDirty()\n            }\n        });\n        !options.keepIsValid && _updateValid();\n    };\n    const _updateDisabledField = ({ disabled, name, field, fields, value: value1 })=>{\n        if (isBoolean(disabled) && _state.mount || !!disabled) {\n            const inputValue = disabled ? undefined : isUndefined(value1) ? getFieldValue(field ? field._f : get(fields, name)._f) : value1;\n            set(_formValues, name, inputValue);\n            updateTouchAndDirty(name, inputValue, false, false, true);\n        }\n    };\n    const register = (name, options = {})=>{\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(props.disabled);\n        set(_fields, name, {\n            ...field || {},\n            _f: {\n                ...field && field._f ? field._f : {\n                    ref: {\n                        name\n                    }\n                },\n                name,\n                mount: true,\n                ...options\n            }\n        });\n        _names.mount.add(name);\n        if (field) {\n            _updateDisabledField({\n                field,\n                disabled: isBoolean(options.disabled) ? options.disabled : props.disabled,\n                name,\n                value: options.value\n            });\n        } else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...disabledIsDefined ? {\n                disabled: options.disabled || props.disabled\n            } : {},\n            ..._options.progressive ? {\n                required: !!options.required,\n                min: getRuleValue(options.min),\n                max: getRuleValue(options.max),\n                minLength: getRuleValue(options.minLength),\n                maxLength: getRuleValue(options.maxLength),\n                pattern: getRuleValue(options.pattern)\n            } : {},\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref)=>{\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll(\"input,select,textarea\")[0] || ref : ref : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox ? refs.find((option)=>option === fieldRef) : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...radioOrCheckbox ? {\n                                refs: [\n                                    ...refs.filter(live),\n                                    fieldRef,\n                                    ...Array.isArray(get(_defaultValues, name)) ? [\n                                        {}\n                                    ] : []\n                                ],\n                                ref: {\n                                    type: fieldRef.type,\n                                    name\n                                }\n                            } : {\n                                ref: fieldRef\n                            }\n                        }\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                } else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n                }\n            }\n        };\n    };\n    const _focusError = ()=>_options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled)=>{\n        if (isBoolean(disabled)) {\n            _subjects.state.next({\n                disabled\n            });\n            iterateFieldsByAction(_fields, (ref, name)=>{\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef)=>{\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid)=>async (e)=>{\n            let onValidError = undefined;\n            if (e) {\n                e.preventDefault && e.preventDefault();\n                e.persist && e.persist();\n            }\n            let fieldValues = cloneObject(_formValues);\n            _subjects.state.next({\n                isSubmitting: true\n            });\n            if (_options.resolver) {\n                const { errors, values } = await _executeSchema();\n                _formState.errors = errors;\n                fieldValues = values;\n            } else {\n                await executeBuiltInValidation(_fields);\n            }\n            unset(_formState.errors, \"root\");\n            if (isEmptyObject(_formState.errors)) {\n                _subjects.state.next({\n                    errors: {}\n                });\n                try {\n                    await onValid(fieldValues, e);\n                } catch (error) {\n                    onValidError = error;\n                }\n            } else {\n                if (onInvalid) {\n                    await onInvalid({\n                        ..._formState.errors\n                    }, e);\n                }\n                _focusError();\n                setTimeout(_focusError);\n            }\n            _subjects.state.next({\n                isSubmitted: true,\n                isSubmitting: false,\n                isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n                submitCount: _formState.submitCount + 1,\n                errors: _formState.errors\n            });\n            if (onValidError) {\n                throw onValidError;\n            }\n        };\n    const resetField = (name, options = {})=>{\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            } else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _updateValid();\n            }\n            _subjects.state.next({\n                ..._formState\n            });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {})=>{\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                for (const fieldName of _names.mount){\n                    get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n                }\n            } else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount){\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest(\"form\");\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                _fields = {};\n            }\n            _formValues = props.shouldUnregister ? keepStateOptions.keepDefaultValues ? cloneObject(_defaultValues) : {} : cloneObject(values);\n            _subjects.array.next({\n                values: {\n                    ...values\n                }\n            });\n            _subjects.values.next({\n                values: {\n                    ...values\n                }\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: \"\"\n        };\n        _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!props.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n            isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n            dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n            touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n            isSubmitting: false\n        });\n    };\n    const reset = (formValues, keepStateOptions)=>_reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n    const setFocus = (name, options = {})=>{\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect && fieldRef.select();\n            }\n        }\n    };\n    const _updateFormState = (updatedFormState)=>{\n        _formState = {\n            ..._formState,\n            ...updatedFormState\n        };\n    };\n    const _resetDefaultValues = ()=>isFunction(_options.defaultValues) && _options.defaultValues().then((values)=>{\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false\n            });\n        });\n    return {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _executeSchema,\n            _getWatch,\n            _getDirty,\n            _updateValid,\n            _removeUnmounted,\n            _updateFieldArray,\n            _updateDisabledField,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _updateFormState,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            _setErrors,\n            get _fields () {\n                return _fields;\n            },\n            get _formValues () {\n                return _formValues;\n            },\n            get _state () {\n                return _state;\n            },\n            set _state (value){\n                _state = value;\n            },\n            get _defaultValues () {\n                return _defaultValues;\n            },\n            get _names () {\n                return _names;\n            },\n            set _names (value){\n                _names = value;\n            },\n            get _formState () {\n                return _formState;\n            },\n            set _formState (value){\n                _formState = value;\n            },\n            get _options () {\n                return _options;\n            },\n            set _options (value){\n                _options = {\n                    ..._options,\n                    ...value\n                };\n            }\n        },\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState\n    };\n}\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */ function useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n    });\n    if (!_formControl.current) {\n        _formControl.current = {\n            ...createFormControl(props),\n            formState\n        };\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useSubscribe({\n        subject: control._subjects.state,\n        next: (value1)=>{\n            if (shouldRenderFormState(value1, control._proxyFormState, control._updateFormState, true)) {\n                updateFormState({\n                    ...control._formState\n                });\n            }\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._disableForm(props.disabled), [\n        control,\n        props.disabled\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty\n                });\n            }\n        }\n    }, [\n        control,\n        formState.isDirty\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, control._options.resetOptions);\n            _values.current = props.values;\n            updateFormState((state)=>({\n                    ...state\n                }));\n        } else {\n            control._resetDefaultValues();\n        }\n    }, [\n        props.values,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.errors) {\n            control._setErrors(props.errors);\n        }\n    }, [\n        props.errors,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!control._state.mount) {\n            control._updateValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({\n                ...control._formState\n            });\n        }\n        control._removeUnmounted();\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        props.shouldUnregister && control._subjects.values.next({\n            values: control._getWatch()\n        });\n    }, [\n        props.shouldUnregister,\n        control\n    ]);\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n //# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;