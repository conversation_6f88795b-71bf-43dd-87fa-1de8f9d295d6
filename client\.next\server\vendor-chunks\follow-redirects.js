"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/follow-redirects";
exports.ids = ["vendor-chunks/follow-redirects"];
exports.modules = {

/***/ "(ssr)/./node_modules/follow-redirects/debug.js":
/*!************************************************!*\
  !*** ./node_modules/follow-redirects/debug.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar debug;\nmodule.exports = function() {\n    if (!debug) {\n        try {\n            /* eslint global-require: off */ debug = __webpack_require__(/*! debug */ \"(ssr)/./node_modules/debug/src/index.js\")(\"follow-redirects\");\n        } catch (error) {}\n        if (typeof debug !== \"function\") {\n            debug = function() {};\n        }\n    }\n    debug.apply(null, arguments);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZm9sbG93LXJlZGlyZWN0cy9kZWJ1Zy5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUE7QUFFSkMsT0FBT0MsT0FBTyxHQUFHO0lBQ2YsSUFBSSxDQUFDRixPQUFPO1FBQ1YsSUFBSTtZQUNGLDhCQUE4QixHQUM5QkEsUUFBUUcsbUJBQU9BLENBQUMsc0RBQU8sRUFBRTtRQUMzQixFQUNBLE9BQU9DLE9BQU8sQ0FBUTtRQUN0QixJQUFJLE9BQU9KLFVBQVUsWUFBWTtZQUMvQkEsUUFBUSxZQUFvQjtRQUM5QjtJQUNGO0lBQ0FBLE1BQU1LLEtBQUssQ0FBQyxNQUFNQztBQUNwQiIsInNvdXJjZXMiOlsid2VicGFjazovL2RydXRvby8uL25vZGVfbW9kdWxlcy9mb2xsb3ctcmVkaXJlY3RzL2RlYnVnLmpzPzI1ODEiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGRlYnVnO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uICgpIHtcbiAgaWYgKCFkZWJ1Zykge1xuICAgIHRyeSB7XG4gICAgICAvKiBlc2xpbnQgZ2xvYmFsLXJlcXVpcmU6IG9mZiAqL1xuICAgICAgZGVidWcgPSByZXF1aXJlKFwiZGVidWdcIikoXCJmb2xsb3ctcmVkaXJlY3RzXCIpO1xuICAgIH1cbiAgICBjYXRjaCAoZXJyb3IpIHsgLyogKi8gfVxuICAgIGlmICh0eXBlb2YgZGVidWcgIT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgZGVidWcgPSBmdW5jdGlvbiAoKSB7IC8qICovIH07XG4gICAgfVxuICB9XG4gIGRlYnVnLmFwcGx5KG51bGwsIGFyZ3VtZW50cyk7XG59O1xuIl0sIm5hbWVzIjpbImRlYnVnIiwibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiLCJlcnJvciIsImFwcGx5IiwiYXJndW1lbnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/follow-redirects/debug.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/follow-redirects/index.js":
/*!************************************************!*\
  !*** ./node_modules/follow-redirects/index.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar url = __webpack_require__(/*! url */ \"url\");\nvar URL = url.URL;\nvar http = __webpack_require__(/*! http */ \"http\");\nvar https = __webpack_require__(/*! https */ \"https\");\nvar Writable = (__webpack_require__(/*! stream */ \"stream\").Writable);\nvar assert = __webpack_require__(/*! assert */ \"assert\");\nvar debug = __webpack_require__(/*! ./debug */ \"(ssr)/./node_modules/follow-redirects/debug.js\");\n// Whether to use the native URL object or the legacy url module\nvar useNativeURL = false;\ntry {\n    assert(new URL());\n} catch (error) {\n    useNativeURL = error.code === \"ERR_INVALID_URL\";\n}\n// URL fields to preserve in copy operations\nvar preservedUrlFields = [\n    \"auth\",\n    \"host\",\n    \"hostname\",\n    \"href\",\n    \"path\",\n    \"pathname\",\n    \"port\",\n    \"protocol\",\n    \"query\",\n    \"search\",\n    \"hash\"\n];\n// Create handlers that pass events from native requests\nvar events = [\n    \"abort\",\n    \"aborted\",\n    \"connect\",\n    \"error\",\n    \"socket\",\n    \"timeout\"\n];\nvar eventHandlers = Object.create(null);\nevents.forEach(function(event) {\n    eventHandlers[event] = function(arg1, arg2, arg3) {\n        this._redirectable.emit(event, arg1, arg2, arg3);\n    };\n});\n// Error types with codes\nvar InvalidUrlError = createErrorType(\"ERR_INVALID_URL\", \"Invalid URL\", TypeError);\nvar RedirectionError = createErrorType(\"ERR_FR_REDIRECTION_FAILURE\", \"Redirected request failed\");\nvar TooManyRedirectsError = createErrorType(\"ERR_FR_TOO_MANY_REDIRECTS\", \"Maximum number of redirects exceeded\", RedirectionError);\nvar MaxBodyLengthExceededError = createErrorType(\"ERR_FR_MAX_BODY_LENGTH_EXCEEDED\", \"Request body larger than maxBodyLength limit\");\nvar WriteAfterEndError = createErrorType(\"ERR_STREAM_WRITE_AFTER_END\", \"write after end\");\n// istanbul ignore next\nvar destroy = Writable.prototype.destroy || noop;\n// An HTTP(S) request that can be redirected\nfunction RedirectableRequest(options, responseCallback) {\n    // Initialize the request\n    Writable.call(this);\n    this._sanitizeOptions(options);\n    this._options = options;\n    this._ended = false;\n    this._ending = false;\n    this._redirectCount = 0;\n    this._redirects = [];\n    this._requestBodyLength = 0;\n    this._requestBodyBuffers = [];\n    // Attach a callback if passed\n    if (responseCallback) {\n        this.on(\"response\", responseCallback);\n    }\n    // React to responses of native requests\n    var self = this;\n    this._onNativeResponse = function(response) {\n        try {\n            self._processResponse(response);\n        } catch (cause) {\n            self.emit(\"error\", cause instanceof RedirectionError ? cause : new RedirectionError({\n                cause: cause\n            }));\n        }\n    };\n    // Perform the first request\n    this._performRequest();\n}\nRedirectableRequest.prototype = Object.create(Writable.prototype);\nRedirectableRequest.prototype.abort = function() {\n    destroyRequest(this._currentRequest);\n    this._currentRequest.abort();\n    this.emit(\"abort\");\n};\nRedirectableRequest.prototype.destroy = function(error) {\n    destroyRequest(this._currentRequest, error);\n    destroy.call(this, error);\n    return this;\n};\n// Writes buffered data to the current native request\nRedirectableRequest.prototype.write = function(data, encoding, callback) {\n    // Writing is not allowed if end has been called\n    if (this._ending) {\n        throw new WriteAfterEndError();\n    }\n    // Validate input and shift parameters if necessary\n    if (!isString(data) && !isBuffer(data)) {\n        throw new TypeError(\"data should be a string, Buffer or Uint8Array\");\n    }\n    if (isFunction(encoding)) {\n        callback = encoding;\n        encoding = null;\n    }\n    // Ignore empty buffers, since writing them doesn't invoke the callback\n    // https://github.com/nodejs/node/issues/22066\n    if (data.length === 0) {\n        if (callback) {\n            callback();\n        }\n        return;\n    }\n    // Only write when we don't exceed the maximum body length\n    if (this._requestBodyLength + data.length <= this._options.maxBodyLength) {\n        this._requestBodyLength += data.length;\n        this._requestBodyBuffers.push({\n            data: data,\n            encoding: encoding\n        });\n        this._currentRequest.write(data, encoding, callback);\n    } else {\n        this.emit(\"error\", new MaxBodyLengthExceededError());\n        this.abort();\n    }\n};\n// Ends the current native request\nRedirectableRequest.prototype.end = function(data, encoding, callback) {\n    // Shift parameters if necessary\n    if (isFunction(data)) {\n        callback = data;\n        data = encoding = null;\n    } else if (isFunction(encoding)) {\n        callback = encoding;\n        encoding = null;\n    }\n    // Write data if needed and end\n    if (!data) {\n        this._ended = this._ending = true;\n        this._currentRequest.end(null, null, callback);\n    } else {\n        var self = this;\n        var currentRequest = this._currentRequest;\n        this.write(data, encoding, function() {\n            self._ended = true;\n            currentRequest.end(null, null, callback);\n        });\n        this._ending = true;\n    }\n};\n// Sets a header value on the current native request\nRedirectableRequest.prototype.setHeader = function(name, value) {\n    this._options.headers[name] = value;\n    this._currentRequest.setHeader(name, value);\n};\n// Clears a header value on the current native request\nRedirectableRequest.prototype.removeHeader = function(name) {\n    delete this._options.headers[name];\n    this._currentRequest.removeHeader(name);\n};\n// Global timeout for all underlying requests\nRedirectableRequest.prototype.setTimeout = function(msecs, callback) {\n    var self = this;\n    // Destroys the socket on timeout\n    function destroyOnTimeout(socket) {\n        socket.setTimeout(msecs);\n        socket.removeListener(\"timeout\", socket.destroy);\n        socket.addListener(\"timeout\", socket.destroy);\n    }\n    // Sets up a timer to trigger a timeout event\n    function startTimer(socket) {\n        if (self._timeout) {\n            clearTimeout(self._timeout);\n        }\n        self._timeout = setTimeout(function() {\n            self.emit(\"timeout\");\n            clearTimer();\n        }, msecs);\n        destroyOnTimeout(socket);\n    }\n    // Stops a timeout from triggering\n    function clearTimer() {\n        // Clear the timeout\n        if (self._timeout) {\n            clearTimeout(self._timeout);\n            self._timeout = null;\n        }\n        // Clean up all attached listeners\n        self.removeListener(\"abort\", clearTimer);\n        self.removeListener(\"error\", clearTimer);\n        self.removeListener(\"response\", clearTimer);\n        self.removeListener(\"close\", clearTimer);\n        if (callback) {\n            self.removeListener(\"timeout\", callback);\n        }\n        if (!self.socket) {\n            self._currentRequest.removeListener(\"socket\", startTimer);\n        }\n    }\n    // Attach callback if passed\n    if (callback) {\n        this.on(\"timeout\", callback);\n    }\n    // Start the timer if or when the socket is opened\n    if (this.socket) {\n        startTimer(this.socket);\n    } else {\n        this._currentRequest.once(\"socket\", startTimer);\n    }\n    // Clean up on events\n    this.on(\"socket\", destroyOnTimeout);\n    this.on(\"abort\", clearTimer);\n    this.on(\"error\", clearTimer);\n    this.on(\"response\", clearTimer);\n    this.on(\"close\", clearTimer);\n    return this;\n};\n// Proxy all other public ClientRequest methods\n[\n    \"flushHeaders\",\n    \"getHeader\",\n    \"setNoDelay\",\n    \"setSocketKeepAlive\"\n].forEach(function(method) {\n    RedirectableRequest.prototype[method] = function(a, b) {\n        return this._currentRequest[method](a, b);\n    };\n});\n// Proxy all public ClientRequest properties\n[\n    \"aborted\",\n    \"connection\",\n    \"socket\"\n].forEach(function(property) {\n    Object.defineProperty(RedirectableRequest.prototype, property, {\n        get: function() {\n            return this._currentRequest[property];\n        }\n    });\n});\nRedirectableRequest.prototype._sanitizeOptions = function(options) {\n    // Ensure headers are always present\n    if (!options.headers) {\n        options.headers = {};\n    }\n    // Since http.request treats host as an alias of hostname,\n    // but the url module interprets host as hostname plus port,\n    // eliminate the host property to avoid confusion.\n    if (options.host) {\n        // Use hostname if set, because it has precedence\n        if (!options.hostname) {\n            options.hostname = options.host;\n        }\n        delete options.host;\n    }\n    // Complete the URL object when necessary\n    if (!options.pathname && options.path) {\n        var searchPos = options.path.indexOf(\"?\");\n        if (searchPos < 0) {\n            options.pathname = options.path;\n        } else {\n            options.pathname = options.path.substring(0, searchPos);\n            options.search = options.path.substring(searchPos);\n        }\n    }\n};\n// Executes the next native request (initial or redirect)\nRedirectableRequest.prototype._performRequest = function() {\n    // Load the native protocol\n    var protocol = this._options.protocol;\n    var nativeProtocol = this._options.nativeProtocols[protocol];\n    if (!nativeProtocol) {\n        throw new TypeError(\"Unsupported protocol \" + protocol);\n    }\n    // If specified, use the agent corresponding to the protocol\n    // (HTTP and HTTPS use different types of agents)\n    if (this._options.agents) {\n        var scheme = protocol.slice(0, -1);\n        this._options.agent = this._options.agents[scheme];\n    }\n    // Create the native request and set up its event handlers\n    var request = this._currentRequest = nativeProtocol.request(this._options, this._onNativeResponse);\n    request._redirectable = this;\n    for (var event of events){\n        request.on(event, eventHandlers[event]);\n    }\n    // RFC7230§5.3.1: When making a request directly to an origin server, […]\n    // a client MUST send only the absolute path […] as the request-target.\n    this._currentUrl = /^\\//.test(this._options.path) ? url.format(this._options) : // When making a request to a proxy, […]\n    // a client MUST send the target URI in absolute-form […].\n    this._options.path;\n    // End a redirected request\n    // (The first request must be ended explicitly with RedirectableRequest#end)\n    if (this._isRedirect) {\n        // Write the request entity and end\n        var i = 0;\n        var self = this;\n        var buffers = this._requestBodyBuffers;\n        (function writeNext(error) {\n            // Only write if this request has not been redirected yet\n            /* istanbul ignore else */ if (request === self._currentRequest) {\n                // Report any write errors\n                /* istanbul ignore if */ if (error) {\n                    self.emit(\"error\", error);\n                } else if (i < buffers.length) {\n                    var buffer = buffers[i++];\n                    /* istanbul ignore else */ if (!request.finished) {\n                        request.write(buffer.data, buffer.encoding, writeNext);\n                    }\n                } else if (self._ended) {\n                    request.end();\n                }\n            }\n        })();\n    }\n};\n// Processes a response from the current native request\nRedirectableRequest.prototype._processResponse = function(response) {\n    // Store the redirected response\n    var statusCode = response.statusCode;\n    if (this._options.trackRedirects) {\n        this._redirects.push({\n            url: this._currentUrl,\n            headers: response.headers,\n            statusCode: statusCode\n        });\n    }\n    // RFC7231§6.4: The 3xx (Redirection) class of status code indicates\n    // that further action needs to be taken by the user agent in order to\n    // fulfill the request. If a Location header field is provided,\n    // the user agent MAY automatically redirect its request to the URI\n    // referenced by the Location field value,\n    // even if the specific status code is not understood.\n    // If the response is not a redirect; return it as-is\n    var location = response.headers.location;\n    if (!location || this._options.followRedirects === false || statusCode < 300 || statusCode >= 400) {\n        response.responseUrl = this._currentUrl;\n        response.redirects = this._redirects;\n        this.emit(\"response\", response);\n        // Clean up\n        this._requestBodyBuffers = [];\n        return;\n    }\n    // The response is a redirect, so abort the current request\n    destroyRequest(this._currentRequest);\n    // Discard the remainder of the response to avoid waiting for data\n    response.destroy();\n    // RFC7231§6.4: A client SHOULD detect and intervene\n    // in cyclical redirections (i.e., \"infinite\" redirection loops).\n    if (++this._redirectCount > this._options.maxRedirects) {\n        throw new TooManyRedirectsError();\n    }\n    // Store the request headers if applicable\n    var requestHeaders;\n    var beforeRedirect = this._options.beforeRedirect;\n    if (beforeRedirect) {\n        requestHeaders = Object.assign({\n            // The Host header was set by nativeProtocol.request\n            Host: response.req.getHeader(\"host\")\n        }, this._options.headers);\n    }\n    // RFC7231§6.4: Automatic redirection needs to done with\n    // care for methods not known to be safe, […]\n    // RFC7231§6.4.2–3: For historical reasons, a user agent MAY change\n    // the request method from POST to GET for the subsequent request.\n    var method = this._options.method;\n    if ((statusCode === 301 || statusCode === 302) && this._options.method === \"POST\" || // RFC7231§6.4.4: The 303 (See Other) status code indicates that\n    // the server is redirecting the user agent to a different resource […]\n    // A user agent can perform a retrieval request targeting that URI\n    // (a GET or HEAD request if using HTTP) […]\n    statusCode === 303 && !/^(?:GET|HEAD)$/.test(this._options.method)) {\n        this._options.method = \"GET\";\n        // Drop a possible entity and headers related to it\n        this._requestBodyBuffers = [];\n        removeMatchingHeaders(/^content-/i, this._options.headers);\n    }\n    // Drop the Host header, as the redirect might lead to a different host\n    var currentHostHeader = removeMatchingHeaders(/^host$/i, this._options.headers);\n    // If the redirect is relative, carry over the host of the last request\n    var currentUrlParts = parseUrl(this._currentUrl);\n    var currentHost = currentHostHeader || currentUrlParts.host;\n    var currentUrl = /^\\w+:/.test(location) ? this._currentUrl : url.format(Object.assign(currentUrlParts, {\n        host: currentHost\n    }));\n    // Create the redirected request\n    var redirectUrl = resolveUrl(location, currentUrl);\n    debug(\"redirecting to\", redirectUrl.href);\n    this._isRedirect = true;\n    spreadUrlObject(redirectUrl, this._options);\n    // Drop confidential headers when redirecting to a less secure protocol\n    // or to a different domain that is not a superdomain\n    if (redirectUrl.protocol !== currentUrlParts.protocol && redirectUrl.protocol !== \"https:\" || redirectUrl.host !== currentHost && !isSubdomain(redirectUrl.host, currentHost)) {\n        removeMatchingHeaders(/^(?:authorization|cookie)$/i, this._options.headers);\n    }\n    // Evaluate the beforeRedirect callback\n    if (isFunction(beforeRedirect)) {\n        var responseDetails = {\n            headers: response.headers,\n            statusCode: statusCode\n        };\n        var requestDetails = {\n            url: currentUrl,\n            method: method,\n            headers: requestHeaders\n        };\n        beforeRedirect(this._options, responseDetails, requestDetails);\n        this._sanitizeOptions(this._options);\n    }\n    // Perform the redirected request\n    this._performRequest();\n};\n// Wraps the key/value object of protocols with redirect functionality\nfunction wrap(protocols) {\n    // Default settings\n    var exports = {\n        maxRedirects: 21,\n        maxBodyLength: 10 * 1024 * 1024\n    };\n    // Wrap each protocol\n    var nativeProtocols = {};\n    Object.keys(protocols).forEach(function(scheme) {\n        var protocol = scheme + \":\";\n        var nativeProtocol = nativeProtocols[protocol] = protocols[scheme];\n        var wrappedProtocol = exports[scheme] = Object.create(nativeProtocol);\n        // Executes a request, following redirects\n        function request(input, options, callback) {\n            // Parse parameters, ensuring that input is an object\n            if (isURL(input)) {\n                input = spreadUrlObject(input);\n            } else if (isString(input)) {\n                input = spreadUrlObject(parseUrl(input));\n            } else {\n                callback = options;\n                options = validateUrl(input);\n                input = {\n                    protocol: protocol\n                };\n            }\n            if (isFunction(options)) {\n                callback = options;\n                options = null;\n            }\n            // Set defaults\n            options = Object.assign({\n                maxRedirects: exports.maxRedirects,\n                maxBodyLength: exports.maxBodyLength\n            }, input, options);\n            options.nativeProtocols = nativeProtocols;\n            if (!isString(options.host) && !isString(options.hostname)) {\n                options.hostname = \"::1\";\n            }\n            assert.equal(options.protocol, protocol, \"protocol mismatch\");\n            debug(\"options\", options);\n            return new RedirectableRequest(options, callback);\n        }\n        // Executes a GET request, following redirects\n        function get(input, options, callback) {\n            var wrappedRequest = wrappedProtocol.request(input, options, callback);\n            wrappedRequest.end();\n            return wrappedRequest;\n        }\n        // Expose the properties on the wrapped protocol\n        Object.defineProperties(wrappedProtocol, {\n            request: {\n                value: request,\n                configurable: true,\n                enumerable: true,\n                writable: true\n            },\n            get: {\n                value: get,\n                configurable: true,\n                enumerable: true,\n                writable: true\n            }\n        });\n    });\n    return exports;\n}\nfunction noop() {}\nfunction parseUrl(input) {\n    var parsed;\n    /* istanbul ignore else */ if (useNativeURL) {\n        parsed = new URL(input);\n    } else {\n        // Ensure the URL is valid and absolute\n        parsed = validateUrl(url.parse(input));\n        if (!isString(parsed.protocol)) {\n            throw new InvalidUrlError({\n                input\n            });\n        }\n    }\n    return parsed;\n}\nfunction resolveUrl(relative, base) {\n    /* istanbul ignore next */ return useNativeURL ? new URL(relative, base) : parseUrl(url.resolve(base, relative));\n}\nfunction validateUrl(input) {\n    if (/^\\[/.test(input.hostname) && !/^\\[[:0-9a-f]+\\]$/i.test(input.hostname)) {\n        throw new InvalidUrlError({\n            input: input.href || input\n        });\n    }\n    if (/^\\[/.test(input.host) && !/^\\[[:0-9a-f]+\\](:\\d+)?$/i.test(input.host)) {\n        throw new InvalidUrlError({\n            input: input.href || input\n        });\n    }\n    return input;\n}\nfunction spreadUrlObject(urlObject, target) {\n    var spread = target || {};\n    for (var key of preservedUrlFields){\n        spread[key] = urlObject[key];\n    }\n    // Fix IPv6 hostname\n    if (spread.hostname.startsWith(\"[\")) {\n        spread.hostname = spread.hostname.slice(1, -1);\n    }\n    // Ensure port is a number\n    if (spread.port !== \"\") {\n        spread.port = Number(spread.port);\n    }\n    // Concatenate path\n    spread.path = spread.search ? spread.pathname + spread.search : spread.pathname;\n    return spread;\n}\nfunction removeMatchingHeaders(regex, headers) {\n    var lastValue;\n    for(var header in headers){\n        if (regex.test(header)) {\n            lastValue = headers[header];\n            delete headers[header];\n        }\n    }\n    return lastValue === null || typeof lastValue === \"undefined\" ? undefined : String(lastValue).trim();\n}\nfunction createErrorType(code, message, baseClass) {\n    // Create constructor\n    function CustomError(properties) {\n        Error.captureStackTrace(this, this.constructor);\n        Object.assign(this, properties || {});\n        this.code = code;\n        this.message = this.cause ? message + \": \" + this.cause.message : message;\n    }\n    // Attach constructor and set default properties\n    CustomError.prototype = new (baseClass || Error)();\n    Object.defineProperties(CustomError.prototype, {\n        constructor: {\n            value: CustomError,\n            enumerable: false\n        },\n        name: {\n            value: \"Error [\" + code + \"]\",\n            enumerable: false\n        }\n    });\n    return CustomError;\n}\nfunction destroyRequest(request, error) {\n    for (var event of events){\n        request.removeListener(event, eventHandlers[event]);\n    }\n    request.on(\"error\", noop);\n    request.destroy(error);\n}\nfunction isSubdomain(subdomain, domain) {\n    assert(isString(subdomain) && isString(domain));\n    var dot = subdomain.length - domain.length - 1;\n    return dot > 0 && subdomain[dot] === \".\" && subdomain.endsWith(domain);\n}\nfunction isString(value) {\n    return typeof value === \"string\" || value instanceof String;\n}\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\nfunction isBuffer(value) {\n    return typeof value === \"object\" && \"length\" in value;\n}\nfunction isURL(value) {\n    return URL && value instanceof URL;\n}\n// Exports\nmodule.exports = wrap({\n    http: http,\n    https: https\n});\nmodule.exports.wrap = wrap;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/follow-redirects/index.js\n");

/***/ })

};
;