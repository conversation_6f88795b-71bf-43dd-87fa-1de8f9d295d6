{"name": "dru<PERSON>o", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^5.3.1", "@ant-design/nextjs-registry": "^1.0.0", "@hookform/resolvers": "^3.9.0", "@reduxjs/toolkit": "^2.2.1", "antd": "^5.14.2", "antd-img-crop": "^4.23.0", "axios": "^1.6.7", "jwt-decode": "^4.0.0", "next": "14.1.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.53.0", "react-redux": "^9.1.0", "recharts": "^2.12.7", "yup": "^1.4.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}