"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-overflow";
exports.ids = ["vendor-chunks/rc-overflow"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-overflow/es/Item.js":
/*!*********************************************!*\
  !*** ./node_modules/rc-overflow/es/Item.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n\n\n\nvar _excluded = [\n    \"prefixCls\",\n    \"invalidate\",\n    \"item\",\n    \"renderItem\",\n    \"responsive\",\n    \"responsiveDisabled\",\n    \"registerSize\",\n    \"itemKey\",\n    \"className\",\n    \"style\",\n    \"children\",\n    \"display\",\n    \"order\",\n    \"component\"\n];\n\n\n\n// Use shared variable to save bundle size\nvar UNDEFINED = undefined;\nfunction InternalItem(props, ref) {\n    var prefixCls = props.prefixCls, invalidate = props.invalidate, item = props.item, renderItem = props.renderItem, responsive = props.responsive, responsiveDisabled = props.responsiveDisabled, registerSize = props.registerSize, itemKey = props.itemKey, className = props.className, style = props.style, children = props.children, display = props.display, order = props.order, _props$component = props.component, Component = _props$component === void 0 ? \"div\" : _props$component, restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props, _excluded);\n    var mergedHidden = responsive && !display;\n    // ================================ Effect ================================\n    function internalRegisterSize(width) {\n        registerSize(itemKey, width);\n    }\n    react__WEBPACK_IMPORTED_MODULE_3__.useEffect(function() {\n        return function() {\n            internalRegisterSize(null);\n        };\n    }, []);\n    // ================================ Render ================================\n    var childNode = renderItem && item !== UNDEFINED ? renderItem(item) : children;\n    var overflowStyle;\n    if (!invalidate) {\n        overflowStyle = {\n            opacity: mergedHidden ? 0 : 1,\n            height: mergedHidden ? 0 : UNDEFINED,\n            overflowY: mergedHidden ? \"hidden\" : UNDEFINED,\n            order: responsive ? order : UNDEFINED,\n            pointerEvents: mergedHidden ? \"none\" : UNDEFINED,\n            position: mergedHidden ? \"absolute\" : UNDEFINED\n        };\n    }\n    var overflowProps = {};\n    if (mergedHidden) {\n        overflowProps[\"aria-hidden\"] = true;\n    }\n    var itemNode = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(!invalidate && prefixCls, className),\n        style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, overflowStyle), style)\n    }, overflowProps, restProps, {\n        ref: ref\n    }), childNode);\n    if (responsive) {\n        itemNode = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            onResize: function onResize(_ref) {\n                var offsetWidth = _ref.offsetWidth;\n                internalRegisterSize(offsetWidth);\n            },\n            disabled: responsiveDisabled\n        }, itemNode);\n    }\n    return itemNode;\n}\nvar Item = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3__.forwardRef(InternalItem);\nItem.displayName = \"Item\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Item);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/Item.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/Overflow.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-overflow/es/Overflow.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverflowContext: () => (/* reexport safe */ _context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var _Item__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./Item */ \"(ssr)/./node_modules/rc-overflow/es/Item.js\");\n/* harmony import */ var _hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useEffectState */ \"(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js\");\n/* harmony import */ var _RawItem__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./RawItem */ \"(ssr)/./node_modules/rc-overflow/es/RawItem.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-overflow/es/context.js\");\n\n\n\n\nvar _excluded = [\n    \"prefixCls\",\n    \"data\",\n    \"renderItem\",\n    \"renderRawItem\",\n    \"itemKey\",\n    \"itemWidth\",\n    \"ssr\",\n    \"style\",\n    \"className\",\n    \"maxCount\",\n    \"renderRest\",\n    \"renderRawRest\",\n    \"suffix\",\n    \"component\",\n    \"itemComponent\",\n    \"onVisibleChange\"\n];\n\n\n\n\n\n\n\n\n\nvar RESPONSIVE = \"responsive\";\nvar INVALIDATE = \"invalidate\";\n\nfunction defaultRenderRest(omittedItems) {\n    return \"+ \".concat(omittedItems.length, \" ...\");\n}\nfunction Overflow(props, ref) {\n    var _props$prefixCls = props.prefixCls, prefixCls = _props$prefixCls === void 0 ? \"rc-overflow\" : _props$prefixCls, _props$data = props.data, data = _props$data === void 0 ? [] : _props$data, renderItem = props.renderItem, renderRawItem = props.renderRawItem, itemKey = props.itemKey, _props$itemWidth = props.itemWidth, itemWidth = _props$itemWidth === void 0 ? 10 : _props$itemWidth, ssr = props.ssr, style = props.style, className = props.className, maxCount = props.maxCount, renderRest = props.renderRest, renderRawRest = props.renderRawRest, suffix = props.suffix, _props$component = props.component, Component = _props$component === void 0 ? \"div\" : _props$component, itemComponent = props.itemComponent, onVisibleChange = props.onVisibleChange, restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(props, _excluded);\n    var fullySSR = ssr === \"full\";\n    var notifyEffectUpdate = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__.useBatcher)();\n    var _useEffectState = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, null), _useEffectState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState, 2), containerWidth = _useEffectState2[0], setContainerWidth = _useEffectState2[1];\n    var mergedContainerWidth = containerWidth || 0;\n    var _useEffectState3 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, new Map()), _useEffectState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState3, 2), itemWidths = _useEffectState4[0], setItemWidths = _useEffectState4[1];\n    var _useEffectState5 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0), _useEffectState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState5, 2), prevRestWidth = _useEffectState6[0], setPrevRestWidth = _useEffectState6[1];\n    var _useEffectState7 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0), _useEffectState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState7, 2), restWidth = _useEffectState8[0], setRestWidth = _useEffectState8[1];\n    var _useEffectState9 = (0,_hooks_useEffectState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(notifyEffectUpdate, 0), _useEffectState10 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useEffectState9, 2), suffixWidth = _useEffectState10[0], setSuffixWidth = _useEffectState10[1];\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null), _useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState, 2), suffixFixedStart = _useState2[0], setSuffixFixedStart = _useState2[1];\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null), _useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState3, 2), displayCount = _useState4[0], setDisplayCount = _useState4[1];\n    var mergedDisplayCount = react__WEBPACK_IMPORTED_MODULE_4__.useMemo(function() {\n        if (displayCount === null && fullySSR) {\n            return Number.MAX_SAFE_INTEGER;\n        }\n        return displayCount || 0;\n    }, [\n        displayCount,\n        containerWidth\n    ]);\n    var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false), _useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_useState5, 2), restReady = _useState6[0], setRestReady = _useState6[1];\n    var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n    // Always use the max width to avoid blink\n    var mergedRestWidth = Math.max(prevRestWidth, restWidth);\n    // ================================= Data =================================\n    var isResponsive = maxCount === RESPONSIVE;\n    var shouldResponsive = data.length && isResponsive;\n    var invalidate = maxCount === INVALIDATE;\n    /**\n   * When is `responsive`, we will always render rest node to get the real width of it for calculation\n   */ var showRest = shouldResponsive || typeof maxCount === \"number\" && data.length > maxCount;\n    var mergedData = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function() {\n        var items = data;\n        if (shouldResponsive) {\n            if (containerWidth === null && fullySSR) {\n                items = data;\n            } else {\n                items = data.slice(0, Math.min(data.length, mergedContainerWidth / itemWidth));\n            }\n        } else if (typeof maxCount === \"number\") {\n            items = data.slice(0, maxCount);\n        }\n        return items;\n    }, [\n        data,\n        itemWidth,\n        containerWidth,\n        maxCount,\n        shouldResponsive\n    ]);\n    var omittedItems = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function() {\n        if (shouldResponsive) {\n            return data.slice(mergedDisplayCount + 1);\n        }\n        return data.slice(mergedData.length);\n    }, [\n        data,\n        mergedData,\n        shouldResponsive,\n        mergedDisplayCount\n    ]);\n    // ================================= Item =================================\n    var getKey = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function(item, index) {\n        var _ref;\n        if (typeof itemKey === \"function\") {\n            return itemKey(item);\n        }\n        return (_ref = itemKey && (item === null || item === void 0 ? void 0 : item[itemKey])) !== null && _ref !== void 0 ? _ref : index;\n    }, [\n        itemKey\n    ]);\n    var mergedRenderItem = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(renderItem || function(item) {\n        return item;\n    }, [\n        renderItem\n    ]);\n    function updateDisplayCount(count, suffixFixedStartVal, notReady) {\n        // React 18 will sync render even when the value is same in some case.\n        // We take `mergedData` as deps which may cause dead loop if it's dynamic generate.\n        // ref: https://github.com/ant-design/ant-design/issues/36559\n        if (displayCount === count && (suffixFixedStartVal === undefined || suffixFixedStartVal === suffixFixedStart)) {\n            return;\n        }\n        setDisplayCount(count);\n        if (!notReady) {\n            setRestReady(count < data.length - 1);\n            onVisibleChange === null || onVisibleChange === void 0 ? void 0 : onVisibleChange(count);\n        }\n        if (suffixFixedStartVal !== undefined) {\n            setSuffixFixedStart(suffixFixedStartVal);\n        }\n    }\n    // ================================= Size =================================\n    function onOverflowResize(_, element) {\n        setContainerWidth(element.clientWidth);\n    }\n    function registerSize(key, width) {\n        setItemWidths(function(origin) {\n            var clone = new Map(origin);\n            if (width === null) {\n                clone.delete(key);\n            } else {\n                clone.set(key, width);\n            }\n            return clone;\n        });\n    }\n    function registerOverflowSize(_, width) {\n        setRestWidth(width);\n        setPrevRestWidth(restWidth);\n    }\n    function registerSuffixSize(_, width) {\n        setSuffixWidth(width);\n    }\n    // ================================ Effect ================================\n    function getItemWidth(index) {\n        return itemWidths.get(getKey(mergedData[index], index));\n    }\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        if (mergedContainerWidth && typeof mergedRestWidth === \"number\" && mergedData) {\n            var totalWidth = suffixWidth;\n            var len = mergedData.length;\n            var lastIndex = len - 1;\n            // When data count change to 0, reset this since not loop will reach\n            if (!len) {\n                updateDisplayCount(0, null);\n                return;\n            }\n            for(var i = 0; i < len; i += 1){\n                var currentItemWidth = getItemWidth(i);\n                // Fully will always render\n                if (fullySSR) {\n                    currentItemWidth = currentItemWidth || 0;\n                }\n                // Break since data not ready\n                if (currentItemWidth === undefined) {\n                    updateDisplayCount(i - 1, undefined, true);\n                    break;\n                }\n                // Find best match\n                totalWidth += currentItemWidth;\n                if (// Only one means `totalWidth` is the final width\n                lastIndex === 0 && totalWidth <= mergedContainerWidth || // Last two width will be the final width\n                i === lastIndex - 1 && totalWidth + getItemWidth(lastIndex) <= mergedContainerWidth) {\n                    // Additional check if match the end\n                    updateDisplayCount(lastIndex, null);\n                    break;\n                } else if (totalWidth + mergedRestWidth > mergedContainerWidth) {\n                    // Can not hold all the content to show rest\n                    updateDisplayCount(i - 1, totalWidth - currentItemWidth - suffixWidth + restWidth);\n                    break;\n                }\n            }\n            if (suffix && getItemWidth(0) + suffixWidth > mergedContainerWidth) {\n                setSuffixFixedStart(null);\n            }\n        }\n    }, [\n        mergedContainerWidth,\n        itemWidths,\n        restWidth,\n        suffixWidth,\n        getKey,\n        mergedData\n    ]);\n    // ================================ Render ================================\n    var displayRest = restReady && !!omittedItems.length;\n    var suffixStyle = {};\n    if (suffixFixedStart !== null && shouldResponsive) {\n        suffixStyle = {\n            position: \"absolute\",\n            left: suffixFixedStart,\n            top: 0\n        };\n    }\n    var itemSharedProps = {\n        prefixCls: itemPrefixCls,\n        responsive: shouldResponsive,\n        component: itemComponent,\n        invalidate: invalidate\n    };\n    // >>>>> Choice render fun by `renderRawItem`\n    var internalRenderItemNode = renderRawItem ? function(item, index) {\n        var key = getKey(item, index);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.createElement(_context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext.Provider, {\n            key: key,\n            value: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, itemSharedProps), {}, {\n                order: index,\n                item: item,\n                itemKey: key,\n                registerSize: registerSize,\n                display: index <= mergedDisplayCount\n            })\n        }, renderRawItem(item, index));\n    } : function(item, index) {\n        var key = getKey(item, index);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, {\n            order: index,\n            key: key,\n            item: item,\n            renderItem: mergedRenderItem,\n            itemKey: key,\n            registerSize: registerSize,\n            display: index <= mergedDisplayCount\n        }));\n    };\n    // >>>>> Rest node\n    var restNode;\n    var restContextProps = {\n        order: displayRest ? mergedDisplayCount : Number.MAX_SAFE_INTEGER,\n        className: \"\".concat(itemPrefixCls, \"-rest\"),\n        registerSize: registerOverflowSize,\n        display: displayRest\n    };\n    if (!renderRawRest) {\n        var mergedRenderRest = renderRest || defaultRenderRest;\n        restNode = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, restContextProps), typeof mergedRenderRest === \"function\" ? mergedRenderRest(omittedItems) : mergedRenderRest);\n    } else if (renderRawRest) {\n        restNode = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.createElement(_context__WEBPACK_IMPORTED_MODULE_11__.OverflowContext.Provider, {\n            value: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, itemSharedProps), restContextProps)\n        }, renderRawRest(omittedItems));\n    }\n    var overflowNode = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        className: classnames__WEBPACK_IMPORTED_MODULE_5___default()(!invalidate && prefixCls, className),\n        style: style,\n        ref: ref\n    }, restProps), mergedData.map(internalRenderItemNode), showRest ? restNode : null, suffix && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.createElement(_Item__WEBPACK_IMPORTED_MODULE_8__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, itemSharedProps, {\n        responsive: isResponsive,\n        responsiveDisabled: !shouldResponsive,\n        order: mergedDisplayCount,\n        className: \"\".concat(itemPrefixCls, \"-suffix\"),\n        registerSize: registerSuffixSize,\n        display: true,\n        style: suffixStyle\n    }), suffix));\n    if (isResponsive) {\n        overflowNode = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            onResize: onOverflowResize,\n            disabled: !shouldResponsive\n        }, overflowNode);\n    }\n    return overflowNode;\n}\nvar ForwardOverflow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.forwardRef(Overflow);\nForwardOverflow.displayName = \"Overflow\";\nForwardOverflow.Item = _RawItem__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\nForwardOverflow.RESPONSIVE = RESPONSIVE;\nForwardOverflow.INVALIDATE = INVALIDATE;\n// Convert to generic type\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardOverflow);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/Overflow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/RawItem.js":
/*!************************************************!*\
  !*** ./node_modules/rc-overflow/es/RawItem.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _Item__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Item */ \"(ssr)/./node_modules/rc-overflow/es/Item.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/rc-overflow/es/context.js\");\n\n\nvar _excluded = [\n    \"component\"\n], _excluded2 = [\n    \"className\"\n], _excluded3 = [\n    \"className\"\n];\n\n\n\n\nvar InternalRawItem = function InternalRawItem(props, ref) {\n    var context = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_context__WEBPACK_IMPORTED_MODULE_5__.OverflowContext);\n    // Render directly when context not provided\n    if (!context) {\n        var _props$component = props.component, Component = _props$component === void 0 ? \"div\" : _props$component, _restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(props, _excluded);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, _restProps, {\n            ref: ref\n        }));\n    }\n    var contextClassName = context.className, restContext = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(context, _excluded2);\n    var className = props.className, restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(props, _excluded3);\n    // Do not pass context to sub item to avoid multiple measure\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(_context__WEBPACK_IMPORTED_MODULE_5__.OverflowContext.Provider, {\n        value: null\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(_Item__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        ref: ref,\n        className: classnames__WEBPACK_IMPORTED_MODULE_3___default()(contextClassName, className)\n    }, restContext, restProps)));\n};\nvar RawItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(InternalRawItem);\nRawItem.displayName = \"RawItem\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RawItem);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/RawItem.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/context.js":
/*!************************************************!*\
  !*** ./node_modules/rc-overflow/es/context.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverflowContext: () => (/* binding */ OverflowContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar OverflowContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0___default().createContext(null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvY29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMEI7QUFDbkIsSUFBSUMsa0JBQWtCLFdBQVcsR0FBRUQsMERBQW1CLENBQUMsTUFBTSIsInNvdXJjZXMiOlsid2VicGFjazovL2RydXRvby8uL25vZGVfbW9kdWxlcy9yYy1vdmVyZmxvdy9lcy9jb250ZXh0LmpzP2ExN2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmV4cG9ydCB2YXIgT3ZlcmZsb3dDb250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQobnVsbCk7Il0sIm5hbWVzIjpbIlJlYWN0IiwiT3ZlcmZsb3dDb250ZXh0IiwiY3JlYXRlQ29udGV4dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js":
/*!************************************************************!*\
  !*** ./node_modules/rc-overflow/es/hooks/channelUpdate.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ channelUpdate)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n\nfunction channelUpdate(callback) {\n    if (typeof MessageChannel === \"undefined\") {\n        (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(callback);\n    } else {\n        var channel = new MessageChannel();\n        channel.port1.onmessage = function() {\n            return callback();\n        };\n        channel.port2.postMessage(undefined);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvaG9va3MvY2hhbm5lbFVwZGF0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpQztBQUNsQixTQUFTQyxjQUFjQyxRQUFRO0lBQzVDLElBQUksT0FBT0MsbUJBQW1CLGFBQWE7UUFDekNILDBEQUFHQSxDQUFDRTtJQUNOLE9BQU87UUFDTCxJQUFJRSxVQUFVLElBQUlEO1FBQ2xCQyxRQUFRQyxLQUFLLENBQUNDLFNBQVMsR0FBRztZQUN4QixPQUFPSjtRQUNUO1FBQ0FFLFFBQVFHLEtBQUssQ0FBQ0MsV0FBVyxDQUFDQztJQUM1QjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJ1dG9vLy4vbm9kZV9tb2R1bGVzL3JjLW92ZXJmbG93L2VzL2hvb2tzL2NoYW5uZWxVcGRhdGUuanM/MjU4MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgcmFmIGZyb20gXCJyYy11dGlsL2VzL3JhZlwiO1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gY2hhbm5lbFVwZGF0ZShjYWxsYmFjaykge1xuICBpZiAodHlwZW9mIE1lc3NhZ2VDaGFubmVsID09PSAndW5kZWZpbmVkJykge1xuICAgIHJhZihjYWxsYmFjayk7XG4gIH0gZWxzZSB7XG4gICAgdmFyIGNoYW5uZWwgPSBuZXcgTWVzc2FnZUNoYW5uZWwoKTtcbiAgICBjaGFubmVsLnBvcnQxLm9ubWVzc2FnZSA9IGZ1bmN0aW9uICgpIHtcbiAgICAgIHJldHVybiBjYWxsYmFjaygpO1xuICAgIH07XG4gICAgY2hhbm5lbC5wb3J0Mi5wb3N0TWVzc2FnZSh1bmRlZmluZWQpO1xuICB9XG59Il0sIm5hbWVzIjpbInJhZiIsImNoYW5uZWxVcGRhdGUiLCJjYWxsYmFjayIsIk1lc3NhZ2VDaGFubmVsIiwiY2hhbm5lbCIsInBvcnQxIiwib25tZXNzYWdlIiwicG9ydDIiLCJwb3N0TWVzc2FnZSIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js":
/*!*************************************************************!*\
  !*** ./node_modules/rc-overflow/es/hooks/useEffectState.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useEffectState),\n/* harmony export */   useBatcher: () => (/* binding */ useBatcher)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useEvent */ \"(ssr)/./node_modules/rc-util/es/hooks/useEvent.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _channelUpdate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./channelUpdate */ \"(ssr)/./node_modules/rc-overflow/es/hooks/channelUpdate.js\");\n\n\n\n\n\n/**\n * Batcher for record any `useEffectState` need update.\n */ function useBatcher() {\n    // Updater Trigger\n    var updateFuncRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n    // Notify update\n    var notifyEffectUpdate = function notifyEffectUpdate(callback) {\n        if (!updateFuncRef.current) {\n            updateFuncRef.current = [];\n            (0,_channelUpdate__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function() {\n                (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.unstable_batchedUpdates)(function() {\n                    updateFuncRef.current.forEach(function(fn) {\n                        fn();\n                    });\n                    updateFuncRef.current = null;\n                });\n            });\n        }\n        updateFuncRef.current.push(callback);\n    };\n    return notifyEffectUpdate;\n}\n/**\n * Trigger state update by `useLayoutEffect` to save perf.\n */ function useEffectState(notifyEffectUpdate, defaultValue) {\n    // Value\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_2__.useState(defaultValue), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_React$useState, 2), stateValue = _React$useState2[0], setStateValue = _React$useState2[1];\n    // Set State\n    var setEffectVal = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function(nextValue) {\n        notifyEffectUpdate(function() {\n            setStateValue(nextValue);\n        });\n    });\n    return [\n        stateValue,\n        setEffectVal\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/hooks/useEffectState.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-overflow/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-overflow/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Overflow__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Overflow */ \"(ssr)/./node_modules/rc-overflow/es/Overflow.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Overflow__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtb3ZlcmZsb3cvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBa0M7QUFDbEMsaUVBQWVBLGlEQUFRQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJ1dG9vLy4vbm9kZV9tb2R1bGVzL3JjLW92ZXJmbG93L2VzL2luZGV4LmpzPzY2ZTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IE92ZXJmbG93IGZyb20gJy4vT3ZlcmZsb3cnO1xuZXhwb3J0IGRlZmF1bHQgT3ZlcmZsb3c7Il0sIm5hbWVzIjpbIk92ZXJmbG93Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-overflow/es/index.js\n");

/***/ })

};
;