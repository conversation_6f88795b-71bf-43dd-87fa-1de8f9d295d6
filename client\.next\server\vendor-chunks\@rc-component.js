"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@rc-component";
exports.ids = ["vendor-chunks/@rc-component"];
exports.modules = {

/***/ "(ssr)/./node_modules/@rc-component/context/es/Immutable.js":
/*!************************************************************!*\
  !*** ./node_modules/@rc-component/context/es/Immutable.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createImmutable)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n/**\n * Create Immutable pair for `makeImmutable` and `responseImmutable`.\n */ function createImmutable() {\n    var ImmutableContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createContext(null);\n    /**\n   * Get render update mark by `makeImmutable` root.\n   * Do not deps on the return value as render times\n   * but only use for `useMemo` or `useCallback` deps.\n   */ function useImmutableMark() {\n        return react__WEBPACK_IMPORTED_MODULE_2__.useContext(ImmutableContext);\n    }\n    /**\n  * Wrapped Component will be marked as Immutable.\n  * When Component parent trigger render,\n  * it will notice children component (use with `responseImmutable`) node that parent has updated.\n  * @param Component Passed Component\n  * @param triggerRender Customize trigger `responseImmutable` children re-render logic. Default will always trigger re-render when this component re-render.\n  */ function makeImmutable(Component, shouldTriggerRender) {\n        var refAble = (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_1__.supportRef)(Component);\n        var ImmutableComponent = function ImmutableComponent(props, ref) {\n            var refProps = refAble ? {\n                ref: ref\n            } : {};\n            var renderTimesRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(0);\n            var prevProps = react__WEBPACK_IMPORTED_MODULE_2__.useRef(props);\n            // If parent has the context, we do not wrap it\n            var mark = useImmutableMark();\n            if (mark !== null) {\n                return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, refProps));\n            }\n            if (// Always trigger re-render if not provide `notTriggerRender`\n            !shouldTriggerRender || shouldTriggerRender(prevProps.current, props)) {\n                renderTimesRef.current += 1;\n            }\n            prevProps.current = props;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(ImmutableContext.Provider, {\n                value: renderTimesRef.current\n            }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, refProps)));\n        };\n        if (true) {\n            ImmutableComponent.displayName = \"ImmutableRoot(\".concat(Component.displayName || Component.name, \")\");\n        }\n        return refAble ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(ImmutableComponent) : ImmutableComponent;\n    }\n    /**\n   * Wrapped Component with `React.memo`.\n   * But will rerender when parent with `makeImmutable` rerender.\n   */ function responseImmutable(Component, propsAreEqual) {\n        var refAble = (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_1__.supportRef)(Component);\n        var ImmutableComponent = function ImmutableComponent(props, ref) {\n            var refProps = refAble ? {\n                ref: ref\n            } : {};\n            useImmutableMark();\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createElement(Component, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, refProps));\n        };\n        if (true) {\n            ImmutableComponent.displayName = \"ImmutableResponse(\".concat(Component.displayName || Component.name, \")\");\n        }\n        return refAble ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.memo(/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(ImmutableComponent), propsAreEqual) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.memo(ImmutableComponent, propsAreEqual);\n    }\n    return {\n        makeImmutable: makeImmutable,\n        responseImmutable: responseImmutable,\n        useImmutableMark: useImmutableMark\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/context/es/Immutable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/context/es/context.js":
/*!**********************************************************!*\
  !*** ./node_modules/@rc-component/context/es/context.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext),\n/* harmony export */   useContext: () => (/* binding */ useContext)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useEvent */ \"(ssr)/./node_modules/rc-util/es/hooks/useEvent.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var rc_util_es_isEqual__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util/es/isEqual */ \"(ssr)/./node_modules/rc-util/es/isEqual.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\nfunction createContext(defaultValue) {\n    var Context = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.createContext(undefined);\n    var Provider = function Provider(_ref) {\n        var value = _ref.value, children = _ref.children;\n        var valueRef = react__WEBPACK_IMPORTED_MODULE_4__.useRef(value);\n        valueRef.current = value;\n        var _React$useState = react__WEBPACK_IMPORTED_MODULE_4__.useState(function() {\n            return {\n                getValue: function getValue() {\n                    return valueRef.current;\n                },\n                listeners: new Set()\n            };\n        }), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_React$useState, 1), context = _React$useState2[0];\n        (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function() {\n            (0,react_dom__WEBPACK_IMPORTED_MODULE_5__.unstable_batchedUpdates)(function() {\n                context.listeners.forEach(function(listener) {\n                    listener(value);\n                });\n            });\n        }, [\n            value\n        ]);\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_4__.createElement(Context.Provider, {\n            value: context\n        }, children);\n    };\n    return {\n        Context: Context,\n        Provider: Provider,\n        defaultValue: defaultValue\n    };\n}\n/** e.g. useSelect(userContext) => user */ /** e.g. useSelect(userContext, user => user.name) => user.name */ /** e.g. useSelect(userContext, ['name', 'age']) => user { name, age } */ /** e.g. useSelect(userContext, 'name') => user.name */ function useContext(holder, selector) {\n    var eventSelector = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(typeof selector === \"function\" ? selector : function(ctx) {\n        if (selector === undefined) {\n            return ctx;\n        }\n        if (!Array.isArray(selector)) {\n            return ctx[selector];\n        }\n        var obj = {};\n        selector.forEach(function(key) {\n            obj[key] = ctx[key];\n        });\n        return obj;\n    });\n    var context = react__WEBPACK_IMPORTED_MODULE_4__.useContext(holder === null || holder === void 0 ? void 0 : holder.Context);\n    var _ref2 = context || {}, listeners = _ref2.listeners, getValue = _ref2.getValue;\n    var valueRef = react__WEBPACK_IMPORTED_MODULE_4__.useRef();\n    valueRef.current = eventSelector(context ? getValue() : holder === null || holder === void 0 ? void 0 : holder.defaultValue);\n    var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_4__.useState({}), _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_React$useState3, 2), forceUpdate = _React$useState4[1];\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function() {\n        if (!context) {\n            return;\n        }\n        function trigger(nextValue) {\n            var nextSelectorValue = eventSelector(nextValue);\n            if (!(0,rc_util_es_isEqual__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(valueRef.current, nextSelectorValue, true)) {\n                forceUpdate({});\n            }\n        }\n        listeners.add(trigger);\n        return function() {\n            listeners.delete(trigger);\n        };\n    }, [\n        context\n    ]);\n    return valueRef.current;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/context/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/context/es/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@rc-component/context/es/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* reexport safe */ _context__WEBPACK_IMPORTED_MODULE_0__.createContext),\n/* harmony export */   createImmutable: () => (/* reexport safe */ _Immutable__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   makeImmutable: () => (/* binding */ makeImmutable),\n/* harmony export */   responseImmutable: () => (/* binding */ responseImmutable),\n/* harmony export */   useContext: () => (/* reexport safe */ _context__WEBPACK_IMPORTED_MODULE_0__.useContext),\n/* harmony export */   useImmutableMark: () => (/* binding */ useImmutableMark)\n/* harmony export */ });\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/@rc-component/context/es/context.js\");\n/* harmony import */ var _Immutable__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Immutable */ \"(ssr)/./node_modules/@rc-component/context/es/Immutable.js\");\n\n\n// For legacy usage, we export it directly\nvar _createImmutable = (0,_Immutable__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(), makeImmutable = _createImmutable.makeImmutable, responseImmutable = _createImmutable.responseImmutable, useImmutableMark = _createImmutable.useImmutableMark;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJjLWNvbXBvbmVudC9jb250ZXh0L2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXNEO0FBQ1o7QUFFMUMsMENBQTBDO0FBQzFDLElBQUlHLG1CQUFtQkQsc0RBQWVBLElBQ3BDRSxnQkFBZ0JELGlCQUFpQkMsYUFBYSxFQUM5Q0Msb0JBQW9CRixpQkFBaUJFLGlCQUFpQixFQUN0REMsbUJBQW1CSCxpQkFBaUJHLGdCQUFnQjtBQUNvRCIsInNvdXJjZXMiOlsid2VicGFjazovL2RydXRvby8uL25vZGVfbW9kdWxlcy9AcmMtY29tcG9uZW50L2NvbnRleHQvZXMvaW5kZXguanM/NjlkNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0IH0gZnJvbSBcIi4vY29udGV4dFwiO1xuaW1wb3J0IGNyZWF0ZUltbXV0YWJsZSBmcm9tIFwiLi9JbW11dGFibGVcIjtcblxuLy8gRm9yIGxlZ2FjeSB1c2FnZSwgd2UgZXhwb3J0IGl0IGRpcmVjdGx5XG52YXIgX2NyZWF0ZUltbXV0YWJsZSA9IGNyZWF0ZUltbXV0YWJsZSgpLFxuICBtYWtlSW1tdXRhYmxlID0gX2NyZWF0ZUltbXV0YWJsZS5tYWtlSW1tdXRhYmxlLFxuICByZXNwb25zZUltbXV0YWJsZSA9IF9jcmVhdGVJbW11dGFibGUucmVzcG9uc2VJbW11dGFibGUsXG4gIHVzZUltbXV0YWJsZU1hcmsgPSBfY3JlYXRlSW1tdXRhYmxlLnVzZUltbXV0YWJsZU1hcms7XG5leHBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCBjcmVhdGVJbW11dGFibGUsIG1ha2VJbW11dGFibGUsIHJlc3BvbnNlSW1tdXRhYmxlLCB1c2VJbW11dGFibGVNYXJrIH07Il0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwiY3JlYXRlSW1tdXRhYmxlIiwiX2NyZWF0ZUltbXV0YWJsZSIsIm1ha2VJbW11dGFibGUiLCJyZXNwb25zZUltbXV0YWJsZSIsInVzZUltbXV0YWJsZU1hcmsiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/context/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/portal/es/Context.js":
/*!*********************************************************!*\
  !*** ./node_modules/@rc-component/portal/es/Context.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar OrderContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OrderContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJjLWNvbXBvbmVudC9wb3J0YWwvZXMvQ29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0I7QUFDL0IsSUFBSUMsZUFBZSxXQUFXLEdBQUVELGdEQUFtQixDQUFDO0FBQ3BELGlFQUFlQyxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJ1dG9vLy4vbm9kZV9tb2R1bGVzL0ByYy1jb21wb25lbnQvcG9ydGFsL2VzL0NvbnRleHQuanM/ZGY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG52YXIgT3JkZXJDb250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQobnVsbCk7XG5leHBvcnQgZGVmYXVsdCBPcmRlckNvbnRleHQ7Il0sIm5hbWVzIjpbIlJlYWN0IiwiT3JkZXJDb250ZXh0IiwiY3JlYXRlQ29udGV4dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/portal/es/Context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/portal/es/Portal.js":
/*!********************************************************!*\
  !*** ./node_modules/@rc-component/portal/es/Portal.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ \"(ssr)/./node_modules/rc-util/es/Dom/canUseDom.js\");\n/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/warning */ \"(ssr)/./node_modules/rc-util/es/warning.js\");\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var _Context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Context */ \"(ssr)/./node_modules/@rc-component/portal/es/Context.js\");\n/* harmony import */ var _useDom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./useDom */ \"(ssr)/./node_modules/@rc-component/portal/es/useDom.js\");\n/* harmony import */ var _useScrollLocker__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./useScrollLocker */ \"(ssr)/./node_modules/@rc-component/portal/es/useScrollLocker.js\");\n/* harmony import */ var _mock__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./mock */ \"(ssr)/./node_modules/@rc-component/portal/es/mock.js\");\n\n\n\n\n\n\n\n\n\n\nvar getPortalContainer = function getPortalContainer(getContainer) {\n    if (getContainer === false) {\n        return false;\n    }\n    if (!(0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_3__[\"default\"])() || !getContainer) {\n        return null;\n    }\n    if (typeof getContainer === \"string\") {\n        return document.querySelector(getContainer);\n    }\n    if (typeof getContainer === \"function\") {\n        return getContainer();\n    }\n    return getContainer;\n};\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function(props, ref) {\n    var open = props.open, autoLock = props.autoLock, getContainer = props.getContainer, debug = props.debug, _props$autoDestroy = props.autoDestroy, autoDestroy = _props$autoDestroy === void 0 ? true : _props$autoDestroy, children = props.children;\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_1__.useState(open), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_React$useState, 2), shouldRender = _React$useState2[0], setShouldRender = _React$useState2[1];\n    var mergedRender = shouldRender || open;\n    // ========================= Warning =========================\n    if (true) {\n        (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_4__[\"default\"])((0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_3__[\"default\"])() || !open, \"Portal only work in client side. Please call 'useEffect' to show Portal instead default render in SSR.\");\n    }\n    // ====================== Should Render ======================\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(function() {\n        if (autoDestroy || open) {\n            setShouldRender(open);\n        }\n    }, [\n        open,\n        autoDestroy\n    ]);\n    // ======================== Container ========================\n    var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_1__.useState(function() {\n        return getPortalContainer(getContainer);\n    }), _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_React$useState3, 2), innerContainer = _React$useState4[0], setInnerContainer = _React$useState4[1];\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(function() {\n        var customizeContainer = getPortalContainer(getContainer);\n        // Tell component that we check this in effect which is safe to be `null`\n        setInnerContainer(customizeContainer !== null && customizeContainer !== void 0 ? customizeContainer : null);\n    });\n    var _useDom = (0,_useDom__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(mergedRender && !innerContainer, debug), _useDom2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_useDom, 2), defaultContainer = _useDom2[0], queueCreate = _useDom2[1];\n    var mergedContainer = innerContainer !== null && innerContainer !== void 0 ? innerContainer : defaultContainer;\n    // ========================= Locker ==========================\n    (0,_useScrollLocker__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(autoLock && open && (0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_3__[\"default\"])() && (mergedContainer === defaultContainer || mergedContainer === document.body));\n    // =========================== Ref ===========================\n    var childRef = null;\n    if (children && (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_5__.supportRef)(children) && ref) {\n        var _ref = children;\n        childRef = _ref.ref;\n    }\n    var mergedRef = (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_5__.useComposeRef)(childRef, ref);\n    // ========================= Render ==========================\n    // Do not render when nothing need render\n    // When innerContainer is `undefined`, it may not ready since user use ref in the same render\n    if (!mergedRender || !(0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_3__[\"default\"])() || innerContainer === undefined) {\n        return null;\n    }\n    // Render inline\n    var renderInline = mergedContainer === false || (0,_mock__WEBPACK_IMPORTED_MODULE_9__.inlineMock)();\n    var reffedChildren = children;\n    if (ref) {\n        reffedChildren = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.cloneElement(children, {\n            ref: mergedRef\n        });\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(_Context__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Provider, {\n        value: queueCreate\n    }, renderInline ? reffedChildren : /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(reffedChildren, mergedContainer));\n});\nif (true) {\n    Portal.displayName = \"Portal\";\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Portal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJjLWNvbXBvbmVudC9wb3J0YWwvZXMvUG9ydGFsLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBc0U7QUFDdkM7QUFDVTtBQUNRO0FBQ1I7QUFDa0I7QUFDdEI7QUFDUDtBQUNrQjtBQUNaO0FBQ3BDLElBQUlXLHFCQUFxQixTQUFTQSxtQkFBbUJDLFlBQVk7SUFDL0QsSUFBSUEsaUJBQWlCLE9BQU87UUFDMUIsT0FBTztJQUNUO0lBQ0EsSUFBSSxDQUFDVCxvRUFBU0EsTUFBTSxDQUFDUyxjQUFjO1FBQ2pDLE9BQU87SUFDVDtJQUNBLElBQUksT0FBT0EsaUJBQWlCLFVBQVU7UUFDcEMsT0FBT0MsU0FBU0MsYUFBYSxDQUFDRjtJQUNoQztJQUNBLElBQUksT0FBT0EsaUJBQWlCLFlBQVk7UUFDdEMsT0FBT0E7SUFDVDtJQUNBLE9BQU9BO0FBQ1Q7QUFDQSxJQUFJRyxTQUFTLFdBQVcsR0FBRWQsNkNBQWdCLENBQUMsU0FBVWdCLEtBQUssRUFBRUMsR0FBRztJQUM3RCxJQUFJQyxPQUFPRixNQUFNRSxJQUFJLEVBQ25CQyxXQUFXSCxNQUFNRyxRQUFRLEVBQ3pCUixlQUFlSyxNQUFNTCxZQUFZLEVBQ2pDUyxRQUFRSixNQUFNSSxLQUFLLEVBQ25CQyxxQkFBcUJMLE1BQU1NLFdBQVcsRUFDdENBLGNBQWNELHVCQUF1QixLQUFLLElBQUksT0FBT0Esb0JBQ3JERSxXQUFXUCxNQUFNTyxRQUFRO0lBQzNCLElBQUlDLGtCQUFrQnhCLDJDQUFjLENBQUNrQixPQUNuQ1EsbUJBQW1CM0Isb0ZBQWNBLENBQUN5QixpQkFBaUIsSUFDbkRHLGVBQWVELGdCQUFnQixDQUFDLEVBQUUsRUFDbENFLGtCQUFrQkYsZ0JBQWdCLENBQUMsRUFBRTtJQUN2QyxJQUFJRyxlQUFlRixnQkFBZ0JUO0lBRW5DLDhEQUE4RDtJQUM5RCxJQUFJWSxJQUF5QixFQUFjO1FBQ3pDM0IsOERBQU9BLENBQUNELG9FQUFTQSxNQUFNLENBQUNnQixNQUFNO0lBQ2hDO0lBRUEsOERBQThEO0lBQzlEbEIsNENBQWUsQ0FBQztRQUNkLElBQUlzQixlQUFlSixNQUFNO1lBQ3ZCVSxnQkFBZ0JWO1FBQ2xCO0lBQ0YsR0FBRztRQUFDQTtRQUFNSTtLQUFZO0lBRXRCLDhEQUE4RDtJQUM5RCxJQUFJVSxtQkFBbUJoQywyQ0FBYyxDQUFDO1FBQ2xDLE9BQU9VLG1CQUFtQkM7SUFDNUIsSUFDQXNCLG1CQUFtQmxDLG9GQUFjQSxDQUFDaUMsa0JBQWtCLElBQ3BERSxpQkFBaUJELGdCQUFnQixDQUFDLEVBQUUsRUFDcENFLG9CQUFvQkYsZ0JBQWdCLENBQUMsRUFBRTtJQUN6Q2pDLDRDQUFlLENBQUM7UUFDZCxJQUFJb0MscUJBQXFCMUIsbUJBQW1CQztRQUU1Qyx5RUFBeUU7UUFDekV3QixrQkFBa0JDLHVCQUF1QixRQUFRQSx1QkFBdUIsS0FBSyxJQUFJQSxxQkFBcUI7SUFDeEc7SUFDQSxJQUFJQyxVQUFVOUIsbURBQU1BLENBQUNzQixnQkFBZ0IsQ0FBQ0ssZ0JBQWdCZCxRQUNwRGtCLFdBQVd2QyxvRkFBY0EsQ0FBQ3NDLFNBQVMsSUFDbkNFLG1CQUFtQkQsUUFBUSxDQUFDLEVBQUUsRUFDOUJFLGNBQWNGLFFBQVEsQ0FBQyxFQUFFO0lBQzNCLElBQUlHLGtCQUFrQlAsbUJBQW1CLFFBQVFBLG1CQUFtQixLQUFLLElBQUlBLGlCQUFpQks7SUFFOUYsOERBQThEO0lBQzlEL0IsNERBQWVBLENBQUNXLFlBQVlELFFBQVFoQixvRUFBU0EsTUFBT3VDLENBQUFBLG9CQUFvQkYsb0JBQW9CRSxvQkFBb0I3QixTQUFTOEIsSUFBSTtJQUU3SCw4REFBOEQ7SUFDOUQsSUFBSUMsV0FBVztJQUNmLElBQUlwQixZQUFZbkIsMERBQVVBLENBQUNtQixhQUFhTixLQUFLO1FBQzNDLElBQUkyQixPQUFPckI7UUFDWG9CLFdBQVdDLEtBQUszQixHQUFHO0lBQ3JCO0lBQ0EsSUFBSTRCLFlBQVl4Qyw2REFBYUEsQ0FBQ3NDLFVBQVUxQjtJQUV4Qyw4REFBOEQ7SUFDOUQseUNBQXlDO0lBQ3pDLDZGQUE2RjtJQUM3RixJQUFJLENBQUNZLGdCQUFnQixDQUFDM0Isb0VBQVNBLE1BQU1nQyxtQkFBbUJZLFdBQVc7UUFDakUsT0FBTztJQUNUO0lBRUEsZ0JBQWdCO0lBQ2hCLElBQUlDLGVBQWVOLG9CQUFvQixTQUFTaEMsaURBQVVBO0lBQzFELElBQUl1QyxpQkFBaUJ6QjtJQUNyQixJQUFJTixLQUFLO1FBQ1ArQixpQkFBaUIsV0FBVyxHQUFFaEQsK0NBQWtCLENBQUN1QixVQUFVO1lBQ3pETixLQUFLNEI7UUFDUDtJQUNGO0lBQ0EsT0FBTyxXQUFXLEdBQUU3QyxnREFBbUIsQ0FBQ00sZ0RBQVlBLENBQUM2QyxRQUFRLEVBQUU7UUFDN0RDLE9BQU9aO0lBQ1QsR0FBR08sZUFBZUMsaUJBQWlCLFdBQVcsR0FBRS9DLHVEQUFZQSxDQUFDK0MsZ0JBQWdCUDtBQUMvRTtBQUNBLElBQUlYLElBQXlCLEVBQWM7SUFDekNoQixPQUFPdUMsV0FBVyxHQUFHO0FBQ3ZCO0FBQ0EsaUVBQWV2QyxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJ1dG9vLy4vbm9kZV9tb2R1bGVzL0ByYy1jb21wb25lbnQvcG9ydGFsL2VzL1BvcnRhbC5qcz8wY2I5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfc2xpY2VkVG9BcnJheSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vc2xpY2VkVG9BcnJheVwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY3JlYXRlUG9ydGFsIH0gZnJvbSAncmVhY3QtZG9tJztcbmltcG9ydCBjYW5Vc2VEb20gZnJvbSBcInJjLXV0aWwvZXMvRG9tL2NhblVzZURvbVwiO1xuaW1wb3J0IHdhcm5pbmcgZnJvbSBcInJjLXV0aWwvZXMvd2FybmluZ1wiO1xuaW1wb3J0IHsgc3VwcG9ydFJlZiwgdXNlQ29tcG9zZVJlZiB9IGZyb20gXCJyYy11dGlsL2VzL3JlZlwiO1xuaW1wb3J0IE9yZGVyQ29udGV4dCBmcm9tIFwiLi9Db250ZXh0XCI7XG5pbXBvcnQgdXNlRG9tIGZyb20gXCIuL3VzZURvbVwiO1xuaW1wb3J0IHVzZVNjcm9sbExvY2tlciBmcm9tIFwiLi91c2VTY3JvbGxMb2NrZXJcIjtcbmltcG9ydCB7IGlubGluZU1vY2sgfSBmcm9tIFwiLi9tb2NrXCI7XG52YXIgZ2V0UG9ydGFsQ29udGFpbmVyID0gZnVuY3Rpb24gZ2V0UG9ydGFsQ29udGFpbmVyKGdldENvbnRhaW5lcikge1xuICBpZiAoZ2V0Q29udGFpbmVyID09PSBmYWxzZSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBpZiAoIWNhblVzZURvbSgpIHx8ICFnZXRDb250YWluZXIpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuICBpZiAodHlwZW9mIGdldENvbnRhaW5lciA9PT0gJ3N0cmluZycpIHtcbiAgICByZXR1cm4gZG9jdW1lbnQucXVlcnlTZWxlY3RvcihnZXRDb250YWluZXIpO1xuICB9XG4gIGlmICh0eXBlb2YgZ2V0Q29udGFpbmVyID09PSAnZnVuY3Rpb24nKSB7XG4gICAgcmV0dXJuIGdldENvbnRhaW5lcigpO1xuICB9XG4gIHJldHVybiBnZXRDb250YWluZXI7XG59O1xudmFyIFBvcnRhbCA9IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKGZ1bmN0aW9uIChwcm9wcywgcmVmKSB7XG4gIHZhciBvcGVuID0gcHJvcHMub3BlbixcbiAgICBhdXRvTG9jayA9IHByb3BzLmF1dG9Mb2NrLFxuICAgIGdldENvbnRhaW5lciA9IHByb3BzLmdldENvbnRhaW5lcixcbiAgICBkZWJ1ZyA9IHByb3BzLmRlYnVnLFxuICAgIF9wcm9wcyRhdXRvRGVzdHJveSA9IHByb3BzLmF1dG9EZXN0cm95LFxuICAgIGF1dG9EZXN0cm95ID0gX3Byb3BzJGF1dG9EZXN0cm95ID09PSB2b2lkIDAgPyB0cnVlIDogX3Byb3BzJGF1dG9EZXN0cm95LFxuICAgIGNoaWxkcmVuID0gcHJvcHMuY2hpbGRyZW47XG4gIHZhciBfUmVhY3QkdXNlU3RhdGUgPSBSZWFjdC51c2VTdGF0ZShvcGVuKSxcbiAgICBfUmVhY3QkdXNlU3RhdGUyID0gX3NsaWNlZFRvQXJyYXkoX1JlYWN0JHVzZVN0YXRlLCAyKSxcbiAgICBzaG91bGRSZW5kZXIgPSBfUmVhY3QkdXNlU3RhdGUyWzBdLFxuICAgIHNldFNob3VsZFJlbmRlciA9IF9SZWFjdCR1c2VTdGF0ZTJbMV07XG4gIHZhciBtZXJnZWRSZW5kZXIgPSBzaG91bGRSZW5kZXIgfHwgb3BlbjtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09IFdhcm5pbmcgPT09PT09PT09PT09PT09PT09PT09PT09PVxuICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgIHdhcm5pbmcoY2FuVXNlRG9tKCkgfHwgIW9wZW4sIFwiUG9ydGFsIG9ubHkgd29yayBpbiBjbGllbnQgc2lkZS4gUGxlYXNlIGNhbGwgJ3VzZUVmZmVjdCcgdG8gc2hvdyBQb3J0YWwgaW5zdGVhZCBkZWZhdWx0IHJlbmRlciBpbiBTU1IuXCIpO1xuICB9XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PSBTaG91bGQgUmVuZGVyID09PT09PT09PT09PT09PT09PT09PT1cbiAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICBpZiAoYXV0b0Rlc3Ryb3kgfHwgb3Blbikge1xuICAgICAgc2V0U2hvdWxkUmVuZGVyKG9wZW4pO1xuICAgIH1cbiAgfSwgW29wZW4sIGF1dG9EZXN0cm95XSk7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09IENvbnRhaW5lciA9PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdmFyIF9SZWFjdCR1c2VTdGF0ZTMgPSBSZWFjdC51c2VTdGF0ZShmdW5jdGlvbiAoKSB7XG4gICAgICByZXR1cm4gZ2V0UG9ydGFsQ29udGFpbmVyKGdldENvbnRhaW5lcik7XG4gICAgfSksXG4gICAgX1JlYWN0JHVzZVN0YXRlNCA9IF9zbGljZWRUb0FycmF5KF9SZWFjdCR1c2VTdGF0ZTMsIDIpLFxuICAgIGlubmVyQ29udGFpbmVyID0gX1JlYWN0JHVzZVN0YXRlNFswXSxcbiAgICBzZXRJbm5lckNvbnRhaW5lciA9IF9SZWFjdCR1c2VTdGF0ZTRbMV07XG4gIFJlYWN0LnVzZUVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgdmFyIGN1c3RvbWl6ZUNvbnRhaW5lciA9IGdldFBvcnRhbENvbnRhaW5lcihnZXRDb250YWluZXIpO1xuXG4gICAgLy8gVGVsbCBjb21wb25lbnQgdGhhdCB3ZSBjaGVjayB0aGlzIGluIGVmZmVjdCB3aGljaCBpcyBzYWZlIHRvIGJlIGBudWxsYFxuICAgIHNldElubmVyQ29udGFpbmVyKGN1c3RvbWl6ZUNvbnRhaW5lciAhPT0gbnVsbCAmJiBjdXN0b21pemVDb250YWluZXIgIT09IHZvaWQgMCA/IGN1c3RvbWl6ZUNvbnRhaW5lciA6IG51bGwpO1xuICB9KTtcbiAgdmFyIF91c2VEb20gPSB1c2VEb20obWVyZ2VkUmVuZGVyICYmICFpbm5lckNvbnRhaW5lciwgZGVidWcpLFxuICAgIF91c2VEb20yID0gX3NsaWNlZFRvQXJyYXkoX3VzZURvbSwgMiksXG4gICAgZGVmYXVsdENvbnRhaW5lciA9IF91c2VEb20yWzBdLFxuICAgIHF1ZXVlQ3JlYXRlID0gX3VzZURvbTJbMV07XG4gIHZhciBtZXJnZWRDb250YWluZXIgPSBpbm5lckNvbnRhaW5lciAhPT0gbnVsbCAmJiBpbm5lckNvbnRhaW5lciAhPT0gdm9pZCAwID8gaW5uZXJDb250YWluZXIgOiBkZWZhdWx0Q29udGFpbmVyO1xuXG4gIC8vID09PT09PT09PT09PT09PT09PT09PT09PT0gTG9ja2VyID09PT09PT09PT09PT09PT09PT09PT09PT09XG4gIHVzZVNjcm9sbExvY2tlcihhdXRvTG9jayAmJiBvcGVuICYmIGNhblVzZURvbSgpICYmIChtZXJnZWRDb250YWluZXIgPT09IGRlZmF1bHRDb250YWluZXIgfHwgbWVyZ2VkQ29udGFpbmVyID09PSBkb2N1bWVudC5ib2R5KSk7XG5cbiAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09IFJlZiA9PT09PT09PT09PT09PT09PT09PT09PT09PT1cbiAgdmFyIGNoaWxkUmVmID0gbnVsbDtcbiAgaWYgKGNoaWxkcmVuICYmIHN1cHBvcnRSZWYoY2hpbGRyZW4pICYmIHJlZikge1xuICAgIHZhciBfcmVmID0gY2hpbGRyZW47XG4gICAgY2hpbGRSZWYgPSBfcmVmLnJlZjtcbiAgfVxuICB2YXIgbWVyZ2VkUmVmID0gdXNlQ29tcG9zZVJlZihjaGlsZFJlZiwgcmVmKTtcblxuICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09IFJlbmRlciA9PT09PT09PT09PT09PT09PT09PT09PT09PVxuICAvLyBEbyBub3QgcmVuZGVyIHdoZW4gbm90aGluZyBuZWVkIHJlbmRlclxuICAvLyBXaGVuIGlubmVyQ29udGFpbmVyIGlzIGB1bmRlZmluZWRgLCBpdCBtYXkgbm90IHJlYWR5IHNpbmNlIHVzZXIgdXNlIHJlZiBpbiB0aGUgc2FtZSByZW5kZXJcbiAgaWYgKCFtZXJnZWRSZW5kZXIgfHwgIWNhblVzZURvbSgpIHx8IGlubmVyQ29udGFpbmVyID09PSB1bmRlZmluZWQpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIC8vIFJlbmRlciBpbmxpbmVcbiAgdmFyIHJlbmRlcklubGluZSA9IG1lcmdlZENvbnRhaW5lciA9PT0gZmFsc2UgfHwgaW5saW5lTW9jaygpO1xuICB2YXIgcmVmZmVkQ2hpbGRyZW4gPSBjaGlsZHJlbjtcbiAgaWYgKHJlZikge1xuICAgIHJlZmZlZENoaWxkcmVuID0gLyojX19QVVJFX18qL1JlYWN0LmNsb25lRWxlbWVudChjaGlsZHJlbiwge1xuICAgICAgcmVmOiBtZXJnZWRSZWZcbiAgICB9KTtcbiAgfVxuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoT3JkZXJDb250ZXh0LlByb3ZpZGVyLCB7XG4gICAgdmFsdWU6IHF1ZXVlQ3JlYXRlXG4gIH0sIHJlbmRlcklubGluZSA/IHJlZmZlZENoaWxkcmVuIDogLyojX19QVVJFX18qL2NyZWF0ZVBvcnRhbChyZWZmZWRDaGlsZHJlbiwgbWVyZ2VkQ29udGFpbmVyKSk7XG59KTtcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIFBvcnRhbC5kaXNwbGF5TmFtZSA9ICdQb3J0YWwnO1xufVxuZXhwb3J0IGRlZmF1bHQgUG9ydGFsOyJdLCJuYW1lcyI6WyJfc2xpY2VkVG9BcnJheSIsIlJlYWN0IiwiY3JlYXRlUG9ydGFsIiwiY2FuVXNlRG9tIiwid2FybmluZyIsInN1cHBvcnRSZWYiLCJ1c2VDb21wb3NlUmVmIiwiT3JkZXJDb250ZXh0IiwidXNlRG9tIiwidXNlU2Nyb2xsTG9ja2VyIiwiaW5saW5lTW9jayIsImdldFBvcnRhbENvbnRhaW5lciIsImdldENvbnRhaW5lciIsImRvY3VtZW50IiwicXVlcnlTZWxlY3RvciIsIlBvcnRhbCIsImZvcndhcmRSZWYiLCJwcm9wcyIsInJlZiIsIm9wZW4iLCJhdXRvTG9jayIsImRlYnVnIiwiX3Byb3BzJGF1dG9EZXN0cm95IiwiYXV0b0Rlc3Ryb3kiLCJjaGlsZHJlbiIsIl9SZWFjdCR1c2VTdGF0ZSIsInVzZVN0YXRlIiwiX1JlYWN0JHVzZVN0YXRlMiIsInNob3VsZFJlbmRlciIsInNldFNob3VsZFJlbmRlciIsIm1lcmdlZFJlbmRlciIsInByb2Nlc3MiLCJ1c2VFZmZlY3QiLCJfUmVhY3QkdXNlU3RhdGUzIiwiX1JlYWN0JHVzZVN0YXRlNCIsImlubmVyQ29udGFpbmVyIiwic2V0SW5uZXJDb250YWluZXIiLCJjdXN0b21pemVDb250YWluZXIiLCJfdXNlRG9tIiwiX3VzZURvbTIiLCJkZWZhdWx0Q29udGFpbmVyIiwicXVldWVDcmVhdGUiLCJtZXJnZWRDb250YWluZXIiLCJib2R5IiwiY2hpbGRSZWYiLCJfcmVmIiwibWVyZ2VkUmVmIiwidW5kZWZpbmVkIiwicmVuZGVySW5saW5lIiwicmVmZmVkQ2hpbGRyZW4iLCJjbG9uZUVsZW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiUHJvdmlkZXIiLCJ2YWx1ZSIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/portal/es/Portal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/portal/es/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/@rc-component/portal/es/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   inlineMock: () => (/* reexport safe */ _mock__WEBPACK_IMPORTED_MODULE_1__.inlineMock)\n/* harmony export */ });\n/* harmony import */ var _Portal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Portal */ \"(ssr)/./node_modules/@rc-component/portal/es/Portal.js\");\n/* harmony import */ var _mock__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mock */ \"(ssr)/./node_modules/@rc-component/portal/es/mock.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Portal__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJjLWNvbXBvbmVudC9wb3J0YWwvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE4QjtBQUNNO0FBQ2Q7QUFDdEIsaUVBQWVBLCtDQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJ1dG9vLy4vbm9kZV9tb2R1bGVzL0ByYy1jb21wb25lbnQvcG9ydGFsL2VzL2luZGV4LmpzPzQwYmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFBvcnRhbCBmcm9tIFwiLi9Qb3J0YWxcIjtcbmltcG9ydCB7IGlubGluZU1vY2sgfSBmcm9tIFwiLi9tb2NrXCI7XG5leHBvcnQgeyBpbmxpbmVNb2NrIH07XG5leHBvcnQgZGVmYXVsdCBQb3J0YWw7Il0sIm5hbWVzIjpbIlBvcnRhbCIsImlubGluZU1vY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/portal/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/portal/es/mock.js":
/*!******************************************************!*\
  !*** ./node_modules/@rc-component/portal/es/mock.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   inlineMock: () => (/* binding */ inlineMock)\n/* harmony export */ });\nvar inline = false;\nfunction inlineMock(nextInline) {\n    if (typeof nextInline === \"boolean\") {\n        inline = nextInline;\n    }\n    return inline;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJjLWNvbXBvbmVudC9wb3J0YWwvZXMvbW9jay5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFPLElBQUlBLFNBQVMsTUFBTTtBQUNuQixTQUFTQyxXQUFXQyxVQUFVO0lBQ25DLElBQUksT0FBT0EsZUFBZSxXQUFXO1FBQ25DRixTQUFTRTtJQUNYO0lBQ0EsT0FBT0Y7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2RydXRvby8uL25vZGVfbW9kdWxlcy9AcmMtY29tcG9uZW50L3BvcnRhbC9lcy9tb2NrLmpzP2FjNGUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBpbmxpbmUgPSBmYWxzZTtcbmV4cG9ydCBmdW5jdGlvbiBpbmxpbmVNb2NrKG5leHRJbmxpbmUpIHtcbiAgaWYgKHR5cGVvZiBuZXh0SW5saW5lID09PSAnYm9vbGVhbicpIHtcbiAgICBpbmxpbmUgPSBuZXh0SW5saW5lO1xuICB9XG4gIHJldHVybiBpbmxpbmU7XG59Il0sIm5hbWVzIjpbImlubGluZSIsImlubGluZU1vY2siLCJuZXh0SW5saW5lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/portal/es/mock.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/portal/es/useDom.js":
/*!********************************************************!*\
  !*** ./node_modules/@rc-component/portal/es/useDom.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useDom)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/Dom/canUseDom */ \"(ssr)/./node_modules/rc-util/es/Dom/canUseDom.js\");\n/* harmony import */ var _Context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Context */ \"(ssr)/./node_modules/@rc-component/portal/es/Context.js\");\n\n\n\n\n\n\nvar EMPTY_LIST = [];\n/**\n * Will add `div` to document. Nest call will keep order\n * @param render Render DOM in document\n */ function useDom(render, debug) {\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_2__.useState(function() {\n        if (!(0,rc_util_es_Dom_canUseDom__WEBPACK_IMPORTED_MODULE_4__[\"default\"])()) {\n            return null;\n        }\n        var defaultEle = document.createElement(\"div\");\n        if ( true && debug) {\n            defaultEle.setAttribute(\"data-debug\", debug);\n        }\n        return defaultEle;\n    }), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState, 1), ele = _React$useState2[0];\n    // ========================== Order ==========================\n    var appendedRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(false);\n    var queueCreate = react__WEBPACK_IMPORTED_MODULE_2__.useContext(_Context__WEBPACK_IMPORTED_MODULE_5__[\"default\"]);\n    var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_2__.useState(EMPTY_LIST), _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState3, 2), queue = _React$useState4[0], setQueue = _React$useState4[1];\n    var mergedQueueCreate = queueCreate || (appendedRef.current ? undefined : function(appendFn) {\n        setQueue(function(origin) {\n            var newQueue = [\n                appendFn\n            ].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(origin));\n            return newQueue;\n        });\n    });\n    // =========================== DOM ===========================\n    function append() {\n        if (!ele.parentElement) {\n            document.body.appendChild(ele);\n        }\n        appendedRef.current = true;\n    }\n    function cleanup() {\n        var _ele$parentElement;\n        (_ele$parentElement = ele.parentElement) === null || _ele$parentElement === void 0 ? void 0 : _ele$parentElement.removeChild(ele);\n        appendedRef.current = false;\n    }\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function() {\n        if (render) {\n            if (queueCreate) {\n                queueCreate(append);\n            } else {\n                append();\n            }\n        } else {\n            cleanup();\n        }\n        return cleanup;\n    }, [\n        render\n    ]);\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function() {\n        if (queue.length) {\n            queue.forEach(function(appendFn) {\n                return appendFn();\n            });\n            setQueue(EMPTY_LIST);\n        }\n    }, [\n        queue\n    ]);\n    return [\n        ele,\n        mergedQueueCreate\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/portal/es/useDom.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/portal/es/useScrollLocker.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@rc-component/portal/es/useScrollLocker.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useScrollLocker)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var rc_util_es_Dom_dynamicCSS__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/Dom/dynamicCSS */ \"(ssr)/./node_modules/rc-util/es/Dom/dynamicCSS.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var rc_util_es_getScrollBarSize__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/getScrollBarSize */ \"(ssr)/./node_modules/rc-util/es/getScrollBarSize.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/@rc-component/portal/es/util.js\");\n\n\n\n\n\n\nvar UNIQUE_ID = \"rc-util-locker-\".concat(Date.now());\nvar uuid = 0;\nfunction useScrollLocker(lock) {\n    var mergedLock = !!lock;\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_1__.useState(function() {\n        uuid += 1;\n        return \"\".concat(UNIQUE_ID, \"_\").concat(uuid);\n    }), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_React$useState, 1), id = _React$useState2[0];\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function() {\n        if (mergedLock) {\n            var scrollbarSize = (0,rc_util_es_getScrollBarSize__WEBPACK_IMPORTED_MODULE_4__.getTargetScrollBarSize)(document.body).width;\n            var isOverflow = (0,_util__WEBPACK_IMPORTED_MODULE_5__.isBodyOverflowing)();\n            (0,rc_util_es_Dom_dynamicCSS__WEBPACK_IMPORTED_MODULE_2__.updateCSS)(\"\\nhtml body {\\n  overflow-y: hidden;\\n  \".concat(isOverflow ? \"width: calc(100% - \".concat(scrollbarSize, \"px);\") : \"\", \"\\n}\"), id);\n        } else {\n            (0,rc_util_es_Dom_dynamicCSS__WEBPACK_IMPORTED_MODULE_2__.removeCSS)(id);\n        }\n        return function() {\n            (0,rc_util_es_Dom_dynamicCSS__WEBPACK_IMPORTED_MODULE_2__.removeCSS)(id);\n        };\n    }, [\n        mergedLock,\n        id\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/portal/es/useScrollLocker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/portal/es/util.js":
/*!******************************************************!*\
  !*** ./node_modules/@rc-component/portal/es/util.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBodyOverflowing: () => (/* binding */ isBodyOverflowing)\n/* harmony export */ });\n/**\n * Test usage export. Do not use in your production\n */ function isBodyOverflowing() {\n    return document.body.scrollHeight > (window.innerHeight || document.documentElement.clientHeight) && window.innerWidth > document.body.offsetWidth;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJjLWNvbXBvbmVudC9wb3J0YWwvZXMvdXRpbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0NBRUMsR0FDTSxTQUFTQTtJQUNkLE9BQU9DLFNBQVNDLElBQUksQ0FBQ0MsWUFBWSxHQUFJQyxDQUFBQSxPQUFPQyxXQUFXLElBQUlKLFNBQVNLLGVBQWUsQ0FBQ0MsWUFBWSxLQUFLSCxPQUFPSSxVQUFVLEdBQUdQLFNBQVNDLElBQUksQ0FBQ08sV0FBVztBQUNwSiIsInNvdXJjZXMiOlsid2VicGFjazovL2RydXRvby8uL25vZGVfbW9kdWxlcy9AcmMtY29tcG9uZW50L3BvcnRhbC9lcy91dGlsLmpzP2RlMmYiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBUZXN0IHVzYWdlIGV4cG9ydC4gRG8gbm90IHVzZSBpbiB5b3VyIHByb2R1Y3Rpb25cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzQm9keU92ZXJmbG93aW5nKCkge1xuICByZXR1cm4gZG9jdW1lbnQuYm9keS5zY3JvbGxIZWlnaHQgPiAod2luZG93LmlubmVySGVpZ2h0IHx8IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRIZWlnaHQpICYmIHdpbmRvdy5pbm5lcldpZHRoID4gZG9jdW1lbnQuYm9keS5vZmZzZXRXaWR0aDtcbn0iXSwibmFtZXMiOlsiaXNCb2R5T3ZlcmZsb3dpbmciLCJkb2N1bWVudCIsImJvZHkiLCJzY3JvbGxIZWlnaHQiLCJ3aW5kb3ciLCJpbm5lckhlaWdodCIsImRvY3VtZW50RWxlbWVudCIsImNsaWVudEhlaWdodCIsImlubmVyV2lkdGgiLCJvZmZzZXRXaWR0aCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/portal/es/util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/trigger/es/Popup/Arrow.js":
/*!**************************************************************!*\
  !*** ./node_modules/@rc-component/trigger/es/Popup/Arrow.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Arrow)\n/* harmony export */ });\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Arrow(props) {\n    var prefixCls = props.prefixCls, align = props.align, arrow = props.arrow, arrowPos = props.arrowPos;\n    var _ref = arrow || {}, className = _ref.className, content = _ref.content;\n    var _arrowPos$x = arrowPos.x, x = _arrowPos$x === void 0 ? 0 : _arrowPos$x, _arrowPos$y = arrowPos.y, y = _arrowPos$y === void 0 ? 0 : _arrowPos$y;\n    var arrowRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef();\n    // Skip if no align\n    if (!align || !align.points) {\n        return null;\n    }\n    var alignStyle = {\n        position: \"absolute\"\n    };\n    // Skip if no need to align\n    if (align.autoArrow !== false) {\n        var popupPoints = align.points[0];\n        var targetPoints = align.points[1];\n        var popupTB = popupPoints[0];\n        var popupLR = popupPoints[1];\n        var targetTB = targetPoints[0];\n        var targetLR = targetPoints[1];\n        // Top & Bottom\n        if (popupTB === targetTB || ![\n            \"t\",\n            \"b\"\n        ].includes(popupTB)) {\n            alignStyle.top = y;\n        } else if (popupTB === \"t\") {\n            alignStyle.top = 0;\n        } else {\n            alignStyle.bottom = 0;\n        }\n        // Left & Right\n        if (popupLR === targetLR || ![\n            \"l\",\n            \"r\"\n        ].includes(popupLR)) {\n            alignStyle.left = x;\n        } else if (popupLR === \"l\") {\n            alignStyle.left = 0;\n        } else {\n            alignStyle.right = 0;\n        }\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        ref: arrowRef,\n        className: classnames__WEBPACK_IMPORTED_MODULE_0___default()(\"\".concat(prefixCls, \"-arrow\"), className),\n        style: alignStyle\n    }, content);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/trigger/es/Popup/Arrow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/trigger/es/Popup/Mask.js":
/*!*************************************************************!*\
  !*** ./node_modules/@rc-component/trigger/es/Popup/Mask.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Mask)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var rc_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-motion */ \"(ssr)/./node_modules/rc-motion/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Mask(props) {\n    var prefixCls = props.prefixCls, open = props.open, zIndex = props.zIndex, mask = props.mask, motion = props.motion;\n    if (!mask) {\n        return null;\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3__.createElement(rc_motion__WEBPACK_IMPORTED_MODULE_2__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, motion, {\n        motionAppear: true,\n        visible: open,\n        removeOnLeave: true\n    }), function(_ref) {\n        var className = _ref.className;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_3__.createElement(\"div\", {\n            style: {\n                zIndex: zIndex\n            },\n            className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(\"\".concat(prefixCls, \"-mask\"), className)\n        });\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/trigger/es/Popup/Mask.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/trigger/es/Popup/PopupContent.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@rc-component/trigger/es/Popup/PopupContent.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar PopupContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.memo(function(_ref) {\n    var children = _ref.children;\n    return children;\n}, function(_, next) {\n    return next.cache;\n});\nif (true) {\n    PopupContent.displayName = \"PopupContent\";\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PopupContent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJjLWNvbXBvbmVudC90cmlnZ2VyL2VzL1BvcHVwL1BvcHVwQ29udGVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0I7QUFDL0IsSUFBSUMsZUFBZSxXQUFXLEdBQUVELHVDQUFVLENBQUMsU0FBVUcsSUFBSTtJQUN2RCxJQUFJQyxXQUFXRCxLQUFLQyxRQUFRO0lBQzVCLE9BQU9BO0FBQ1QsR0FBRyxTQUFVQyxDQUFDLEVBQUVDLElBQUk7SUFDbEIsT0FBT0EsS0FBS0MsS0FBSztBQUNuQjtBQUNBLElBQUlDLElBQXlCLEVBQWM7SUFDekNQLGFBQWFRLFdBQVcsR0FBRztBQUM3QjtBQUNBLGlFQUFlUixZQUFZQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJ1dG9vLy4vbm9kZV9tb2R1bGVzL0ByYy1jb21wb25lbnQvdHJpZ2dlci9lcy9Qb3B1cC9Qb3B1cENvbnRlbnQuanM/NDdhNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG52YXIgUG9wdXBDb250ZW50ID0gLyojX19QVVJFX18qL1JlYWN0Lm1lbW8oZnVuY3Rpb24gKF9yZWYpIHtcbiAgdmFyIGNoaWxkcmVuID0gX3JlZi5jaGlsZHJlbjtcbiAgcmV0dXJuIGNoaWxkcmVuO1xufSwgZnVuY3Rpb24gKF8sIG5leHQpIHtcbiAgcmV0dXJuIG5leHQuY2FjaGU7XG59KTtcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIFBvcHVwQ29udGVudC5kaXNwbGF5TmFtZSA9ICdQb3B1cENvbnRlbnQnO1xufVxuZXhwb3J0IGRlZmF1bHQgUG9wdXBDb250ZW50OyJdLCJuYW1lcyI6WyJSZWFjdCIsIlBvcHVwQ29udGVudCIsIm1lbW8iLCJfcmVmIiwiY2hpbGRyZW4iLCJfIiwibmV4dCIsImNhY2hlIiwicHJvY2VzcyIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/trigger/es/Popup/PopupContent.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/trigger/es/Popup/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@rc-component/trigger/es/Popup/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var rc_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-motion */ \"(ssr)/./node_modules/rc-motion/es/index.js\");\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _Arrow__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./Arrow */ \"(ssr)/./node_modules/@rc-component/trigger/es/Popup/Arrow.js\");\n/* harmony import */ var _Mask__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./Mask */ \"(ssr)/./node_modules/@rc-component/trigger/es/Popup/Mask.js\");\n/* harmony import */ var _PopupContent__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./PopupContent */ \"(ssr)/./node_modules/@rc-component/trigger/es/Popup/PopupContent.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nvar Popup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_8__.forwardRef(function(props, ref) {\n    var popup = props.popup, className = props.className, prefixCls = props.prefixCls, style = props.style, target = props.target, _onVisibleChanged = props.onVisibleChanged, open = props.open, keepDom = props.keepDom, fresh = props.fresh, onClick = props.onClick, mask = props.mask, arrow = props.arrow, arrowPos = props.arrowPos, align = props.align, motion = props.motion, maskMotion = props.maskMotion, forceRender = props.forceRender, getPopupContainer = props.getPopupContainer, autoDestroy = props.autoDestroy, Portal = props.portal, zIndex = props.zIndex, onMouseEnter = props.onMouseEnter, onMouseLeave = props.onMouseLeave, onPointerEnter = props.onPointerEnter, ready = props.ready, offsetX = props.offsetX, offsetY = props.offsetY, offsetR = props.offsetR, offsetB = props.offsetB, onAlign = props.onAlign, onPrepare = props.onPrepare, stretch = props.stretch, targetWidth = props.targetWidth, targetHeight = props.targetHeight;\n    var childNode = typeof popup === \"function\" ? popup() : popup;\n    // We can not remove holder only when motion finished.\n    var isNodeVisible = open || keepDom;\n    // ======================= Container ========================\n    var getPopupContainerNeedParams = (getPopupContainer === null || getPopupContainer === void 0 ? void 0 : getPopupContainer.length) > 0;\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_8__.useState(!getPopupContainer || !getPopupContainerNeedParams), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(_React$useState, 2), show = _React$useState2[0], setShow = _React$useState2[1];\n    // Delay to show since `getPopupContainer` need target element\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(function() {\n        if (!show && getPopupContainerNeedParams && target) {\n            setShow(true);\n        }\n    }, [\n        show,\n        getPopupContainerNeedParams,\n        target\n    ]);\n    // ========================= Render =========================\n    if (!show) {\n        return null;\n    }\n    // >>>>> Offset\n    var AUTO = \"auto\";\n    var offsetStyle = {\n        left: \"-1000vw\",\n        top: \"-1000vh\",\n        right: AUTO,\n        bottom: AUTO\n    };\n    // Set align style\n    if (ready || !open) {\n        var _experimental;\n        var points = align.points;\n        var dynamicInset = align.dynamicInset || ((_experimental = align._experimental) === null || _experimental === void 0 ? void 0 : _experimental.dynamicInset);\n        var alignRight = dynamicInset && points[0][1] === \"r\";\n        var alignBottom = dynamicInset && points[0][0] === \"b\";\n        if (alignRight) {\n            offsetStyle.right = offsetR;\n            offsetStyle.left = AUTO;\n        } else {\n            offsetStyle.left = offsetX;\n            offsetStyle.right = AUTO;\n        }\n        if (alignBottom) {\n            offsetStyle.bottom = offsetB;\n            offsetStyle.top = AUTO;\n        } else {\n            offsetStyle.top = offsetY;\n            offsetStyle.bottom = AUTO;\n        }\n    }\n    // >>>>> Misc\n    var miscStyle = {};\n    if (stretch) {\n        if (stretch.includes(\"height\") && targetHeight) {\n            miscStyle.height = targetHeight;\n        } else if (stretch.includes(\"minHeight\") && targetHeight) {\n            miscStyle.minHeight = targetHeight;\n        }\n        if (stretch.includes(\"width\") && targetWidth) {\n            miscStyle.width = targetWidth;\n        } else if (stretch.includes(\"minWidth\") && targetWidth) {\n            miscStyle.minWidth = targetWidth;\n        }\n    }\n    if (!open) {\n        miscStyle.pointerEvents = \"none\";\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_8__.createElement(Portal, {\n        open: forceRender || isNodeVisible,\n        getContainer: getPopupContainer && function() {\n            return getPopupContainer(target);\n        },\n        autoDestroy: autoDestroy\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_8__.createElement(_Mask__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        prefixCls: prefixCls,\n        open: open,\n        zIndex: zIndex,\n        mask: mask,\n        motion: maskMotion\n    }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_8__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        onResize: onAlign,\n        disabled: !open\n    }, function(resizeObserverRef) {\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_8__.createElement(rc_motion__WEBPACK_IMPORTED_MODULE_4__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            motionAppear: true,\n            motionEnter: true,\n            motionLeave: true,\n            removeOnLeave: false,\n            forceRender: forceRender,\n            leavedClassName: \"\".concat(prefixCls, \"-hidden\")\n        }, motion, {\n            onAppearPrepare: onPrepare,\n            onEnterPrepare: onPrepare,\n            visible: open,\n            onVisibleChanged: function onVisibleChanged(nextVisible) {\n                var _motion$onVisibleChan;\n                motion === null || motion === void 0 || (_motion$onVisibleChan = motion.onVisibleChanged) === null || _motion$onVisibleChan === void 0 || _motion$onVisibleChan.call(motion, nextVisible);\n                _onVisibleChanged(nextVisible);\n            }\n        }), function(_ref, motionRef) {\n            var motionClassName = _ref.className, motionStyle = _ref.style;\n            var cls = classnames__WEBPACK_IMPORTED_MODULE_3___default()(prefixCls, motionClassName, className);\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_8__.createElement(\"div\", {\n                ref: (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_7__.composeRef)(resizeObserverRef, ref, motionRef),\n                className: cls,\n                style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n                    \"--arrow-x\": \"\".concat(arrowPos.x || 0, \"px\"),\n                    \"--arrow-y\": \"\".concat(arrowPos.y || 0, \"px\")\n                }, offsetStyle), miscStyle), motionStyle), {}, {\n                    boxSizing: \"border-box\",\n                    zIndex: zIndex\n                }, style),\n                onMouseEnter: onMouseEnter,\n                onMouseLeave: onMouseLeave,\n                onPointerEnter: onPointerEnter,\n                onClick: onClick\n            }, arrow && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_8__.createElement(_Arrow__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                prefixCls: prefixCls,\n                arrow: arrow,\n                arrowPos: arrowPos,\n                align: align\n            }), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_8__.createElement(_PopupContent__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                cache: !open && !fresh\n            }, childNode));\n        });\n    }));\n});\nif (true) {\n    Popup.displayName = \"Popup\";\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Popup);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/trigger/es/Popup/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/trigger/es/TriggerWrapper.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@rc-component/trigger/es/TriggerWrapper.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var rc_util_es_ref__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rc-util/es/ref */ \"(ssr)/./node_modules/rc-util/es/ref.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar TriggerWrapper = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(function(props, ref) {\n    var children = props.children, getTriggerDOMNode = props.getTriggerDOMNode;\n    var canUseRef = (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_0__.supportRef)(children);\n    // When use `getTriggerDOMNode`, we should do additional work to get the real dom\n    var setRef = react__WEBPACK_IMPORTED_MODULE_1__.useCallback(function(node) {\n        (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_0__.fillRef)(ref, getTriggerDOMNode ? getTriggerDOMNode(node) : node);\n    }, [\n        getTriggerDOMNode\n    ]);\n    var mergedRef = (0,rc_util_es_ref__WEBPACK_IMPORTED_MODULE_0__.useComposeRef)(setRef, children.ref);\n    return canUseRef ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.cloneElement(children, {\n        ref: mergedRef\n    }) : children;\n});\nif (true) {\n    TriggerWrapper.displayName = \"TriggerWrapper\";\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TriggerWrapper);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/trigger/es/TriggerWrapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/trigger/es/context.js":
/*!**********************************************************!*\
  !*** ./node_modules/@rc-component/trigger/es/context.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar TriggerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TriggerContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJjLWNvbXBvbmVudC90cmlnZ2VyL2VzL2NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStCO0FBQy9CLElBQUlDLGlCQUFpQixXQUFXLEdBQUVELGdEQUFtQixDQUFDO0FBQ3RELGlFQUFlQyxjQUFjQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZHJ1dG9vLy4vbm9kZV9tb2R1bGVzL0ByYy1jb21wb25lbnQvdHJpZ2dlci9lcy9jb250ZXh0LmpzPzkwMGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xudmFyIFRyaWdnZXJDb250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQobnVsbCk7XG5leHBvcnQgZGVmYXVsdCBUcmlnZ2VyQ29udGV4dDsiXSwibmFtZXMiOlsiUmVhY3QiLCJUcmlnZ2VyQ29udGV4dCIsImNyZWF0ZUNvbnRleHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/trigger/es/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/trigger/es/hooks/useAction.js":
/*!******************************************************************!*\
  !*** ./node_modules/@rc-component/trigger/es/hooks/useAction.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useAction)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction toArray(val) {\n    return val ? Array.isArray(val) ? val : [\n        val\n    ] : [];\n}\nfunction useAction(mobile, action, showAction, hideAction) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(function() {\n        var mergedShowAction = toArray(showAction !== null && showAction !== void 0 ? showAction : action);\n        var mergedHideAction = toArray(hideAction !== null && hideAction !== void 0 ? hideAction : action);\n        var showActionSet = new Set(mergedShowAction);\n        var hideActionSet = new Set(mergedHideAction);\n        if (mobile) {\n            if (showActionSet.has(\"hover\")) {\n                showActionSet.delete(\"hover\");\n                showActionSet.add(\"click\");\n            }\n            if (hideActionSet.has(\"hover\")) {\n                hideActionSet.delete(\"hover\");\n                hideActionSet.add(\"click\");\n            }\n        }\n        return [\n            showActionSet,\n            hideActionSet\n        ];\n    }, [\n        mobile,\n        action,\n        showAction,\n        hideAction\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/trigger/es/hooks/useAction.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/trigger/es/hooks/useAlign.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@rc-component/trigger/es/hooks/useAlign.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useAlign)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/Dom/findDOMNode */ \"(ssr)/./node_modules/rc-util/es/Dom/findDOMNode.js\");\n/* harmony import */ var rc_util_es_Dom_isVisible__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util/es/Dom/isVisible */ \"(ssr)/./node_modules/rc-util/es/Dom/isVisible.js\");\n/* harmony import */ var rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rc-util/es/hooks/useEvent */ \"(ssr)/./node_modules/rc-util/es/hooks/useEvent.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../util */ \"(ssr)/./node_modules/@rc-component/trigger/es/util.js\");\n\n\n\n\n\n\n\n\nfunction getUnitOffset(size) {\n    var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var offsetStr = \"\".concat(offset);\n    var cells = offsetStr.match(/^(.*)\\%$/);\n    if (cells) {\n        return size * (parseFloat(cells[1]) / 100);\n    }\n    return parseFloat(offsetStr);\n}\nfunction getNumberOffset(rect, offset) {\n    var _ref = offset || [], _ref2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref, 2), offsetX = _ref2[0], offsetY = _ref2[1];\n    return [\n        getUnitOffset(rect.width, offsetX),\n        getUnitOffset(rect.height, offsetY)\n    ];\n}\nfunction splitPoints() {\n    var points = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n    return [\n        points[0],\n        points[1]\n    ];\n}\nfunction getAlignPoint(rect, points) {\n    var topBottom = points[0];\n    var leftRight = points[1];\n    var x;\n    var y;\n    // Top & Bottom\n    if (topBottom === \"t\") {\n        y = rect.y;\n    } else if (topBottom === \"b\") {\n        y = rect.y + rect.height;\n    } else {\n        y = rect.y + rect.height / 2;\n    }\n    // Left & Right\n    if (leftRight === \"l\") {\n        x = rect.x;\n    } else if (leftRight === \"r\") {\n        x = rect.x + rect.width;\n    } else {\n        x = rect.x + rect.width / 2;\n    }\n    return {\n        x: x,\n        y: y\n    };\n}\nfunction reversePoints(points, index) {\n    var reverseMap = {\n        t: \"b\",\n        b: \"t\",\n        l: \"r\",\n        r: \"l\"\n    };\n    return points.map(function(point, i) {\n        if (i === index) {\n            return reverseMap[point] || \"c\";\n        }\n        return point;\n    }).join(\"\");\n}\nfunction useAlign(open, popupEle, target, placement, builtinPlacements, popupAlign, onPopupAlign) {\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_6__.useState({\n        ready: false,\n        offsetX: 0,\n        offsetY: 0,\n        offsetR: 0,\n        offsetB: 0,\n        arrowX: 0,\n        arrowY: 0,\n        scaleX: 1,\n        scaleY: 1,\n        align: builtinPlacements[placement] || {}\n    }), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState, 2), offsetInfo = _React$useState2[0], setOffsetInfo = _React$useState2[1];\n    var alignCountRef = react__WEBPACK_IMPORTED_MODULE_6__.useRef(0);\n    var scrollerList = react__WEBPACK_IMPORTED_MODULE_6__.useMemo(function() {\n        if (!popupEle) {\n            return [];\n        }\n        return (0,_util__WEBPACK_IMPORTED_MODULE_7__.collectScroller)(popupEle);\n    }, [\n        popupEle\n    ]);\n    // ========================= Flip ==========================\n    // We will memo flip info.\n    // If size change to make flip, it will memo the flip info and use it in next align.\n    var prevFlipRef = react__WEBPACK_IMPORTED_MODULE_6__.useRef({});\n    var resetFlipCache = function resetFlipCache() {\n        prevFlipRef.current = {};\n    };\n    if (!open) {\n        resetFlipCache();\n    }\n    // ========================= Align =========================\n    var onAlign = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function() {\n        if (popupEle && target && open) {\n            var _popupElement$parentE, _popupElement$parentE2;\n            var popupElement = popupEle;\n            var doc = popupElement.ownerDocument;\n            var win = (0,_util__WEBPACK_IMPORTED_MODULE_7__.getWin)(popupElement);\n            var _win$getComputedStyle = win.getComputedStyle(popupElement), width = _win$getComputedStyle.width, height = _win$getComputedStyle.height, popupPosition = _win$getComputedStyle.position;\n            var originLeft = popupElement.style.left;\n            var originTop = popupElement.style.top;\n            var originRight = popupElement.style.right;\n            var originBottom = popupElement.style.bottom;\n            var originOverflow = popupElement.style.overflow;\n            // Placement\n            var placementInfo = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, builtinPlacements[placement]), popupAlign);\n            // placeholder element\n            var placeholderElement = doc.createElement(\"div\");\n            (_popupElement$parentE = popupElement.parentElement) === null || _popupElement$parentE === void 0 || _popupElement$parentE.appendChild(placeholderElement);\n            placeholderElement.style.left = \"\".concat(popupElement.offsetLeft, \"px\");\n            placeholderElement.style.top = \"\".concat(popupElement.offsetTop, \"px\");\n            placeholderElement.style.position = popupPosition;\n            placeholderElement.style.height = \"\".concat(popupElement.offsetHeight, \"px\");\n            placeholderElement.style.width = \"\".concat(popupElement.offsetWidth, \"px\");\n            // Reset first\n            popupElement.style.left = \"0\";\n            popupElement.style.top = \"0\";\n            popupElement.style.right = \"auto\";\n            popupElement.style.bottom = \"auto\";\n            popupElement.style.overflow = \"hidden\";\n            // Calculate align style, we should consider `transform` case\n            var targetRect;\n            if (Array.isArray(target)) {\n                targetRect = {\n                    x: target[0],\n                    y: target[1],\n                    width: 0,\n                    height: 0\n                };\n            } else {\n                var rect = target.getBoundingClientRect();\n                targetRect = {\n                    x: rect.x,\n                    y: rect.y,\n                    width: rect.width,\n                    height: rect.height\n                };\n            }\n            var popupRect = popupElement.getBoundingClientRect();\n            var _doc$documentElement = doc.documentElement, clientWidth = _doc$documentElement.clientWidth, clientHeight = _doc$documentElement.clientHeight, scrollWidth = _doc$documentElement.scrollWidth, scrollHeight = _doc$documentElement.scrollHeight, scrollTop = _doc$documentElement.scrollTop, scrollLeft = _doc$documentElement.scrollLeft;\n            var popupHeight = popupRect.height;\n            var popupWidth = popupRect.width;\n            var targetHeight = targetRect.height;\n            var targetWidth = targetRect.width;\n            // Get bounding of visible area\n            var visibleRegion = {\n                left: 0,\n                top: 0,\n                right: clientWidth,\n                bottom: clientHeight\n            };\n            var scrollRegion = {\n                left: -scrollLeft,\n                top: -scrollTop,\n                right: scrollWidth - scrollLeft,\n                bottom: scrollHeight - scrollTop\n            };\n            var htmlRegion = placementInfo.htmlRegion;\n            var VISIBLE = \"visible\";\n            var VISIBLE_FIRST = \"visibleFirst\";\n            if (htmlRegion !== \"scroll\" && htmlRegion !== VISIBLE_FIRST) {\n                htmlRegion = VISIBLE;\n            }\n            var isVisibleFirst = htmlRegion === VISIBLE_FIRST;\n            var scrollRegionArea = (0,_util__WEBPACK_IMPORTED_MODULE_7__.getVisibleArea)(scrollRegion, scrollerList);\n            var visibleRegionArea = (0,_util__WEBPACK_IMPORTED_MODULE_7__.getVisibleArea)(visibleRegion, scrollerList);\n            var visibleArea = htmlRegion === VISIBLE ? visibleRegionArea : scrollRegionArea;\n            // When set to `visibleFirst`,\n            // the check `adjust` logic will use `visibleRegion` for check first.\n            var adjustCheckVisibleArea = isVisibleFirst ? visibleRegionArea : visibleArea;\n            // Record right & bottom align data\n            popupElement.style.left = \"auto\";\n            popupElement.style.top = \"auto\";\n            popupElement.style.right = \"0\";\n            popupElement.style.bottom = \"0\";\n            var popupMirrorRect = popupElement.getBoundingClientRect();\n            // Reset back\n            popupElement.style.left = originLeft;\n            popupElement.style.top = originTop;\n            popupElement.style.right = originRight;\n            popupElement.style.bottom = originBottom;\n            popupElement.style.overflow = originOverflow;\n            (_popupElement$parentE2 = popupElement.parentElement) === null || _popupElement$parentE2 === void 0 || _popupElement$parentE2.removeChild(placeholderElement);\n            // Calculate scale\n            var _scaleX = (0,_util__WEBPACK_IMPORTED_MODULE_7__.toNum)(Math.round(popupWidth / parseFloat(width) * 1000) / 1000);\n            var _scaleY = (0,_util__WEBPACK_IMPORTED_MODULE_7__.toNum)(Math.round(popupHeight / parseFloat(height) * 1000) / 1000);\n            // No need to align since it's not visible in view\n            if (_scaleX === 0 || _scaleY === 0 || (0,rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_2__.isDOM)(target) && !(0,rc_util_es_Dom_isVisible__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(target)) {\n                return;\n            }\n            // Offset\n            var offset = placementInfo.offset, targetOffset = placementInfo.targetOffset;\n            var _getNumberOffset = getNumberOffset(popupRect, offset), _getNumberOffset2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_getNumberOffset, 2), popupOffsetX = _getNumberOffset2[0], popupOffsetY = _getNumberOffset2[1];\n            var _getNumberOffset3 = getNumberOffset(targetRect, targetOffset), _getNumberOffset4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_getNumberOffset3, 2), targetOffsetX = _getNumberOffset4[0], targetOffsetY = _getNumberOffset4[1];\n            targetRect.x -= targetOffsetX;\n            targetRect.y -= targetOffsetY;\n            // Points\n            var _ref3 = placementInfo.points || [], _ref4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref3, 2), popupPoint = _ref4[0], targetPoint = _ref4[1];\n            var targetPoints = splitPoints(targetPoint);\n            var popupPoints = splitPoints(popupPoint);\n            var targetAlignPoint = getAlignPoint(targetRect, targetPoints);\n            var popupAlignPoint = getAlignPoint(popupRect, popupPoints);\n            // Real align info may not same as origin one\n            var nextAlignInfo = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, placementInfo);\n            // Next Offset\n            var nextOffsetX = targetAlignPoint.x - popupAlignPoint.x + popupOffsetX;\n            var nextOffsetY = targetAlignPoint.y - popupAlignPoint.y + popupOffsetY;\n            // ============== Intersection ===============\n            // Get area by position. Used for check if flip area is better\n            function getIntersectionVisibleArea(offsetX, offsetY) {\n                var area = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : visibleArea;\n                var l = popupRect.x + offsetX;\n                var t = popupRect.y + offsetY;\n                var r = l + popupWidth;\n                var b = t + popupHeight;\n                var visibleL = Math.max(l, area.left);\n                var visibleT = Math.max(t, area.top);\n                var visibleR = Math.min(r, area.right);\n                var visibleB = Math.min(b, area.bottom);\n                return Math.max(0, (visibleR - visibleL) * (visibleB - visibleT));\n            }\n            var originIntersectionVisibleArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY);\n            // As `visibleFirst`, we prepare this for check\n            var originIntersectionRecommendArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY, visibleRegionArea);\n            // ========================== Overflow ===========================\n            var targetAlignPointTL = getAlignPoint(targetRect, [\n                \"t\",\n                \"l\"\n            ]);\n            var popupAlignPointTL = getAlignPoint(popupRect, [\n                \"t\",\n                \"l\"\n            ]);\n            var targetAlignPointBR = getAlignPoint(targetRect, [\n                \"b\",\n                \"r\"\n            ]);\n            var popupAlignPointBR = getAlignPoint(popupRect, [\n                \"b\",\n                \"r\"\n            ]);\n            var overflow = placementInfo.overflow || {};\n            var adjustX = overflow.adjustX, adjustY = overflow.adjustY, shiftX = overflow.shiftX, shiftY = overflow.shiftY;\n            var supportAdjust = function supportAdjust(val) {\n                if (typeof val === \"boolean\") {\n                    return val;\n                }\n                return val >= 0;\n            };\n            // Prepare position\n            var nextPopupY;\n            var nextPopupBottom;\n            var nextPopupX;\n            var nextPopupRight;\n            function syncNextPopupPosition() {\n                nextPopupY = popupRect.y + nextOffsetY;\n                nextPopupBottom = nextPopupY + popupHeight;\n                nextPopupX = popupRect.x + nextOffsetX;\n                nextPopupRight = nextPopupX + popupWidth;\n            }\n            syncNextPopupPosition();\n            // >>>>>>>>>> Top & Bottom\n            var needAdjustY = supportAdjust(adjustY);\n            var sameTB = popupPoints[0] === targetPoints[0];\n            // Bottom to Top\n            if (needAdjustY && popupPoints[0] === \"t\" && (nextPopupBottom > adjustCheckVisibleArea.bottom || prevFlipRef.current.bt)) {\n                var tmpNextOffsetY = nextOffsetY;\n                if (sameTB) {\n                    tmpNextOffsetY -= popupHeight - targetHeight;\n                } else {\n                    tmpNextOffsetY = targetAlignPointTL.y - popupAlignPointBR.y - popupOffsetY;\n                }\n                var newVisibleArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY);\n                var newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY, visibleRegionArea);\n                if (// Of course use larger one\n                newVisibleArea > originIntersectionVisibleArea || newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst || // Choose recommend one\n                newVisibleRecommendArea >= originIntersectionRecommendArea)) {\n                    prevFlipRef.current.bt = true;\n                    nextOffsetY = tmpNextOffsetY;\n                    popupOffsetY = -popupOffsetY;\n                    nextAlignInfo.points = [\n                        reversePoints(popupPoints, 0),\n                        reversePoints(targetPoints, 0)\n                    ];\n                } else {\n                    prevFlipRef.current.bt = false;\n                }\n            }\n            // Top to Bottom\n            if (needAdjustY && popupPoints[0] === \"b\" && (nextPopupY < adjustCheckVisibleArea.top || prevFlipRef.current.tb)) {\n                var _tmpNextOffsetY = nextOffsetY;\n                if (sameTB) {\n                    _tmpNextOffsetY += popupHeight - targetHeight;\n                } else {\n                    _tmpNextOffsetY = targetAlignPointBR.y - popupAlignPointTL.y - popupOffsetY;\n                }\n                var _newVisibleArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY);\n                var _newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY, visibleRegionArea);\n                if (// Of course use larger one\n                _newVisibleArea > originIntersectionVisibleArea || _newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst || // Choose recommend one\n                _newVisibleRecommendArea >= originIntersectionRecommendArea)) {\n                    prevFlipRef.current.tb = true;\n                    nextOffsetY = _tmpNextOffsetY;\n                    popupOffsetY = -popupOffsetY;\n                    nextAlignInfo.points = [\n                        reversePoints(popupPoints, 0),\n                        reversePoints(targetPoints, 0)\n                    ];\n                } else {\n                    prevFlipRef.current.tb = false;\n                }\n            }\n            // >>>>>>>>>> Left & Right\n            var needAdjustX = supportAdjust(adjustX);\n            // >>>>> Flip\n            var sameLR = popupPoints[1] === targetPoints[1];\n            // Right to Left\n            if (needAdjustX && popupPoints[1] === \"l\" && (nextPopupRight > adjustCheckVisibleArea.right || prevFlipRef.current.rl)) {\n                var tmpNextOffsetX = nextOffsetX;\n                if (sameLR) {\n                    tmpNextOffsetX -= popupWidth - targetWidth;\n                } else {\n                    tmpNextOffsetX = targetAlignPointTL.x - popupAlignPointBR.x - popupOffsetX;\n                }\n                var _newVisibleArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY);\n                var _newVisibleRecommendArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY, visibleRegionArea);\n                if (// Of course use larger one\n                _newVisibleArea2 > originIntersectionVisibleArea || _newVisibleArea2 === originIntersectionVisibleArea && (!isVisibleFirst || // Choose recommend one\n                _newVisibleRecommendArea2 >= originIntersectionRecommendArea)) {\n                    prevFlipRef.current.rl = true;\n                    nextOffsetX = tmpNextOffsetX;\n                    popupOffsetX = -popupOffsetX;\n                    nextAlignInfo.points = [\n                        reversePoints(popupPoints, 1),\n                        reversePoints(targetPoints, 1)\n                    ];\n                } else {\n                    prevFlipRef.current.rl = false;\n                }\n            }\n            // Left to Right\n            if (needAdjustX && popupPoints[1] === \"r\" && (nextPopupX < adjustCheckVisibleArea.left || prevFlipRef.current.lr)) {\n                var _tmpNextOffsetX = nextOffsetX;\n                if (sameLR) {\n                    _tmpNextOffsetX += popupWidth - targetWidth;\n                } else {\n                    _tmpNextOffsetX = targetAlignPointBR.x - popupAlignPointTL.x - popupOffsetX;\n                }\n                var _newVisibleArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY);\n                var _newVisibleRecommendArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY, visibleRegionArea);\n                if (// Of course use larger one\n                _newVisibleArea3 > originIntersectionVisibleArea || _newVisibleArea3 === originIntersectionVisibleArea && (!isVisibleFirst || // Choose recommend one\n                _newVisibleRecommendArea3 >= originIntersectionRecommendArea)) {\n                    prevFlipRef.current.lr = true;\n                    nextOffsetX = _tmpNextOffsetX;\n                    popupOffsetX = -popupOffsetX;\n                    nextAlignInfo.points = [\n                        reversePoints(popupPoints, 1),\n                        reversePoints(targetPoints, 1)\n                    ];\n                } else {\n                    prevFlipRef.current.lr = false;\n                }\n            }\n            // ============================ Shift ============================\n            syncNextPopupPosition();\n            var numShiftX = shiftX === true ? 0 : shiftX;\n            if (typeof numShiftX === \"number\") {\n                // Left\n                if (nextPopupX < visibleRegionArea.left) {\n                    nextOffsetX -= nextPopupX - visibleRegionArea.left - popupOffsetX;\n                    if (targetRect.x + targetWidth < visibleRegionArea.left + numShiftX) {\n                        nextOffsetX += targetRect.x - visibleRegionArea.left + targetWidth - numShiftX;\n                    }\n                }\n                // Right\n                if (nextPopupRight > visibleRegionArea.right) {\n                    nextOffsetX -= nextPopupRight - visibleRegionArea.right - popupOffsetX;\n                    if (targetRect.x > visibleRegionArea.right - numShiftX) {\n                        nextOffsetX += targetRect.x - visibleRegionArea.right + numShiftX;\n                    }\n                }\n            }\n            var numShiftY = shiftY === true ? 0 : shiftY;\n            if (typeof numShiftY === \"number\") {\n                // Top\n                if (nextPopupY < visibleRegionArea.top) {\n                    nextOffsetY -= nextPopupY - visibleRegionArea.top - popupOffsetY;\n                    // When target if far away from visible area\n                    // Stop shift\n                    if (targetRect.y + targetHeight < visibleRegionArea.top + numShiftY) {\n                        nextOffsetY += targetRect.y - visibleRegionArea.top + targetHeight - numShiftY;\n                    }\n                }\n                // Bottom\n                if (nextPopupBottom > visibleRegionArea.bottom) {\n                    nextOffsetY -= nextPopupBottom - visibleRegionArea.bottom - popupOffsetY;\n                    if (targetRect.y > visibleRegionArea.bottom - numShiftY) {\n                        nextOffsetY += targetRect.y - visibleRegionArea.bottom + numShiftY;\n                    }\n                }\n            }\n            // ============================ Arrow ============================\n            // Arrow center align\n            var popupLeft = popupRect.x + nextOffsetX;\n            var popupRight = popupLeft + popupWidth;\n            var popupTop = popupRect.y + nextOffsetY;\n            var popupBottom = popupTop + popupHeight;\n            var targetLeft = targetRect.x;\n            var targetRight = targetLeft + targetWidth;\n            var targetTop = targetRect.y;\n            var targetBottom = targetTop + targetHeight;\n            var maxLeft = Math.max(popupLeft, targetLeft);\n            var minRight = Math.min(popupRight, targetRight);\n            var xCenter = (maxLeft + minRight) / 2;\n            var nextArrowX = xCenter - popupLeft;\n            var maxTop = Math.max(popupTop, targetTop);\n            var minBottom = Math.min(popupBottom, targetBottom);\n            var yCenter = (maxTop + minBottom) / 2;\n            var nextArrowY = yCenter - popupTop;\n            onPopupAlign === null || onPopupAlign === void 0 || onPopupAlign(popupEle, nextAlignInfo);\n            // Additional calculate right & bottom position\n            var offsetX4Right = popupMirrorRect.right - popupRect.x - (nextOffsetX + popupRect.width);\n            var offsetY4Bottom = popupMirrorRect.bottom - popupRect.y - (nextOffsetY + popupRect.height);\n            setOffsetInfo({\n                ready: true,\n                offsetX: nextOffsetX / _scaleX,\n                offsetY: nextOffsetY / _scaleY,\n                offsetR: offsetX4Right / _scaleX,\n                offsetB: offsetY4Bottom / _scaleY,\n                arrowX: nextArrowX / _scaleX,\n                arrowY: nextArrowY / _scaleY,\n                scaleX: _scaleX,\n                scaleY: _scaleY,\n                align: nextAlignInfo\n            });\n        }\n    });\n    var triggerAlign = function triggerAlign() {\n        alignCountRef.current += 1;\n        var id = alignCountRef.current;\n        // Merge all align requirement into one frame\n        Promise.resolve().then(function() {\n            if (alignCountRef.current === id) {\n                onAlign();\n            }\n        });\n    };\n    // Reset ready status when placement & open changed\n    var resetReady = function resetReady() {\n        setOffsetInfo(function(ori) {\n            return (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, ori), {}, {\n                ready: false\n            });\n        });\n    };\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(resetReady, [\n        placement\n    ]);\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(function() {\n        if (!open) {\n            resetReady();\n        }\n    }, [\n        open\n    ]);\n    return [\n        offsetInfo.ready,\n        offsetInfo.offsetX,\n        offsetInfo.offsetY,\n        offsetInfo.offsetR,\n        offsetInfo.offsetB,\n        offsetInfo.arrowX,\n        offsetInfo.arrowY,\n        offsetInfo.scaleX,\n        offsetInfo.scaleY,\n        offsetInfo.align,\n        triggerAlign\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/trigger/es/hooks/useAlign.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/trigger/es/hooks/useWatch.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@rc-component/trigger/es/hooks/useWatch.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../util */ \"(ssr)/./node_modules/@rc-component/trigger/es/util.js\");\n\n\n\nfunction useWatch(open, target, popup, onAlign, onScroll) {\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function() {\n        if (open && target && popup) {\n            var targetElement = target;\n            var popupElement = popup;\n            var targetScrollList = (0,_util__WEBPACK_IMPORTED_MODULE_2__.collectScroller)(targetElement);\n            var popupScrollList = (0,_util__WEBPACK_IMPORTED_MODULE_2__.collectScroller)(popupElement);\n            var win = (0,_util__WEBPACK_IMPORTED_MODULE_2__.getWin)(popupElement);\n            var mergedList = new Set([\n                win\n            ].concat((0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(targetScrollList), (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(popupScrollList)));\n            function notifyScroll() {\n                onAlign();\n                onScroll();\n            }\n            mergedList.forEach(function(scroller) {\n                scroller.addEventListener(\"scroll\", notifyScroll, {\n                    passive: true\n                });\n            });\n            win.addEventListener(\"resize\", notifyScroll, {\n                passive: true\n            });\n            // First time always do align\n            onAlign();\n            return function() {\n                mergedList.forEach(function(scroller) {\n                    scroller.removeEventListener(\"scroll\", notifyScroll);\n                    win.removeEventListener(\"resize\", notifyScroll);\n                });\n            };\n        }\n    }, [\n        open,\n        target,\n        popup\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/trigger/es/hooks/useWatch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/trigger/es/hooks/useWinClick.js":
/*!********************************************************************!*\
  !*** ./node_modules/@rc-component/trigger/es/hooks/useWinClick.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useWinClick)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var rc_util_es_warning__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rc-util/es/warning */ \"(ssr)/./node_modules/rc-util/es/warning.js\");\n/* harmony import */ var rc_util_es_Dom_shadow__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rc-util/es/Dom/shadow */ \"(ssr)/./node_modules/rc-util/es/Dom/shadow.js\");\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../util */ \"(ssr)/./node_modules/@rc-component/trigger/es/util.js\");\n\n\n\n\n\n\nfunction useWinClick(open, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen) {\n    var openRef = react__WEBPACK_IMPORTED_MODULE_4__.useRef(open);\n    // Window click to hide should be lock to avoid trigger lock immediately\n    var lockRef = react__WEBPACK_IMPORTED_MODULE_4__.useRef(false);\n    if (openRef.current !== open) {\n        lockRef.current = true;\n        openRef.current = open;\n    }\n    react__WEBPACK_IMPORTED_MODULE_4__.useEffect(function() {\n        var id = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function() {\n            lockRef.current = false;\n        });\n        return function() {\n            rc_util_es_raf__WEBPACK_IMPORTED_MODULE_3__[\"default\"].cancel(id);\n        };\n    }, [\n        open\n    ]);\n    // Click to hide is special action since click popup element should not hide\n    react__WEBPACK_IMPORTED_MODULE_4__.useEffect(function() {\n        if (clickToHide && popupEle && (!mask || maskClosable)) {\n            var genClickEvents = function genClickEvents() {\n                var clickInside = false;\n                // User may mouseDown inside and drag out of popup and mouse up\n                // Record here to prevent close\n                var onWindowMouseDown = function onWindowMouseDown(_ref) {\n                    var target = _ref.target;\n                    clickInside = inPopupOrChild(target);\n                };\n                var onWindowClick = function onWindowClick(_ref2) {\n                    var target = _ref2.target;\n                    if (!lockRef.current && openRef.current && !clickInside && !inPopupOrChild(target)) {\n                        triggerOpen(false);\n                    }\n                };\n                return [\n                    onWindowMouseDown,\n                    onWindowClick\n                ];\n            };\n            // Events\n            var _genClickEvents = genClickEvents(), _genClickEvents2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_genClickEvents, 2), onWinMouseDown = _genClickEvents2[0], onWinClick = _genClickEvents2[1];\n            var _genClickEvents3 = genClickEvents(), _genClickEvents4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_genClickEvents3, 2), onShadowMouseDown = _genClickEvents4[0], onShadowClick = _genClickEvents4[1];\n            var win = (0,_util__WEBPACK_IMPORTED_MODULE_5__.getWin)(popupEle);\n            win.addEventListener(\"mousedown\", onWinMouseDown, true);\n            win.addEventListener(\"click\", onWinClick, true);\n            win.addEventListener(\"contextmenu\", onWinClick, true);\n            // shadow root\n            var targetShadowRoot = (0,rc_util_es_Dom_shadow__WEBPACK_IMPORTED_MODULE_2__.getShadowRoot)(targetEle);\n            if (targetShadowRoot) {\n                targetShadowRoot.addEventListener(\"mousedown\", onShadowMouseDown, true);\n                targetShadowRoot.addEventListener(\"click\", onShadowClick, true);\n                targetShadowRoot.addEventListener(\"contextmenu\", onShadowClick, true);\n            }\n            // Warning if target and popup not in same root\n            if (true) {\n                var _targetEle$getRootNod, _popupEle$getRootNode;\n                var targetRoot = targetEle === null || targetEle === void 0 || (_targetEle$getRootNod = targetEle.getRootNode) === null || _targetEle$getRootNod === void 0 ? void 0 : _targetEle$getRootNod.call(targetEle);\n                var popupRoot = (_popupEle$getRootNode = popupEle.getRootNode) === null || _popupEle$getRootNode === void 0 ? void 0 : _popupEle$getRootNode.call(popupEle);\n                (0,rc_util_es_warning__WEBPACK_IMPORTED_MODULE_1__.warning)(targetRoot === popupRoot, \"trigger element and popup element should in same shadow root.\");\n            }\n            return function() {\n                win.removeEventListener(\"mousedown\", onWinMouseDown, true);\n                win.removeEventListener(\"click\", onWinClick, true);\n                win.removeEventListener(\"contextmenu\", onWinClick, true);\n                if (targetShadowRoot) {\n                    targetShadowRoot.removeEventListener(\"mousedown\", onShadowMouseDown, true);\n                    targetShadowRoot.removeEventListener(\"click\", onShadowClick, true);\n                    targetShadowRoot.removeEventListener(\"contextmenu\", onShadowClick, true);\n                }\n            };\n        }\n    }, [\n        clickToHide,\n        targetEle,\n        popupEle,\n        mask,\n        maskClosable\n    ]);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/trigger/es/hooks/useWinClick.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/trigger/es/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@rc-component/trigger/es/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   generateTrigger: () => (/* binding */ generateTrigger)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var _rc_component_portal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @rc-component/portal */ \"(ssr)/./node_modules/@rc-component/portal/es/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rc-util/es/Dom/findDOMNode */ \"(ssr)/./node_modules/rc-util/es/Dom/findDOMNode.js\");\n/* harmony import */ var rc_util_es_Dom_shadow__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-util/es/Dom/shadow */ \"(ssr)/./node_modules/rc-util/es/Dom/shadow.js\");\n/* harmony import */ var rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/hooks/useEvent */ \"(ssr)/./node_modules/rc-util/es/hooks/useEvent.js\");\n/* harmony import */ var rc_util_es_hooks_useId__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-util/es/hooks/useId */ \"(ssr)/./node_modules/rc-util/es/hooks/useId.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var rc_util_es_isMobile__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! rc-util/es/isMobile */ \"(ssr)/./node_modules/rc-util/es/isMobile.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _Popup__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./Popup */ \"(ssr)/./node_modules/@rc-component/trigger/es/Popup/index.js\");\n/* harmony import */ var _TriggerWrapper__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./TriggerWrapper */ \"(ssr)/./node_modules/@rc-component/trigger/es/TriggerWrapper.js\");\n/* harmony import */ var _context__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./context */ \"(ssr)/./node_modules/@rc-component/trigger/es/context.js\");\n/* harmony import */ var _hooks_useAction__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./hooks/useAction */ \"(ssr)/./node_modules/@rc-component/trigger/es/hooks/useAction.js\");\n/* harmony import */ var _hooks_useAlign__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./hooks/useAlign */ \"(ssr)/./node_modules/@rc-component/trigger/es/hooks/useAlign.js\");\n/* harmony import */ var _hooks_useWatch__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./hooks/useWatch */ \"(ssr)/./node_modules/@rc-component/trigger/es/hooks/useWatch.js\");\n/* harmony import */ var _hooks_useWinClick__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./hooks/useWinClick */ \"(ssr)/./node_modules/@rc-component/trigger/es/hooks/useWinClick.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/@rc-component/trigger/es/util.js\");\n\n\n\nvar _excluded = [\n    \"prefixCls\",\n    \"children\",\n    \"action\",\n    \"showAction\",\n    \"hideAction\",\n    \"popupVisible\",\n    \"defaultPopupVisible\",\n    \"onPopupVisibleChange\",\n    \"afterPopupVisibleChange\",\n    \"mouseEnterDelay\",\n    \"mouseLeaveDelay\",\n    \"focusDelay\",\n    \"blurDelay\",\n    \"mask\",\n    \"maskClosable\",\n    \"getPopupContainer\",\n    \"forceRender\",\n    \"autoDestroy\",\n    \"destroyPopupOnHide\",\n    \"popup\",\n    \"popupClassName\",\n    \"popupStyle\",\n    \"popupPlacement\",\n    \"builtinPlacements\",\n    \"popupAlign\",\n    \"zIndex\",\n    \"stretch\",\n    \"getPopupClassNameFromAlign\",\n    \"fresh\",\n    \"alignPoint\",\n    \"onPopupClick\",\n    \"onPopupAlign\",\n    \"arrow\",\n    \"popupMotion\",\n    \"maskMotion\",\n    \"popupTransitionName\",\n    \"popupAnimation\",\n    \"maskTransitionName\",\n    \"maskAnimation\",\n    \"className\",\n    \"getTriggerDOMNode\"\n];\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Removed Props List\n// Seems this can be auto\n// getDocument?: (element?: HTMLElement) => Document;\n// New version will not wrap popup with `rc-trigger-popup-content` when multiple children\nfunction generateTrigger() {\n    var PortalComponent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : _rc_component_portal__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n    var Trigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_12__.forwardRef(function(props, ref) {\n        var _props$prefixCls = props.prefixCls, prefixCls = _props$prefixCls === void 0 ? \"rc-trigger-popup\" : _props$prefixCls, children = props.children, _props$action = props.action, action = _props$action === void 0 ? \"hover\" : _props$action, showAction = props.showAction, hideAction = props.hideAction, popupVisible = props.popupVisible, defaultPopupVisible = props.defaultPopupVisible, onPopupVisibleChange = props.onPopupVisibleChange, afterPopupVisibleChange = props.afterPopupVisibleChange, mouseEnterDelay = props.mouseEnterDelay, _props$mouseLeaveDela = props.mouseLeaveDelay, mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela, focusDelay = props.focusDelay, blurDelay = props.blurDelay, mask = props.mask, _props$maskClosable = props.maskClosable, maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable, getPopupContainer = props.getPopupContainer, forceRender = props.forceRender, autoDestroy = props.autoDestroy, destroyPopupOnHide = props.destroyPopupOnHide, popup = props.popup, popupClassName = props.popupClassName, popupStyle = props.popupStyle, popupPlacement = props.popupPlacement, _props$builtinPlaceme = props.builtinPlacements, builtinPlacements = _props$builtinPlaceme === void 0 ? {} : _props$builtinPlaceme, popupAlign = props.popupAlign, zIndex = props.zIndex, stretch = props.stretch, getPopupClassNameFromAlign = props.getPopupClassNameFromAlign, fresh = props.fresh, alignPoint = props.alignPoint, onPopupClick = props.onPopupClick, onPopupAlign = props.onPopupAlign, arrow = props.arrow, popupMotion = props.popupMotion, maskMotion = props.maskMotion, popupTransitionName = props.popupTransitionName, popupAnimation = props.popupAnimation, maskTransitionName = props.maskTransitionName, maskAnimation = props.maskAnimation, className = props.className, getTriggerDOMNode = props.getTriggerDOMNode, restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props, _excluded);\n        var mergedAutoDestroy = autoDestroy || destroyPopupOnHide || false;\n        // =========================== Mobile ===========================\n        var _React$useState = react__WEBPACK_IMPORTED_MODULE_12__.useState(false), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState, 2), mobile = _React$useState2[0], setMobile = _React$useState2[1];\n        (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function() {\n            setMobile((0,rc_util_es_isMobile__WEBPACK_IMPORTED_MODULE_11__[\"default\"])());\n        }, []);\n        // ========================== Context ===========================\n        var subPopupElements = react__WEBPACK_IMPORTED_MODULE_12__.useRef({});\n        var parentContext = react__WEBPACK_IMPORTED_MODULE_12__.useContext(_context__WEBPACK_IMPORTED_MODULE_15__[\"default\"]);\n        var context = react__WEBPACK_IMPORTED_MODULE_12__.useMemo(function() {\n            return {\n                registerSubPopup: function registerSubPopup(id, subPopupEle) {\n                    subPopupElements.current[id] = subPopupEle;\n                    parentContext === null || parentContext === void 0 || parentContext.registerSubPopup(id, subPopupEle);\n                }\n            };\n        }, [\n            parentContext\n        ]);\n        // =========================== Popup ============================\n        var id = (0,rc_util_es_hooks_useId__WEBPACK_IMPORTED_MODULE_9__[\"default\"])();\n        var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_12__.useState(null), _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState3, 2), popupEle = _React$useState4[0], setPopupEle = _React$useState4[1];\n        var setPopupRef = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function(node) {\n            if ((0,rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_6__.isDOM)(node) && popupEle !== node) {\n                setPopupEle(node);\n            }\n            parentContext === null || parentContext === void 0 || parentContext.registerSubPopup(id, node);\n        });\n        // =========================== Target ===========================\n        // Use state to control here since `useRef` update not trigger render\n        var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_12__.useState(null), _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState5, 2), targetEle = _React$useState6[0], setTargetEle = _React$useState6[1];\n        // Used for forwardRef target. Not use internal\n        var externalForwardRef = react__WEBPACK_IMPORTED_MODULE_12__.useRef(null);\n        var setTargetRef = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function(node) {\n            if ((0,rc_util_es_Dom_findDOMNode__WEBPACK_IMPORTED_MODULE_6__.isDOM)(node) && targetEle !== node) {\n                setTargetEle(node);\n                externalForwardRef.current = node;\n            }\n        });\n        // ========================== Children ==========================\n        var child = react__WEBPACK_IMPORTED_MODULE_12__.Children.only(children);\n        var originChildProps = (child === null || child === void 0 ? void 0 : child.props) || {};\n        var cloneProps = {};\n        var inPopupOrChild = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function(ele) {\n            var _getShadowRoot, _getShadowRoot2;\n            var childDOM = targetEle;\n            return (childDOM === null || childDOM === void 0 ? void 0 : childDOM.contains(ele)) || ((_getShadowRoot = (0,rc_util_es_Dom_shadow__WEBPACK_IMPORTED_MODULE_7__.getShadowRoot)(childDOM)) === null || _getShadowRoot === void 0 ? void 0 : _getShadowRoot.host) === ele || ele === childDOM || (popupEle === null || popupEle === void 0 ? void 0 : popupEle.contains(ele)) || ((_getShadowRoot2 = (0,rc_util_es_Dom_shadow__WEBPACK_IMPORTED_MODULE_7__.getShadowRoot)(popupEle)) === null || _getShadowRoot2 === void 0 ? void 0 : _getShadowRoot2.host) === ele || ele === popupEle || Object.values(subPopupElements.current).some(function(subPopupEle) {\n                return (subPopupEle === null || subPopupEle === void 0 ? void 0 : subPopupEle.contains(ele)) || ele === subPopupEle;\n            });\n        });\n        // =========================== Motion ===========================\n        var mergePopupMotion = (0,_util__WEBPACK_IMPORTED_MODULE_20__.getMotion)(prefixCls, popupMotion, popupAnimation, popupTransitionName);\n        var mergeMaskMotion = (0,_util__WEBPACK_IMPORTED_MODULE_20__.getMotion)(prefixCls, maskMotion, maskAnimation, maskTransitionName);\n        // ============================ Open ============================\n        var _React$useState7 = react__WEBPACK_IMPORTED_MODULE_12__.useState(defaultPopupVisible || false), _React$useState8 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState7, 2), internalOpen = _React$useState8[0], setInternalOpen = _React$useState8[1];\n        // Render still use props as first priority\n        var mergedOpen = popupVisible !== null && popupVisible !== void 0 ? popupVisible : internalOpen;\n        // We use effect sync here in case `popupVisible` back to `undefined`\n        var setMergedOpen = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function(nextOpen) {\n            if (popupVisible === undefined) {\n                setInternalOpen(nextOpen);\n            }\n        });\n        (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function() {\n            setInternalOpen(popupVisible || false);\n        }, [\n            popupVisible\n        ]);\n        var openRef = react__WEBPACK_IMPORTED_MODULE_12__.useRef(mergedOpen);\n        openRef.current = mergedOpen;\n        var lastTriggerRef = react__WEBPACK_IMPORTED_MODULE_12__.useRef([]);\n        lastTriggerRef.current = [];\n        var internalTriggerOpen = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function(nextOpen) {\n            var _lastTriggerRef$curre;\n            setMergedOpen(nextOpen);\n            // Enter or Pointer will both trigger open state change\n            // We only need take one to avoid duplicated change event trigger\n            // Use `lastTriggerRef` to record last open type\n            if (((_lastTriggerRef$curre = lastTriggerRef.current[lastTriggerRef.current.length - 1]) !== null && _lastTriggerRef$curre !== void 0 ? _lastTriggerRef$curre : mergedOpen) !== nextOpen) {\n                lastTriggerRef.current.push(nextOpen);\n                onPopupVisibleChange === null || onPopupVisibleChange === void 0 || onPopupVisibleChange(nextOpen);\n            }\n        });\n        // Trigger for delay\n        var delayRef = react__WEBPACK_IMPORTED_MODULE_12__.useRef();\n        var clearDelay = function clearDelay() {\n            clearTimeout(delayRef.current);\n        };\n        var triggerOpen = function triggerOpen(nextOpen) {\n            var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n            clearDelay();\n            if (delay === 0) {\n                internalTriggerOpen(nextOpen);\n            } else {\n                delayRef.current = setTimeout(function() {\n                    internalTriggerOpen(nextOpen);\n                }, delay * 1000);\n            }\n        };\n        react__WEBPACK_IMPORTED_MODULE_12__.useEffect(function() {\n            return clearDelay;\n        }, []);\n        // ========================== Motion ============================\n        var _React$useState9 = react__WEBPACK_IMPORTED_MODULE_12__.useState(false), _React$useState10 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState9, 2), inMotion = _React$useState10[0], setInMotion = _React$useState10[1];\n        (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function(firstMount) {\n            if (!firstMount || mergedOpen) {\n                setInMotion(true);\n            }\n        }, [\n            mergedOpen\n        ]);\n        var _React$useState11 = react__WEBPACK_IMPORTED_MODULE_12__.useState(null), _React$useState12 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState11, 2), motionPrepareResolve = _React$useState12[0], setMotionPrepareResolve = _React$useState12[1];\n        // =========================== Align ============================\n        var _React$useState13 = react__WEBPACK_IMPORTED_MODULE_12__.useState([\n            0,\n            0\n        ]), _React$useState14 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState13, 2), mousePos = _React$useState14[0], setMousePos = _React$useState14[1];\n        var setMousePosByEvent = function setMousePosByEvent(event) {\n            setMousePos([\n                event.clientX,\n                event.clientY\n            ]);\n        };\n        var _useAlign = (0,_hooks_useAlign__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(mergedOpen, popupEle, alignPoint ? mousePos : targetEle, popupPlacement, builtinPlacements, popupAlign, onPopupAlign), _useAlign2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_useAlign, 11), ready = _useAlign2[0], offsetX = _useAlign2[1], offsetY = _useAlign2[2], offsetR = _useAlign2[3], offsetB = _useAlign2[4], arrowX = _useAlign2[5], arrowY = _useAlign2[6], scaleX = _useAlign2[7], scaleY = _useAlign2[8], alignInfo = _useAlign2[9], onAlign = _useAlign2[10];\n        var _useAction = (0,_hooks_useAction__WEBPACK_IMPORTED_MODULE_16__[\"default\"])(mobile, action, showAction, hideAction), _useAction2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_useAction, 2), showActions = _useAction2[0], hideActions = _useAction2[1];\n        var clickToShow = showActions.has(\"click\");\n        var clickToHide = hideActions.has(\"click\") || hideActions.has(\"contextMenu\");\n        var triggerAlign = (0,rc_util_es_hooks_useEvent__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function() {\n            if (!inMotion) {\n                onAlign();\n            }\n        });\n        var onScroll = function onScroll() {\n            if (openRef.current && alignPoint && clickToHide) {\n                triggerOpen(false);\n            }\n        };\n        (0,_hooks_useWatch__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(mergedOpen, targetEle, popupEle, triggerAlign, onScroll);\n        (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function() {\n            triggerAlign();\n        }, [\n            mousePos,\n            popupPlacement\n        ]);\n        // When no builtinPlacements and popupAlign changed\n        (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function() {\n            if (mergedOpen && !(builtinPlacements !== null && builtinPlacements !== void 0 && builtinPlacements[popupPlacement])) {\n                triggerAlign();\n            }\n        }, [\n            JSON.stringify(popupAlign)\n        ]);\n        var alignedClassName = react__WEBPACK_IMPORTED_MODULE_12__.useMemo(function() {\n            var baseClassName = (0,_util__WEBPACK_IMPORTED_MODULE_20__.getAlignPopupClassName)(builtinPlacements, prefixCls, alignInfo, alignPoint);\n            return classnames__WEBPACK_IMPORTED_MODULE_4___default()(baseClassName, getPopupClassNameFromAlign === null || getPopupClassNameFromAlign === void 0 ? void 0 : getPopupClassNameFromAlign(alignInfo));\n        }, [\n            alignInfo,\n            getPopupClassNameFromAlign,\n            builtinPlacements,\n            prefixCls,\n            alignPoint\n        ]);\n        // ============================ Refs ============================\n        react__WEBPACK_IMPORTED_MODULE_12__.useImperativeHandle(ref, function() {\n            return {\n                nativeElement: externalForwardRef.current,\n                forceAlign: triggerAlign\n            };\n        });\n        // ========================== Stretch ===========================\n        var _React$useState15 = react__WEBPACK_IMPORTED_MODULE_12__.useState(0), _React$useState16 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState15, 2), targetWidth = _React$useState16[0], setTargetWidth = _React$useState16[1];\n        var _React$useState17 = react__WEBPACK_IMPORTED_MODULE_12__.useState(0), _React$useState18 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_React$useState17, 2), targetHeight = _React$useState18[0], setTargetHeight = _React$useState18[1];\n        var syncTargetSize = function syncTargetSize() {\n            if (stretch && targetEle) {\n                var rect = targetEle.getBoundingClientRect();\n                setTargetWidth(rect.width);\n                setTargetHeight(rect.height);\n            }\n        };\n        var onTargetResize = function onTargetResize() {\n            syncTargetSize();\n            triggerAlign();\n        };\n        // ========================== Motion ============================\n        var onVisibleChanged = function onVisibleChanged(visible) {\n            setInMotion(false);\n            onAlign();\n            afterPopupVisibleChange === null || afterPopupVisibleChange === void 0 || afterPopupVisibleChange(visible);\n        };\n        // We will trigger align when motion is in prepare\n        var onPrepare = function onPrepare() {\n            return new Promise(function(resolve) {\n                syncTargetSize();\n                setMotionPrepareResolve(function() {\n                    return resolve;\n                });\n            });\n        };\n        (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function() {\n            if (motionPrepareResolve) {\n                onAlign();\n                motionPrepareResolve();\n                setMotionPrepareResolve(null);\n            }\n        }, [\n            motionPrepareResolve\n        ]);\n        // =========================== Action ===========================\n        /**\n     * Util wrapper for trigger action\n     */ function wrapperAction(eventName, nextOpen, delay, preEvent) {\n            cloneProps[eventName] = function(event) {\n                var _originChildProps$eve;\n                preEvent === null || preEvent === void 0 || preEvent(event);\n                triggerOpen(nextOpen, delay);\n                // Pass to origin\n                for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n                    args[_key - 1] = arguments[_key];\n                }\n                (_originChildProps$eve = originChildProps[eventName]) === null || _originChildProps$eve === void 0 || _originChildProps$eve.call.apply(_originChildProps$eve, [\n                    originChildProps,\n                    event\n                ].concat(args));\n            };\n        }\n        // ======================= Action: Click ========================\n        if (clickToShow || clickToHide) {\n            cloneProps.onClick = function(event) {\n                var _originChildProps$onC;\n                if (openRef.current && clickToHide) {\n                    triggerOpen(false);\n                } else if (!openRef.current && clickToShow) {\n                    setMousePosByEvent(event);\n                    triggerOpen(true);\n                }\n                // Pass to origin\n                for(var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++){\n                    args[_key2 - 1] = arguments[_key2];\n                }\n                (_originChildProps$onC = originChildProps.onClick) === null || _originChildProps$onC === void 0 || _originChildProps$onC.call.apply(_originChildProps$onC, [\n                    originChildProps,\n                    event\n                ].concat(args));\n            };\n        }\n        // Click to hide is special action since click popup element should not hide\n        (0,_hooks_useWinClick__WEBPACK_IMPORTED_MODULE_19__[\"default\"])(mergedOpen, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen);\n        // ======================= Action: Hover ========================\n        var hoverToShow = showActions.has(\"hover\");\n        var hoverToHide = hideActions.has(\"hover\");\n        var onPopupMouseEnter;\n        var onPopupMouseLeave;\n        if (hoverToShow) {\n            // Compatible with old browser which not support pointer event\n            wrapperAction(\"onMouseEnter\", true, mouseEnterDelay, function(event) {\n                setMousePosByEvent(event);\n            });\n            wrapperAction(\"onPointerEnter\", true, mouseEnterDelay, function(event) {\n                setMousePosByEvent(event);\n            });\n            onPopupMouseEnter = function onPopupMouseEnter(event) {\n                // Only trigger re-open when popup is visible\n                if ((mergedOpen || inMotion) && popupEle !== null && popupEle !== void 0 && popupEle.contains(event.target)) {\n                    triggerOpen(true, mouseEnterDelay);\n                }\n            };\n            // Align Point\n            if (alignPoint) {\n                cloneProps.onMouseMove = function(event) {\n                    var _originChildProps$onM;\n                    // setMousePosByEvent(event);\n                    (_originChildProps$onM = originChildProps.onMouseMove) === null || _originChildProps$onM === void 0 || _originChildProps$onM.call(originChildProps, event);\n                };\n            }\n        }\n        if (hoverToHide) {\n            wrapperAction(\"onMouseLeave\", false, mouseLeaveDelay);\n            wrapperAction(\"onPointerLeave\", false, mouseLeaveDelay);\n            onPopupMouseLeave = function onPopupMouseLeave() {\n                triggerOpen(false, mouseLeaveDelay);\n            };\n        }\n        // ======================= Action: Focus ========================\n        if (showActions.has(\"focus\")) {\n            wrapperAction(\"onFocus\", true, focusDelay);\n        }\n        if (hideActions.has(\"focus\")) {\n            wrapperAction(\"onBlur\", false, blurDelay);\n        }\n        // ==================== Action: ContextMenu =====================\n        if (showActions.has(\"contextMenu\")) {\n            cloneProps.onContextMenu = function(event) {\n                var _originChildProps$onC2;\n                if (openRef.current && hideActions.has(\"contextMenu\")) {\n                    triggerOpen(false);\n                } else {\n                    setMousePosByEvent(event);\n                    triggerOpen(true);\n                }\n                event.preventDefault();\n                // Pass to origin\n                for(var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++){\n                    args[_key3 - 1] = arguments[_key3];\n                }\n                (_originChildProps$onC2 = originChildProps.onContextMenu) === null || _originChildProps$onC2 === void 0 || _originChildProps$onC2.call.apply(_originChildProps$onC2, [\n                    originChildProps,\n                    event\n                ].concat(args));\n            };\n        }\n        // ========================= ClassName ==========================\n        if (className) {\n            cloneProps.className = classnames__WEBPACK_IMPORTED_MODULE_4___default()(originChildProps.className, className);\n        }\n        // =========================== Render ===========================\n        var mergedChildrenProps = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, originChildProps), cloneProps);\n        // Pass props into cloneProps for nest usage\n        var passedProps = {};\n        var passedEventList = [\n            \"onContextMenu\",\n            \"onClick\",\n            \"onMouseDown\",\n            \"onTouchStart\",\n            \"onMouseEnter\",\n            \"onMouseLeave\",\n            \"onFocus\",\n            \"onBlur\"\n        ];\n        passedEventList.forEach(function(eventName) {\n            if (restProps[eventName]) {\n                passedProps[eventName] = function() {\n                    var _mergedChildrenProps$;\n                    for(var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++){\n                        args[_key4] = arguments[_key4];\n                    }\n                    (_mergedChildrenProps$ = mergedChildrenProps[eventName]) === null || _mergedChildrenProps$ === void 0 || _mergedChildrenProps$.call.apply(_mergedChildrenProps$, [\n                        mergedChildrenProps\n                    ].concat(args));\n                    restProps[eventName].apply(restProps, args);\n                };\n            }\n        });\n        // Child Node\n        var triggerNode = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_12__.cloneElement(child, (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, mergedChildrenProps), passedProps));\n        var arrowPos = {\n            x: arrowX,\n            y: arrowY\n        };\n        var innerArrow = arrow ? (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, arrow !== true ? arrow : {}) : null;\n        // Render\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_12__.createElement(react__WEBPACK_IMPORTED_MODULE_12__.Fragment, null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_12__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            disabled: !mergedOpen,\n            ref: setTargetRef,\n            onResize: onTargetResize\n        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_12__.createElement(_TriggerWrapper__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            getTriggerDOMNode: getTriggerDOMNode\n        }, triggerNode)), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_12__.createElement(_context__WEBPACK_IMPORTED_MODULE_15__[\"default\"].Provider, {\n            value: context\n        }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_12__.createElement(_Popup__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            portal: PortalComponent,\n            ref: setPopupRef,\n            prefixCls: prefixCls,\n            popup: popup,\n            className: classnames__WEBPACK_IMPORTED_MODULE_4___default()(popupClassName, alignedClassName),\n            style: popupStyle,\n            target: targetEle,\n            onMouseEnter: onPopupMouseEnter,\n            onMouseLeave: onPopupMouseLeave,\n            onPointerEnter: onPopupMouseEnter,\n            zIndex: zIndex,\n            open: mergedOpen,\n            keepDom: inMotion,\n            fresh: fresh,\n            onClick: onPopupClick,\n            mask: mask,\n            motion: mergePopupMotion,\n            maskMotion: mergeMaskMotion,\n            onVisibleChanged: onVisibleChanged,\n            onPrepare: onPrepare,\n            forceRender: forceRender,\n            autoDestroy: mergedAutoDestroy,\n            getPopupContainer: getPopupContainer,\n            align: alignInfo,\n            arrow: innerArrow,\n            arrowPos: arrowPos,\n            ready: ready,\n            offsetX: offsetX,\n            offsetY: offsetY,\n            offsetR: offsetR,\n            offsetB: offsetB,\n            onAlign: triggerAlign,\n            stretch: stretch,\n            targetWidth: targetWidth / scaleX,\n            targetHeight: targetHeight / scaleY\n        })));\n    });\n    if (true) {\n        Trigger.displayName = \"Trigger\";\n    }\n    return Trigger;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (generateTrigger(_rc_component_portal__WEBPACK_IMPORTED_MODULE_3__[\"default\"]));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/trigger/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@rc-component/trigger/es/util.js":
/*!*******************************************************!*\
  !*** ./node_modules/@rc-component/trigger/es/util.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   collectScroller: () => (/* binding */ collectScroller),\n/* harmony export */   getAlignPopupClassName: () => (/* binding */ getAlignPopupClassName),\n/* harmony export */   getMotion: () => (/* binding */ getMotion),\n/* harmony export */   getVisibleArea: () => (/* binding */ getVisibleArea),\n/* harmony export */   getWin: () => (/* binding */ getWin),\n/* harmony export */   toNum: () => (/* binding */ toNum)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n\nfunction isPointsEq() {\n    var a1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    var a2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    var isAlignPoint = arguments.length > 2 ? arguments[2] : undefined;\n    if (isAlignPoint) {\n        return a1[0] === a2[0];\n    }\n    return a1[0] === a2[0] && a1[1] === a2[1];\n}\nfunction getAlignPopupClassName(builtinPlacements, prefixCls, align, isAlignPoint) {\n    var points = align.points;\n    var placements = Object.keys(builtinPlacements);\n    for(var i = 0; i < placements.length; i += 1){\n        var _builtinPlacements$pl;\n        var placement = placements[i];\n        if (isPointsEq((_builtinPlacements$pl = builtinPlacements[placement]) === null || _builtinPlacements$pl === void 0 ? void 0 : _builtinPlacements$pl.points, points, isAlignPoint)) {\n            return \"\".concat(prefixCls, \"-placement-\").concat(placement);\n        }\n    }\n    return \"\";\n}\n/** @deprecated We should not use this if we can refactor all deps */ function getMotion(prefixCls, motion, animation, transitionName) {\n    if (motion) {\n        return motion;\n    }\n    if (animation) {\n        return {\n            motionName: \"\".concat(prefixCls, \"-\").concat(animation)\n        };\n    }\n    if (transitionName) {\n        return {\n            motionName: transitionName\n        };\n    }\n    return null;\n}\nfunction getWin(ele) {\n    return ele.ownerDocument.defaultView;\n}\n/**\n * Get all the scrollable parent elements of the element\n * @param ele       The element to be detected\n * @param areaOnly  Only return the parent which will cut visible area\n */ function collectScroller(ele) {\n    var scrollerList = [];\n    var current = ele === null || ele === void 0 ? void 0 : ele.parentElement;\n    var scrollStyle = [\n        \"hidden\",\n        \"scroll\",\n        \"clip\",\n        \"auto\"\n    ];\n    while(current){\n        var _getWin$getComputedSt = getWin(current).getComputedStyle(current), overflowX = _getWin$getComputedSt.overflowX, overflowY = _getWin$getComputedSt.overflowY, overflow = _getWin$getComputedSt.overflow;\n        if ([\n            overflowX,\n            overflowY,\n            overflow\n        ].some(function(o) {\n            return scrollStyle.includes(o);\n        })) {\n            scrollerList.push(current);\n        }\n        current = current.parentElement;\n    }\n    return scrollerList;\n}\nfunction toNum(num) {\n    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    return Number.isNaN(num) ? defaultValue : num;\n}\nfunction getPxValue(val) {\n    return toNum(parseFloat(val), 0);\n}\n/**\n *\n *\n *  **************************************\n *  *              Border                *\n *  *     **************************     *\n *  *     *                  *     *     *\n *  *  B  *                  *  S  *  B  *\n *  *  o  *                  *  c  *  o  *\n *  *  r  *      Content     *  r  *  r  *\n *  *  d  *                  *  o  *  d  *\n *  *  e  *                  *  l  *  e  *\n *  *  r  ********************  l  *  r  *\n *  *     *        Scroll          *     *\n *  *     **************************     *\n *  *              Border                *\n *  **************************************\n *\n */ /**\n * Get visible area of element\n */ function getVisibleArea(initArea, scrollerList) {\n    var visibleArea = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, initArea);\n    (scrollerList || []).forEach(function(ele) {\n        if (ele instanceof HTMLBodyElement || ele instanceof HTMLHtmlElement) {\n            return;\n        }\n        // Skip if static position which will not affect visible area\n        var _getWin$getComputedSt2 = getWin(ele).getComputedStyle(ele), overflow = _getWin$getComputedSt2.overflow, overflowClipMargin = _getWin$getComputedSt2.overflowClipMargin, borderTopWidth = _getWin$getComputedSt2.borderTopWidth, borderBottomWidth = _getWin$getComputedSt2.borderBottomWidth, borderLeftWidth = _getWin$getComputedSt2.borderLeftWidth, borderRightWidth = _getWin$getComputedSt2.borderRightWidth;\n        var eleRect = ele.getBoundingClientRect();\n        var eleOutHeight = ele.offsetHeight, eleInnerHeight = ele.clientHeight, eleOutWidth = ele.offsetWidth, eleInnerWidth = ele.clientWidth;\n        var borderTopNum = getPxValue(borderTopWidth);\n        var borderBottomNum = getPxValue(borderBottomWidth);\n        var borderLeftNum = getPxValue(borderLeftWidth);\n        var borderRightNum = getPxValue(borderRightWidth);\n        var scaleX = toNum(Math.round(eleRect.width / eleOutWidth * 1000) / 1000);\n        var scaleY = toNum(Math.round(eleRect.height / eleOutHeight * 1000) / 1000);\n        // Original visible area\n        var eleScrollWidth = (eleOutWidth - eleInnerWidth - borderLeftNum - borderRightNum) * scaleX;\n        var eleScrollHeight = (eleOutHeight - eleInnerHeight - borderTopNum - borderBottomNum) * scaleY;\n        // Cut border size\n        var scaledBorderTopWidth = borderTopNum * scaleY;\n        var scaledBorderBottomWidth = borderBottomNum * scaleY;\n        var scaledBorderLeftWidth = borderLeftNum * scaleX;\n        var scaledBorderRightWidth = borderRightNum * scaleX;\n        // Clip margin\n        var clipMarginWidth = 0;\n        var clipMarginHeight = 0;\n        if (overflow === \"clip\") {\n            var clipNum = getPxValue(overflowClipMargin);\n            clipMarginWidth = clipNum * scaleX;\n            clipMarginHeight = clipNum * scaleY;\n        }\n        // Region\n        var eleLeft = eleRect.x + scaledBorderLeftWidth - clipMarginWidth;\n        var eleTop = eleRect.y + scaledBorderTopWidth - clipMarginHeight;\n        var eleRight = eleLeft + eleRect.width + 2 * clipMarginWidth - scaledBorderLeftWidth - scaledBorderRightWidth - eleScrollWidth;\n        var eleBottom = eleTop + eleRect.height + 2 * clipMarginHeight - scaledBorderTopWidth - scaledBorderBottomWidth - eleScrollHeight;\n        visibleArea.left = Math.max(visibleArea.left, eleLeft);\n        visibleArea.top = Math.max(visibleArea.top, eleTop);\n        visibleArea.right = Math.min(visibleArea.right, eleRight);\n        visibleArea.bottom = Math.min(visibleArea.bottom, eleBottom);\n    });\n    return visibleArea;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@rc-component/trigger/es/util.js\n");

/***/ })

};
;