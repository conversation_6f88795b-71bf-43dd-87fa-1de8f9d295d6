"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rc-textarea";
exports.ids = ["vendor-chunks/rc-textarea"];
exports.modules = {

/***/ "(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js":
/*!**********************************************************!*\
  !*** ./node_modules/rc-textarea/es/ResizableTextArea.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/typeof */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/typeof.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_resize_observer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-resize-observer */ \"(ssr)/./node_modules/rc-resize-observer/es/index.js\");\n/* harmony import */ var rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-util/es/hooks/useLayoutEffect */ \"(ssr)/./node_modules/rc-util/es/hooks/useLayoutEffect.js\");\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/raf */ \"(ssr)/./node_modules/rc-util/es/raf.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _calculateNodeHeight__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./calculateNodeHeight */ \"(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js\");\n\n\n\n\n\n\nvar _excluded = [\n    \"prefixCls\",\n    \"onPressEnter\",\n    \"defaultValue\",\n    \"value\",\n    \"autoSize\",\n    \"onResize\",\n    \"className\",\n    \"style\",\n    \"disabled\",\n    \"onChange\",\n    \"onInternalAutoSize\"\n];\n\n\n\n\n\n\n\nvar RESIZE_START = 0;\nvar RESIZE_MEASURING = 1;\nvar RESIZE_STABLE = 2;\nvar ResizableTextArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11__.forwardRef(function(props, ref) {\n    var _ref = props, prefixCls = _ref.prefixCls, onPressEnter = _ref.onPressEnter, defaultValue = _ref.defaultValue, value = _ref.value, autoSize = _ref.autoSize, onResize = _ref.onResize, className = _ref.className, style = _ref.style, disabled = _ref.disabled, onChange = _ref.onChange, onInternalAutoSize = _ref.onInternalAutoSize, restProps = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_ref, _excluded);\n    // =============================== Value ================================\n    var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(defaultValue, {\n        value: value,\n        postState: function postState(val) {\n            return val !== null && val !== void 0 ? val : \"\";\n        }\n    }), _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useMergedState, 2), mergedValue = _useMergedState2[0], setMergedValue = _useMergedState2[1];\n    var onInternalChange = function onInternalChange(event) {\n        setMergedValue(event.target.value);\n        onChange === null || onChange === void 0 || onChange(event);\n    };\n    // ================================ Ref =================================\n    var textareaRef = react__WEBPACK_IMPORTED_MODULE_11__.useRef();\n    react__WEBPACK_IMPORTED_MODULE_11__.useImperativeHandle(ref, function() {\n        return {\n            textArea: textareaRef.current\n        };\n    });\n    // ============================== AutoSize ==============================\n    var _React$useMemo = react__WEBPACK_IMPORTED_MODULE_11__.useMemo(function() {\n        if (autoSize && (0,_babel_runtime_helpers_esm_typeof__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(autoSize) === \"object\") {\n            return [\n                autoSize.minRows,\n                autoSize.maxRows\n            ];\n        }\n        return [];\n    }, [\n        autoSize\n    ]), _React$useMemo2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useMemo, 2), minRows = _React$useMemo2[0], maxRows = _React$useMemo2[1];\n    var needAutoSize = !!autoSize;\n    // =============================== Scroll ===============================\n    // https://github.com/ant-design/ant-design/issues/21870\n    var fixFirefoxAutoScroll = function fixFirefoxAutoScroll() {\n        try {\n            // FF has bug with jump of scroll to top. We force back here.\n            if (document.activeElement === textareaRef.current) {\n                var _textareaRef$current = textareaRef.current, selectionStart = _textareaRef$current.selectionStart, selectionEnd = _textareaRef$current.selectionEnd, scrollTop = _textareaRef$current.scrollTop;\n                // Fix Safari bug which not rollback when break line\n                // This makes Chinese IME can't input. Do not fix this\n                // const { value: tmpValue } = textareaRef.current;\n                // textareaRef.current.value = '';\n                // textareaRef.current.value = tmpValue;\n                textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n                textareaRef.current.scrollTop = scrollTop;\n            }\n        } catch (e) {\n        // Fix error in Chrome:\n        // Failed to read the 'selectionStart' property from 'HTMLInputElement'\n        // http://stackoverflow.com/q/21177489/3040605\n        }\n    };\n    // =============================== Resize ===============================\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_11__.useState(RESIZE_STABLE), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState, 2), resizeState = _React$useState2[0], setResizeState = _React$useState2[1];\n    var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_11__.useState(), _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState3, 2), autoSizeStyle = _React$useState4[0], setAutoSizeStyle = _React$useState4[1];\n    var startResize = function startResize() {\n        setResizeState(RESIZE_START);\n        if (false) {}\n    };\n    // Change to trigger resize measure\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function() {\n        if (needAutoSize) {\n            startResize();\n        }\n    }, [\n        value,\n        minRows,\n        maxRows,\n        needAutoSize\n    ]);\n    (0,rc_util_es_hooks_useLayoutEffect__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function() {\n        if (resizeState === RESIZE_START) {\n            setResizeState(RESIZE_MEASURING);\n        } else if (resizeState === RESIZE_MEASURING) {\n            var textareaStyles = (0,_calculateNodeHeight__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(textareaRef.current, false, minRows, maxRows);\n            // Safari has bug that text will keep break line on text cut when it's prev is break line.\n            // ZombieJ: This not often happen. So we just skip it.\n            // const { selectionStart, selectionEnd, scrollTop } = textareaRef.current;\n            // const { value: tmpValue } = textareaRef.current;\n            // textareaRef.current.value = '';\n            // textareaRef.current.value = tmpValue;\n            // if (document.activeElement === textareaRef.current) {\n            //   textareaRef.current.scrollTop = scrollTop;\n            //   textareaRef.current.setSelectionRange(selectionStart, selectionEnd);\n            // }\n            setResizeState(RESIZE_STABLE);\n            setAutoSizeStyle(textareaStyles);\n        } else {\n            fixFirefoxAutoScroll();\n        }\n    }, [\n        resizeState\n    ]);\n    // We lock resize trigger by raf to avoid Safari warning\n    var resizeRafRef = react__WEBPACK_IMPORTED_MODULE_11__.useRef();\n    var cleanRaf = function cleanRaf() {\n        rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__[\"default\"].cancel(resizeRafRef.current);\n    };\n    var onInternalResize = function onInternalResize(size) {\n        if (resizeState === RESIZE_STABLE) {\n            onResize === null || onResize === void 0 || onResize(size);\n            if (autoSize) {\n                cleanRaf();\n                resizeRafRef.current = (0,rc_util_es_raf__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(function() {\n                    startResize();\n                });\n            }\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_11__.useEffect(function() {\n        return cleanRaf;\n    }, []);\n    // =============================== Render ===============================\n    var mergedAutoSizeStyle = needAutoSize ? autoSizeStyle : null;\n    var mergedStyle = (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, style), mergedAutoSizeStyle);\n    if (resizeState === RESIZE_START || resizeState === RESIZE_MEASURING) {\n        mergedStyle.overflowY = \"hidden\";\n        mergedStyle.overflowX = \"hidden\";\n    }\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11__.createElement(rc_resize_observer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        onResize: onInternalResize,\n        disabled: !(autoSize || onResize)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, restProps, {\n        ref: textareaRef,\n        style: mergedStyle,\n        className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(prefixCls, className, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({}, \"\".concat(prefixCls, \"-disabled\"), disabled)),\n        disabled: disabled,\n        value: mergedValue,\n        onChange: onInternalChange\n    })));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResizableTextArea);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/TextArea.js":
/*!*************************************************!*\
  !*** ./node_modules/rc-textarea/es/TextArea.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/defineProperty */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectSpread2 */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @babel/runtime/helpers/esm/toConsumableArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @babel/runtime/helpers/esm/slicedToArray */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutProperties */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! classnames */ \"(ssr)/./node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var rc_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rc-input */ \"(ssr)/./node_modules/rc-input/es/index.js\");\n/* harmony import */ var rc_input_es_hooks_useCount__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! rc-input/es/hooks/useCount */ \"(ssr)/./node_modules/rc-input/es/hooks/useCount.js\");\n/* harmony import */ var rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! rc-input/es/utils/commonUtils */ \"(ssr)/./node_modules/rc-input/es/utils/commonUtils.js\");\n/* harmony import */ var rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! rc-util/es/hooks/useMergedState */ \"(ssr)/./node_modules/rc-util/es/hooks/useMergedState.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _ResizableTextArea__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./ResizableTextArea */ \"(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\");\n\n\n\n\n\n\nvar _excluded = [\n    \"defaultValue\",\n    \"value\",\n    \"onFocus\",\n    \"onBlur\",\n    \"onChange\",\n    \"allowClear\",\n    \"maxLength\",\n    \"onCompositionStart\",\n    \"onCompositionEnd\",\n    \"suffix\",\n    \"prefixCls\",\n    \"showCount\",\n    \"count\",\n    \"className\",\n    \"style\",\n    \"disabled\",\n    \"hidden\",\n    \"classNames\",\n    \"styles\",\n    \"onResize\"\n];\n\n\n\n\n\n\n\nvar TextArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11___default().forwardRef(function(_ref, ref) {\n    var _countConfig$max, _clsx;\n    var defaultValue = _ref.defaultValue, customValue = _ref.value, onFocus = _ref.onFocus, onBlur = _ref.onBlur, onChange = _ref.onChange, allowClear = _ref.allowClear, maxLength = _ref.maxLength, onCompositionStart = _ref.onCompositionStart, onCompositionEnd = _ref.onCompositionEnd, suffix = _ref.suffix, _ref$prefixCls = _ref.prefixCls, prefixCls = _ref$prefixCls === void 0 ? \"rc-textarea\" : _ref$prefixCls, showCount = _ref.showCount, count = _ref.count, className = _ref.className, style = _ref.style, disabled = _ref.disabled, hidden = _ref.hidden, classNames = _ref.classNames, styles = _ref.styles, onResize = _ref.onResize, rest = (0,_babel_runtime_helpers_esm_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(_ref, _excluded);\n    var _useMergedState = (0,rc_util_es_hooks_useMergedState__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(defaultValue, {\n        value: customValue,\n        defaultValue: defaultValue\n    }), _useMergedState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_useMergedState, 2), value = _useMergedState2[0], setValue = _useMergedState2[1];\n    var formatValue = value === undefined || value === null ? \"\" : String(value);\n    var _React$useState = react__WEBPACK_IMPORTED_MODULE_11___default().useState(false), _React$useState2 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState, 2), focused = _React$useState2[0], setFocused = _React$useState2[1];\n    var compositionRef = react__WEBPACK_IMPORTED_MODULE_11___default().useRef(false);\n    var _React$useState3 = react__WEBPACK_IMPORTED_MODULE_11___default().useState(null), _React$useState4 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState3, 2), textareaResized = _React$useState4[0], setTextareaResized = _React$useState4[1];\n    // =============================== Ref ================================\n    var resizableTextAreaRef = (0,react__WEBPACK_IMPORTED_MODULE_11__.useRef)(null);\n    var getTextArea = function getTextArea() {\n        var _resizableTextAreaRef;\n        return (_resizableTextAreaRef = resizableTextAreaRef.current) === null || _resizableTextAreaRef === void 0 ? void 0 : _resizableTextAreaRef.textArea;\n    };\n    var focus = function focus() {\n        getTextArea().focus();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_11__.useImperativeHandle)(ref, function() {\n        return {\n            resizableTextArea: resizableTextAreaRef.current,\n            focus: focus,\n            blur: function blur() {\n                getTextArea().blur();\n            }\n        };\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_11__.useEffect)(function() {\n        setFocused(function(prev) {\n            return !disabled && prev;\n        });\n    }, [\n        disabled\n    ]);\n    // =========================== Select Range ===========================\n    var _React$useState5 = react__WEBPACK_IMPORTED_MODULE_11___default().useState(null), _React$useState6 = (0,_babel_runtime_helpers_esm_slicedToArray__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_React$useState5, 2), selection = _React$useState6[0], setSelection = _React$useState6[1];\n    react__WEBPACK_IMPORTED_MODULE_11___default().useEffect(function() {\n        if (selection) {\n            var _getTextArea;\n            (_getTextArea = getTextArea()).setSelectionRange.apply(_getTextArea, (0,_babel_runtime_helpers_esm_toConsumableArray__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(selection));\n        }\n    }, [\n        selection\n    ]);\n    // ============================== Count ===============================\n    var countConfig = (0,rc_input_es_hooks_useCount__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(count, showCount);\n    var mergedMax = (_countConfig$max = countConfig.max) !== null && _countConfig$max !== void 0 ? _countConfig$max : maxLength;\n    // Max length value\n    var hasMaxLength = Number(mergedMax) > 0;\n    var valueLength = countConfig.strategy(formatValue);\n    var isOutOfRange = !!mergedMax && valueLength > mergedMax;\n    // ============================== Change ==============================\n    var triggerChange = function triggerChange(e, currentValue) {\n        var cutValue = currentValue;\n        if (!compositionRef.current && countConfig.exceedFormatter && countConfig.max && countConfig.strategy(currentValue) > countConfig.max) {\n            cutValue = countConfig.exceedFormatter(currentValue, {\n                max: countConfig.max\n            });\n            if (currentValue !== cutValue) {\n                setSelection([\n                    getTextArea().selectionStart || 0,\n                    getTextArea().selectionEnd || 0\n                ]);\n            }\n        }\n        setValue(cutValue);\n        (0,rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__.resolveOnChange)(e.currentTarget, e, onChange, cutValue);\n    };\n    // =========================== Value Update ===========================\n    var onInternalCompositionStart = function onInternalCompositionStart(e) {\n        compositionRef.current = true;\n        onCompositionStart === null || onCompositionStart === void 0 || onCompositionStart(e);\n    };\n    var onInternalCompositionEnd = function onInternalCompositionEnd(e) {\n        compositionRef.current = false;\n        triggerChange(e, e.currentTarget.value);\n        onCompositionEnd === null || onCompositionEnd === void 0 || onCompositionEnd(e);\n    };\n    var onInternalChange = function onInternalChange(e) {\n        triggerChange(e, e.target.value);\n    };\n    var handleKeyDown = function handleKeyDown(e) {\n        var onPressEnter = rest.onPressEnter, onKeyDown = rest.onKeyDown;\n        if (e.key === \"Enter\" && onPressEnter) {\n            onPressEnter(e);\n        }\n        onKeyDown === null || onKeyDown === void 0 || onKeyDown(e);\n    };\n    var handleFocus = function handleFocus(e) {\n        setFocused(true);\n        onFocus === null || onFocus === void 0 || onFocus(e);\n    };\n    var handleBlur = function handleBlur(e) {\n        setFocused(false);\n        onBlur === null || onBlur === void 0 || onBlur(e);\n    };\n    // ============================== Reset ===============================\n    var handleReset = function handleReset(e) {\n        setValue(\"\");\n        focus();\n        (0,rc_input_es_utils_commonUtils__WEBPACK_IMPORTED_MODULE_9__.resolveOnChange)(getTextArea(), e, onChange);\n    };\n    var suffixNode = suffix;\n    var dataCount;\n    if (countConfig.show) {\n        if (countConfig.showFormatter) {\n            dataCount = countConfig.showFormatter({\n                value: formatValue,\n                count: valueLength,\n                maxLength: mergedMax\n            });\n        } else {\n            dataCount = \"\".concat(valueLength).concat(hasMaxLength ? \" / \".concat(mergedMax) : \"\");\n        }\n        suffixNode = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11___default().createElement((react__WEBPACK_IMPORTED_MODULE_11___default().Fragment), null, suffixNode, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11___default().createElement(\"span\", {\n            className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(\"\".concat(prefixCls, \"-data-count\"), classNames === null || classNames === void 0 ? void 0 : classNames.count),\n            style: styles === null || styles === void 0 ? void 0 : styles.count\n        }, dataCount));\n    }\n    var handleResize = function handleResize(size) {\n        var _getTextArea2;\n        onResize === null || onResize === void 0 || onResize(size);\n        if ((_getTextArea2 = getTextArea()) !== null && _getTextArea2 !== void 0 && _getTextArea2.style.height) {\n            setTextareaResized(true);\n        }\n    };\n    var isPureTextArea = !rest.autoSize && !showCount && !allowClear;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11___default().createElement(rc_input__WEBPACK_IMPORTED_MODULE_7__.BaseInput, {\n        value: formatValue,\n        allowClear: allowClear,\n        handleReset: handleReset,\n        suffix: suffixNode,\n        prefixCls: prefixCls,\n        classNames: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, classNames), {}, {\n            affixWrapper: classnames__WEBPACK_IMPORTED_MODULE_6___default()(classNames === null || classNames === void 0 ? void 0 : classNames.affixWrapper, (_clsx = {}, (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_clsx, \"\".concat(prefixCls, \"-show-count\"), showCount), (0,_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_clsx, \"\".concat(prefixCls, \"-textarea-allow-clear\"), allowClear), _clsx))\n        }),\n        disabled: disabled,\n        focused: focused,\n        className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(className, isOutOfRange && \"\".concat(prefixCls, \"-out-of-range\")),\n        style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, style), textareaResized && !isPureTextArea ? {\n            height: \"auto\"\n        } : {}),\n        dataAttrs: {\n            affixWrapper: {\n                \"data-count\": typeof dataCount === \"string\" ? dataCount : undefined\n            }\n        },\n        hidden: hidden\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_11___default().createElement(_ResizableTextArea__WEBPACK_IMPORTED_MODULE_12__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, rest, {\n        maxLength: maxLength,\n        onKeyDown: handleKeyDown,\n        onChange: onInternalChange,\n        onFocus: handleFocus,\n        onBlur: handleBlur,\n        onCompositionStart: onInternalCompositionStart,\n        onCompositionEnd: onInternalCompositionEnd,\n        className: classnames__WEBPACK_IMPORTED_MODULE_6___default()(classNames === null || classNames === void 0 ? void 0 : classNames.textarea),\n        style: (0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])((0,_babel_runtime_helpers_esm_objectSpread2__WEBPACK_IMPORTED_MODULE_2__[\"default\"])({}, styles === null || styles === void 0 ? void 0 : styles.textarea), {}, {\n            resize: style === null || style === void 0 ? void 0 : style.resize\n        }),\n        disabled: disabled,\n        prefixCls: prefixCls,\n        onResize: handleResize,\n        ref: resizableTextAreaRef\n    })));\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TextArea);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/TextArea.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js":
/*!************************************************************!*\
  !*** ./node_modules/rc-textarea/es/calculateNodeHeight.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateNodeStyling: () => (/* binding */ calculateNodeStyling),\n/* harmony export */   \"default\": () => (/* binding */ calculateAutoSizeStyle)\n/* harmony export */ });\n// Thanks to https://github.com/andreypopp/react-textarea-autosize/\n/**\n * calculateNodeHeight(uiTextNode, useCache = false)\n */ var HIDDEN_TEXTAREA_STYLE = \"\\n  min-height:0 !important;\\n  max-height:none !important;\\n  height:0 !important;\\n  visibility:hidden !important;\\n  overflow:hidden !important;\\n  position:absolute !important;\\n  z-index:-1000 !important;\\n  top:0 !important;\\n  right:0 !important;\\n  pointer-events: none !important;\\n\";\nvar SIZING_STYLE = [\n    \"letter-spacing\",\n    \"line-height\",\n    \"padding-top\",\n    \"padding-bottom\",\n    \"font-family\",\n    \"font-weight\",\n    \"font-size\",\n    \"font-variant\",\n    \"text-rendering\",\n    \"text-transform\",\n    \"width\",\n    \"text-indent\",\n    \"padding-left\",\n    \"padding-right\",\n    \"border-width\",\n    \"box-sizing\",\n    \"word-break\",\n    \"white-space\"\n];\nvar computedStyleCache = {};\nvar hiddenTextarea;\nfunction calculateNodeStyling(node) {\n    var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var nodeRef = node.getAttribute(\"id\") || node.getAttribute(\"data-reactid\") || node.getAttribute(\"name\");\n    if (useCache && computedStyleCache[nodeRef]) {\n        return computedStyleCache[nodeRef];\n    }\n    var style = window.getComputedStyle(node);\n    var boxSizing = style.getPropertyValue(\"box-sizing\") || style.getPropertyValue(\"-moz-box-sizing\") || style.getPropertyValue(\"-webkit-box-sizing\");\n    var paddingSize = parseFloat(style.getPropertyValue(\"padding-bottom\")) + parseFloat(style.getPropertyValue(\"padding-top\"));\n    var borderSize = parseFloat(style.getPropertyValue(\"border-bottom-width\")) + parseFloat(style.getPropertyValue(\"border-top-width\"));\n    var sizingStyle = SIZING_STYLE.map(function(name) {\n        return \"\".concat(name, \":\").concat(style.getPropertyValue(name));\n    }).join(\";\");\n    var nodeInfo = {\n        sizingStyle: sizingStyle,\n        paddingSize: paddingSize,\n        borderSize: borderSize,\n        boxSizing: boxSizing\n    };\n    if (useCache && nodeRef) {\n        computedStyleCache[nodeRef] = nodeInfo;\n    }\n    return nodeInfo;\n}\nfunction calculateAutoSizeStyle(uiTextNode) {\n    var useCache = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    var minRows = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n    var maxRows = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n    if (!hiddenTextarea) {\n        hiddenTextarea = document.createElement(\"textarea\");\n        hiddenTextarea.setAttribute(\"tab-index\", \"-1\");\n        hiddenTextarea.setAttribute(\"aria-hidden\", \"true\");\n        document.body.appendChild(hiddenTextarea);\n    }\n    // Fix wrap=\"off\" issue\n    // https://github.com/ant-design/ant-design/issues/6577\n    if (uiTextNode.getAttribute(\"wrap\")) {\n        hiddenTextarea.setAttribute(\"wrap\", uiTextNode.getAttribute(\"wrap\"));\n    } else {\n        hiddenTextarea.removeAttribute(\"wrap\");\n    }\n    // Copy all CSS properties that have an impact on the height of the content in\n    // the textbox\n    var _calculateNodeStyling = calculateNodeStyling(uiTextNode, useCache), paddingSize = _calculateNodeStyling.paddingSize, borderSize = _calculateNodeStyling.borderSize, boxSizing = _calculateNodeStyling.boxSizing, sizingStyle = _calculateNodeStyling.sizingStyle;\n    // Need to have the overflow attribute to hide the scrollbar otherwise\n    // text-lines will not calculated properly as the shadow will technically be\n    // narrower for content\n    hiddenTextarea.setAttribute(\"style\", \"\".concat(sizingStyle, \";\").concat(HIDDEN_TEXTAREA_STYLE));\n    hiddenTextarea.value = uiTextNode.value || uiTextNode.placeholder || \"\";\n    var minHeight = undefined;\n    var maxHeight = undefined;\n    var overflowY;\n    var height = hiddenTextarea.scrollHeight;\n    if (boxSizing === \"border-box\") {\n        // border-box: add border, since height = content + padding + border\n        height += borderSize;\n    } else if (boxSizing === \"content-box\") {\n        // remove padding, since height = content\n        height -= paddingSize;\n    }\n    if (minRows !== null || maxRows !== null) {\n        // measure height of a textarea with a single row\n        hiddenTextarea.value = \" \";\n        var singleRowHeight = hiddenTextarea.scrollHeight - paddingSize;\n        if (minRows !== null) {\n            minHeight = singleRowHeight * minRows;\n            if (boxSizing === \"border-box\") {\n                minHeight = minHeight + paddingSize + borderSize;\n            }\n            height = Math.max(minHeight, height);\n        }\n        if (maxRows !== null) {\n            maxHeight = singleRowHeight * maxRows;\n            if (boxSizing === \"border-box\") {\n                maxHeight = maxHeight + paddingSize + borderSize;\n            }\n            overflowY = height > maxHeight ? \"\" : \"hidden\";\n            height = Math.min(maxHeight, height);\n        }\n    }\n    var style = {\n        height: height,\n        overflowY: overflowY,\n        resize: \"none\"\n    };\n    if (minHeight) {\n        style.minHeight = minHeight;\n    }\n    if (maxHeight) {\n        style.maxHeight = maxHeight;\n    }\n    return style;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/calculateNodeHeight.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rc-textarea/es/index.js":
/*!**********************************************!*\
  !*** ./node_modules/rc-textarea/es/index.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResizableTextArea: () => (/* reexport safe */ _ResizableTextArea__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _TextArea__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TextArea */ \"(ssr)/./node_modules/rc-textarea/es/TextArea.js\");\n/* harmony import */ var _ResizableTextArea__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ResizableTextArea */ \"(ssr)/./node_modules/rc-textarea/es/ResizableTextArea.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_TextArea__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmMtdGV4dGFyZWEvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFrQztBQUNpQztBQUNuRSxpRUFBZUEsaURBQVFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kcnV0b28vLi9ub2RlX21vZHVsZXMvcmMtdGV4dGFyZWEvZXMvaW5kZXguanM/ZGMyNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgVGV4dEFyZWEgZnJvbSBcIi4vVGV4dEFyZWFcIjtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUmVzaXphYmxlVGV4dEFyZWEgfSBmcm9tIFwiLi9SZXNpemFibGVUZXh0QXJlYVwiO1xuZXhwb3J0IGRlZmF1bHQgVGV4dEFyZWE7Il0sIm5hbWVzIjpbIlRleHRBcmVhIiwiZGVmYXVsdCIsIlJlc2l6YWJsZVRleHRBcmVhIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rc-textarea/es/index.js\n");

/***/ })

};
;