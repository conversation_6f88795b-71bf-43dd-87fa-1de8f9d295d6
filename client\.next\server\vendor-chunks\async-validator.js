"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/async-validator";
exports.ids = ["vendor-chunks/async-validator"];
exports.modules = {

/***/ "(ssr)/./node_modules/async-validator/dist-web/index.js":
/*!********************************************************!*\
  !*** ./node_modules/async-validator/dist-web/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Schema)\n/* harmony export */ });\nfunction _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction _inheritsLoose(subClass, superClass) {\n    subClass.prototype = Object.create(superClass.prototype);\n    subClass.prototype.constructor = subClass;\n    _setPrototypeOf(subClass, superClass);\n}\nfunction _getPrototypeOf(o) {\n    _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n        return o.__proto__ || Object.getPrototypeOf(o);\n    };\n    return _getPrototypeOf(o);\n}\nfunction _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n        o.__proto__ = p;\n        return o;\n    };\n    return _setPrototypeOf(o, p);\n}\nfunction _isNativeReflectConstruct() {\n    if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n    if (Reflect.construct.sham) return false;\n    if (typeof Proxy === \"function\") return true;\n    try {\n        Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));\n        return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction _construct(Parent, args, Class) {\n    if (_isNativeReflectConstruct()) {\n        _construct = Reflect.construct.bind();\n    } else {\n        _construct = function _construct(Parent, args, Class) {\n            var a = [\n                null\n            ];\n            a.push.apply(a, args);\n            var Constructor = Function.bind.apply(Parent, a);\n            var instance = new Constructor();\n            if (Class) _setPrototypeOf(instance, Class.prototype);\n            return instance;\n        };\n    }\n    return _construct.apply(null, arguments);\n}\nfunction _isNativeFunction(fn) {\n    return Function.toString.call(fn).indexOf(\"[native code]\") !== -1;\n}\nfunction _wrapNativeSuper(Class) {\n    var _cache = typeof Map === \"function\" ? new Map() : undefined;\n    _wrapNativeSuper = function _wrapNativeSuper(Class) {\n        if (Class === null || !_isNativeFunction(Class)) return Class;\n        if (typeof Class !== \"function\") {\n            throw new TypeError(\"Super expression must either be null or a function\");\n        }\n        if (typeof _cache !== \"undefined\") {\n            if (_cache.has(Class)) return _cache.get(Class);\n            _cache.set(Class, Wrapper);\n        }\n        function Wrapper() {\n            return _construct(Class, arguments, _getPrototypeOf(this).constructor);\n        }\n        Wrapper.prototype = Object.create(Class.prototype, {\n            constructor: {\n                value: Wrapper,\n                enumerable: false,\n                writable: true,\n                configurable: true\n            }\n        });\n        return _setPrototypeOf(Wrapper, Class);\n    };\n    return _wrapNativeSuper(Class);\n}\n/* eslint no-console:0 */ var formatRegExp = /%[sdj%]/g;\nvar warning = function warning() {}; // don't print warning message when in production env or node runtime\nif (typeof process !== \"undefined\" && process.env && \"development\" !== \"production\" && \"undefined\" !== \"undefined\" && 0) {}\nfunction convertFieldsError(errors) {\n    if (!errors || !errors.length) return null;\n    var fields = {};\n    errors.forEach(function(error) {\n        var field = error.field;\n        fields[field] = fields[field] || [];\n        fields[field].push(error);\n    });\n    return fields;\n}\nfunction format(template) {\n    for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        args[_key - 1] = arguments[_key];\n    }\n    var i = 0;\n    var len = args.length;\n    if (typeof template === \"function\") {\n        return template.apply(null, args);\n    }\n    if (typeof template === \"string\") {\n        var str = template.replace(formatRegExp, function(x) {\n            if (x === \"%%\") {\n                return \"%\";\n            }\n            if (i >= len) {\n                return x;\n            }\n            switch(x){\n                case \"%s\":\n                    return String(args[i++]);\n                case \"%d\":\n                    return Number(args[i++]);\n                case \"%j\":\n                    try {\n                        return JSON.stringify(args[i++]);\n                    } catch (_) {\n                        return \"[Circular]\";\n                    }\n                    break;\n                default:\n                    return x;\n            }\n        });\n        return str;\n    }\n    return template;\n}\nfunction isNativeStringType(type) {\n    return type === \"string\" || type === \"url\" || type === \"hex\" || type === \"email\" || type === \"date\" || type === \"pattern\";\n}\nfunction isEmptyValue(value, type) {\n    if (value === undefined || value === null) {\n        return true;\n    }\n    if (type === \"array\" && Array.isArray(value) && !value.length) {\n        return true;\n    }\n    if (isNativeStringType(type) && typeof value === \"string\" && !value) {\n        return true;\n    }\n    return false;\n}\nfunction asyncParallelArray(arr, func, callback) {\n    var results = [];\n    var total = 0;\n    var arrLength = arr.length;\n    function count(errors) {\n        results.push.apply(results, errors || []);\n        total++;\n        if (total === arrLength) {\n            callback(results);\n        }\n    }\n    arr.forEach(function(a) {\n        func(a, count);\n    });\n}\nfunction asyncSerialArray(arr, func, callback) {\n    var index = 0;\n    var arrLength = arr.length;\n    function next(errors) {\n        if (errors && errors.length) {\n            callback(errors);\n            return;\n        }\n        var original = index;\n        index = index + 1;\n        if (original < arrLength) {\n            func(arr[original], next);\n        } else {\n            callback([]);\n        }\n    }\n    next([]);\n}\nfunction flattenObjArr(objArr) {\n    var ret = [];\n    Object.keys(objArr).forEach(function(k) {\n        ret.push.apply(ret, objArr[k] || []);\n    });\n    return ret;\n}\nvar AsyncValidationError = /*#__PURE__*/ function(_Error) {\n    _inheritsLoose(AsyncValidationError, _Error);\n    function AsyncValidationError(errors, fields) {\n        var _this;\n        _this = _Error.call(this, \"Async Validation Error\") || this;\n        _this.errors = errors;\n        _this.fields = fields;\n        return _this;\n    }\n    return AsyncValidationError;\n}(/*#__PURE__*/ _wrapNativeSuper(Error));\nfunction asyncMap(objArr, option, func, callback, source) {\n    if (option.first) {\n        var _pending = new Promise(function(resolve, reject) {\n            var next = function next(errors) {\n                callback(errors);\n                return errors.length ? reject(new AsyncValidationError(errors, convertFieldsError(errors))) : resolve(source);\n            };\n            var flattenArr = flattenObjArr(objArr);\n            asyncSerialArray(flattenArr, func, next);\n        });\n        _pending[\"catch\"](function(e) {\n            return e;\n        });\n        return _pending;\n    }\n    var firstFields = option.firstFields === true ? Object.keys(objArr) : option.firstFields || [];\n    var objArrKeys = Object.keys(objArr);\n    var objArrLength = objArrKeys.length;\n    var total = 0;\n    var results = [];\n    var pending = new Promise(function(resolve, reject) {\n        var next = function next(errors) {\n            results.push.apply(results, errors);\n            total++;\n            if (total === objArrLength) {\n                callback(results);\n                return results.length ? reject(new AsyncValidationError(results, convertFieldsError(results))) : resolve(source);\n            }\n        };\n        if (!objArrKeys.length) {\n            callback(results);\n            resolve(source);\n        }\n        objArrKeys.forEach(function(key) {\n            var arr = objArr[key];\n            if (firstFields.indexOf(key) !== -1) {\n                asyncSerialArray(arr, func, next);\n            } else {\n                asyncParallelArray(arr, func, next);\n            }\n        });\n    });\n    pending[\"catch\"](function(e) {\n        return e;\n    });\n    return pending;\n}\nfunction isErrorObj(obj) {\n    return !!(obj && obj.message !== undefined);\n}\nfunction getValue(value, path) {\n    var v = value;\n    for(var i = 0; i < path.length; i++){\n        if (v == undefined) {\n            return v;\n        }\n        v = v[path[i]];\n    }\n    return v;\n}\nfunction complementError(rule, source) {\n    return function(oe) {\n        var fieldValue;\n        if (rule.fullFields) {\n            fieldValue = getValue(source, rule.fullFields);\n        } else {\n            fieldValue = source[oe.field || rule.fullField];\n        }\n        if (isErrorObj(oe)) {\n            oe.field = oe.field || rule.fullField;\n            oe.fieldValue = fieldValue;\n            return oe;\n        }\n        return {\n            message: typeof oe === \"function\" ? oe() : oe,\n            fieldValue: fieldValue,\n            field: oe.field || rule.fullField\n        };\n    };\n}\nfunction deepMerge(target, source) {\n    if (source) {\n        for(var s in source){\n            if (source.hasOwnProperty(s)) {\n                var value = source[s];\n                if (typeof value === \"object\" && typeof target[s] === \"object\") {\n                    target[s] = _extends({}, target[s], value);\n                } else {\n                    target[s] = value;\n                }\n            }\n        }\n    }\n    return target;\n}\nvar required$1 = function required(rule, value, source, errors, options, type) {\n    if (rule.required && (!source.hasOwnProperty(rule.field) || isEmptyValue(value, type || rule.type))) {\n        errors.push(format(options.messages.required, rule.fullField));\n    }\n};\n/**\n *  Rule for validating whitespace.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */ var whitespace = function whitespace(rule, value, source, errors, options) {\n    if (/^\\s+$/.test(value) || value === \"\") {\n        errors.push(format(options.messages.whitespace, rule.fullField));\n    }\n};\n// https://github.com/kevva/url-regex/blob/master/index.js\nvar urlReg;\nvar getUrlRegex = function() {\n    if (urlReg) {\n        return urlReg;\n    }\n    var word = \"[a-fA-F\\\\d:]\";\n    var b = function b(options) {\n        return options && options.includeBoundaries ? \"(?:(?<=\\\\s|^)(?=\" + word + \")|(?<=\" + word + \")(?=\\\\s|$))\" : \"\";\n    };\n    var v4 = \"(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}\";\n    var v6seg = \"[a-fA-F\\\\d]{1,4}\";\n    var v6 = (\"\\n(?:\\n(?:\" + v6seg + \":){7}(?:\" + v6seg + \"|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\\n(?:\" + v6seg + \":){6}(?:\" + v4 + \"|:\" + v6seg + \"|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::*******\\n(?:\" + v6seg + \":){5}(?::\" + v4 + \"|(?::\" + v6seg + \"){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:*******\\n(?:\" + v6seg + \":){4}(?:(?::\" + v6seg + \"){0,1}:\" + v4 + \"|(?::\" + v6seg + \"){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:*******\\n(?:\" + v6seg + \":){3}(?:(?::\" + v6seg + \"){0,2}:\" + v4 + \"|(?::\" + v6seg + \"){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:*******\\n(?:\" + v6seg + \":){2}(?:(?::\" + v6seg + \"){0,3}:\" + v4 + \"|(?::\" + v6seg + \"){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:*******\\n(?:\" + v6seg + \":){1}(?:(?::\" + v6seg + \"){0,4}:\" + v4 + \"|(?::\" + v6seg + \"){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:*******\\n(?::(?:(?::\" + v6seg + \"){0,5}:\" + v4 + \"|(?::\" + v6seg + \"){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::*******\\n)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1\\n\").replace(/\\s*\\/\\/.*$/gm, \"\").replace(/\\n/g, \"\").trim(); // Pre-compile only the exact regexes because adding a global flag make regexes stateful\n    var v46Exact = new RegExp(\"(?:^\" + v4 + \"$)|(?:^\" + v6 + \"$)\");\n    var v4exact = new RegExp(\"^\" + v4 + \"$\");\n    var v6exact = new RegExp(\"^\" + v6 + \"$\");\n    var ip = function ip(options) {\n        return options && options.exact ? v46Exact : new RegExp(\"(?:\" + b(options) + v4 + b(options) + \")|(?:\" + b(options) + v6 + b(options) + \")\", \"g\");\n    };\n    ip.v4 = function(options) {\n        return options && options.exact ? v4exact : new RegExp(\"\" + b(options) + v4 + b(options), \"g\");\n    };\n    ip.v6 = function(options) {\n        return options && options.exact ? v6exact : new RegExp(\"\" + b(options) + v6 + b(options), \"g\");\n    };\n    var protocol = \"(?:(?:[a-z]+:)?//)\";\n    var auth = \"(?:\\\\S+(?::\\\\S*)?@)?\";\n    var ipv4 = ip.v4().source;\n    var ipv6 = ip.v6().source;\n    var host = \"(?:(?:[a-z\\\\u00a1-\\\\uffff0-9][-_]*)*[a-z\\\\u00a1-\\\\uffff0-9]+)\";\n    var domain = \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*\";\n    var tld = \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,}))\";\n    var port = \"(?::\\\\d{2,5})?\";\n    var path = '(?:[/?#][^\\\\s\"]*)?';\n    var regex = \"(?:\" + protocol + \"|www\\\\.)\" + auth + \"(?:localhost|\" + ipv4 + \"|\" + ipv6 + \"|\" + host + domain + tld + \")\" + port + path;\n    urlReg = new RegExp(\"(?:^\" + regex + \"$)\", \"i\");\n    return urlReg;\n};\n/* eslint max-len:0 */ var pattern$2 = {\n    // http://emailregex.com/\n    email: /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]+\\.)+[a-zA-Z\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]{2,}))$/,\n    // url: new RegExp(\n    //   '^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\\\S+(?::\\\\S*)?@)?(?:(?:(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[0-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,})))|localhost)(?::\\\\d{2,5})?(?:(/|\\\\?|#)[^\\\\s]*)?$',\n    //   'i',\n    // ),\n    hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i\n};\nvar types = {\n    integer: function integer(value) {\n        return types.number(value) && parseInt(value, 10) === value;\n    },\n    \"float\": function float(value) {\n        return types.number(value) && !types.integer(value);\n    },\n    array: function array(value) {\n        return Array.isArray(value);\n    },\n    regexp: function regexp(value) {\n        if (value instanceof RegExp) {\n            return true;\n        }\n        try {\n            return !!new RegExp(value);\n        } catch (e) {\n            return false;\n        }\n    },\n    date: function date(value) {\n        return typeof value.getTime === \"function\" && typeof value.getMonth === \"function\" && typeof value.getYear === \"function\" && !isNaN(value.getTime());\n    },\n    number: function number(value) {\n        if (isNaN(value)) {\n            return false;\n        }\n        return typeof value === \"number\";\n    },\n    object: function object(value) {\n        return typeof value === \"object\" && !types.array(value);\n    },\n    method: function method(value) {\n        return typeof value === \"function\";\n    },\n    email: function email(value) {\n        return typeof value === \"string\" && value.length <= 320 && !!value.match(pattern$2.email);\n    },\n    url: function url(value) {\n        return typeof value === \"string\" && value.length <= 2048 && !!value.match(getUrlRegex());\n    },\n    hex: function hex(value) {\n        return typeof value === \"string\" && !!value.match(pattern$2.hex);\n    }\n};\nvar type$1 = function type(rule, value, source, errors, options) {\n    if (rule.required && value === undefined) {\n        required$1(rule, value, source, errors, options);\n        return;\n    }\n    var custom = [\n        \"integer\",\n        \"float\",\n        \"array\",\n        \"regexp\",\n        \"object\",\n        \"method\",\n        \"email\",\n        \"number\",\n        \"date\",\n        \"url\",\n        \"hex\"\n    ];\n    var ruleType = rule.type;\n    if (custom.indexOf(ruleType) > -1) {\n        if (!types[ruleType](value)) {\n            errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type));\n        } // straight typeof check\n    } else if (ruleType && typeof value !== rule.type) {\n        errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type));\n    }\n};\nvar range = function range(rule, value, source, errors, options) {\n    var len = typeof rule.len === \"number\";\n    var min = typeof rule.min === \"number\";\n    var max = typeof rule.max === \"number\"; // 正则匹配码点范围从U+010000一直到U+10FFFF的文字（补充平面Supplementary Plane）\n    var spRegexp = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n    var val = value;\n    var key = null;\n    var num = typeof value === \"number\";\n    var str = typeof value === \"string\";\n    var arr = Array.isArray(value);\n    if (num) {\n        key = \"number\";\n    } else if (str) {\n        key = \"string\";\n    } else if (arr) {\n        key = \"array\";\n    } // if the value is not of a supported type for range validation\n    // the validation rule rule should use the\n    // type property to also test for a particular type\n    if (!key) {\n        return false;\n    }\n    if (arr) {\n        val = value.length;\n    }\n    if (str) {\n        // 处理码点大于U+010000的文字length属性不准确的bug，如\"𠮷𠮷𠮷\".lenght !== 3\n        val = value.replace(spRegexp, \"_\").length;\n    }\n    if (len) {\n        if (val !== rule.len) {\n            errors.push(format(options.messages[key].len, rule.fullField, rule.len));\n        }\n    } else if (min && !max && val < rule.min) {\n        errors.push(format(options.messages[key].min, rule.fullField, rule.min));\n    } else if (max && !min && val > rule.max) {\n        errors.push(format(options.messages[key].max, rule.fullField, rule.max));\n    } else if (min && max && (val < rule.min || val > rule.max)) {\n        errors.push(format(options.messages[key].range, rule.fullField, rule.min, rule.max));\n    }\n};\nvar ENUM$1 = \"enum\";\nvar enumerable$1 = function enumerable(rule, value, source, errors, options) {\n    rule[ENUM$1] = Array.isArray(rule[ENUM$1]) ? rule[ENUM$1] : [];\n    if (rule[ENUM$1].indexOf(value) === -1) {\n        errors.push(format(options.messages[ENUM$1], rule.fullField, rule[ENUM$1].join(\", \")));\n    }\n};\nvar pattern$1 = function pattern(rule, value, source, errors, options) {\n    if (rule.pattern) {\n        if (rule.pattern instanceof RegExp) {\n            // if a RegExp instance is passed, reset `lastIndex` in case its `global`\n            // flag is accidentally set to `true`, which in a validation scenario\n            // is not necessary and the result might be misleading\n            rule.pattern.lastIndex = 0;\n            if (!rule.pattern.test(value)) {\n                errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n            }\n        } else if (typeof rule.pattern === \"string\") {\n            var _pattern = new RegExp(rule.pattern);\n            if (!_pattern.test(value)) {\n                errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n            }\n        }\n    }\n};\nvar rules = {\n    required: required$1,\n    whitespace: whitespace,\n    type: type$1,\n    range: range,\n    \"enum\": enumerable$1,\n    pattern: pattern$1\n};\nvar string = function string(rule, value, callback, source, options) {\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n    if (validate) {\n        if (isEmptyValue(value, \"string\") && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options, \"string\");\n        if (!isEmptyValue(value, \"string\")) {\n            rules.type(rule, value, source, errors, options);\n            rules.range(rule, value, source, errors, options);\n            rules.pattern(rule, value, source, errors, options);\n            if (rule.whitespace === true) {\n                rules.whitespace(rule, value, source, errors, options);\n            }\n        }\n    }\n    callback(errors);\n};\nvar method = function method(rule, value, callback, source, options) {\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n    if (validate) {\n        if (isEmptyValue(value) && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options);\n        if (value !== undefined) {\n            rules.type(rule, value, source, errors, options);\n        }\n    }\n    callback(errors);\n};\nvar number = function number(rule, value, callback, source, options) {\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n    if (validate) {\n        if (value === \"\") {\n            value = undefined;\n        }\n        if (isEmptyValue(value) && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options);\n        if (value !== undefined) {\n            rules.type(rule, value, source, errors, options);\n            rules.range(rule, value, source, errors, options);\n        }\n    }\n    callback(errors);\n};\nvar _boolean = function _boolean(rule, value, callback, source, options) {\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n    if (validate) {\n        if (isEmptyValue(value) && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options);\n        if (value !== undefined) {\n            rules.type(rule, value, source, errors, options);\n        }\n    }\n    callback(errors);\n};\nvar regexp = function regexp(rule, value, callback, source, options) {\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n    if (validate) {\n        if (isEmptyValue(value) && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options);\n        if (!isEmptyValue(value)) {\n            rules.type(rule, value, source, errors, options);\n        }\n    }\n    callback(errors);\n};\nvar integer = function integer(rule, value, callback, source, options) {\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n    if (validate) {\n        if (isEmptyValue(value) && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options);\n        if (value !== undefined) {\n            rules.type(rule, value, source, errors, options);\n            rules.range(rule, value, source, errors, options);\n        }\n    }\n    callback(errors);\n};\nvar floatFn = function floatFn(rule, value, callback, source, options) {\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n    if (validate) {\n        if (isEmptyValue(value) && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options);\n        if (value !== undefined) {\n            rules.type(rule, value, source, errors, options);\n            rules.range(rule, value, source, errors, options);\n        }\n    }\n    callback(errors);\n};\nvar array = function array(rule, value, callback, source, options) {\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n    if (validate) {\n        if ((value === undefined || value === null) && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options, \"array\");\n        if (value !== undefined && value !== null) {\n            rules.type(rule, value, source, errors, options);\n            rules.range(rule, value, source, errors, options);\n        }\n    }\n    callback(errors);\n};\nvar object = function object(rule, value, callback, source, options) {\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n    if (validate) {\n        if (isEmptyValue(value) && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options);\n        if (value !== undefined) {\n            rules.type(rule, value, source, errors, options);\n        }\n    }\n    callback(errors);\n};\nvar ENUM = \"enum\";\nvar enumerable = function enumerable(rule, value, callback, source, options) {\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n    if (validate) {\n        if (isEmptyValue(value) && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options);\n        if (value !== undefined) {\n            rules[ENUM](rule, value, source, errors, options);\n        }\n    }\n    callback(errors);\n};\nvar pattern = function pattern(rule, value, callback, source, options) {\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n    if (validate) {\n        if (isEmptyValue(value, \"string\") && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options);\n        if (!isEmptyValue(value, \"string\")) {\n            rules.pattern(rule, value, source, errors, options);\n        }\n    }\n    callback(errors);\n};\nvar date = function date(rule, value, callback, source, options) {\n    // console.log('integer rule called %j', rule);\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field); // console.log('validate on %s value', value);\n    if (validate) {\n        if (isEmptyValue(value, \"date\") && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options);\n        if (!isEmptyValue(value, \"date\")) {\n            var dateObject;\n            if (value instanceof Date) {\n                dateObject = value;\n            } else {\n                dateObject = new Date(value);\n            }\n            rules.type(rule, dateObject, source, errors, options);\n            if (dateObject) {\n                rules.range(rule, dateObject.getTime(), source, errors, options);\n            }\n        }\n    }\n    callback(errors);\n};\nvar required = function required(rule, value, callback, source, options) {\n    var errors = [];\n    var type = Array.isArray(value) ? \"array\" : typeof value;\n    rules.required(rule, value, source, errors, options, type);\n    callback(errors);\n};\nvar type = function type(rule, value, callback, source, options) {\n    var ruleType = rule.type;\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n    if (validate) {\n        if (isEmptyValue(value, ruleType) && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options, ruleType);\n        if (!isEmptyValue(value, ruleType)) {\n            rules.type(rule, value, source, errors, options);\n        }\n    }\n    callback(errors);\n};\nvar any = function any(rule, value, callback, source, options) {\n    var errors = [];\n    var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n    if (validate) {\n        if (isEmptyValue(value) && !rule.required) {\n            return callback();\n        }\n        rules.required(rule, value, source, errors, options);\n    }\n    callback(errors);\n};\nvar validators = {\n    string: string,\n    method: method,\n    number: number,\n    \"boolean\": _boolean,\n    regexp: regexp,\n    integer: integer,\n    \"float\": floatFn,\n    array: array,\n    object: object,\n    \"enum\": enumerable,\n    pattern: pattern,\n    date: date,\n    url: type,\n    hex: type,\n    email: type,\n    required: required,\n    any: any\n};\nfunction newMessages() {\n    return {\n        \"default\": \"Validation error on field %s\",\n        required: \"%s is required\",\n        \"enum\": \"%s must be one of %s\",\n        whitespace: \"%s cannot be empty\",\n        date: {\n            format: \"%s date %s is invalid for format %s\",\n            parse: \"%s date could not be parsed, %s is invalid \",\n            invalid: \"%s date %s is invalid\"\n        },\n        types: {\n            string: \"%s is not a %s\",\n            method: \"%s is not a %s (function)\",\n            array: \"%s is not an %s\",\n            object: \"%s is not an %s\",\n            number: \"%s is not a %s\",\n            date: \"%s is not a %s\",\n            \"boolean\": \"%s is not a %s\",\n            integer: \"%s is not an %s\",\n            \"float\": \"%s is not a %s\",\n            regexp: \"%s is not a valid %s\",\n            email: \"%s is not a valid %s\",\n            url: \"%s is not a valid %s\",\n            hex: \"%s is not a valid %s\"\n        },\n        string: {\n            len: \"%s must be exactly %s characters\",\n            min: \"%s must be at least %s characters\",\n            max: \"%s cannot be longer than %s characters\",\n            range: \"%s must be between %s and %s characters\"\n        },\n        number: {\n            len: \"%s must equal %s\",\n            min: \"%s cannot be less than %s\",\n            max: \"%s cannot be greater than %s\",\n            range: \"%s must be between %s and %s\"\n        },\n        array: {\n            len: \"%s must be exactly %s in length\",\n            min: \"%s cannot be less than %s in length\",\n            max: \"%s cannot be greater than %s in length\",\n            range: \"%s must be between %s and %s in length\"\n        },\n        pattern: {\n            mismatch: \"%s value %s does not match pattern %s\"\n        },\n        clone: function clone() {\n            var cloned = JSON.parse(JSON.stringify(this));\n            cloned.clone = this.clone;\n            return cloned;\n        }\n    };\n}\nvar messages = newMessages();\n/**\n *  Encapsulates a validation schema.\n *\n *  @param descriptor An object declaring validation rules\n *  for this schema.\n */ var Schema = /*#__PURE__*/ function() {\n    // ========================= Static =========================\n    // ======================== Instance ========================\n    function Schema(descriptor) {\n        this.rules = null;\n        this._messages = messages;\n        this.define(descriptor);\n    }\n    var _proto = Schema.prototype;\n    _proto.define = function define(rules) {\n        var _this = this;\n        if (!rules) {\n            throw new Error(\"Cannot configure a schema with no rules\");\n        }\n        if (typeof rules !== \"object\" || Array.isArray(rules)) {\n            throw new Error(\"Rules must be an object\");\n        }\n        this.rules = {};\n        Object.keys(rules).forEach(function(name) {\n            var item = rules[name];\n            _this.rules[name] = Array.isArray(item) ? item : [\n                item\n            ];\n        });\n    };\n    _proto.messages = function messages(_messages) {\n        if (_messages) {\n            this._messages = deepMerge(newMessages(), _messages);\n        }\n        return this._messages;\n    };\n    _proto.validate = function validate(source_, o, oc) {\n        var _this2 = this;\n        if (o === void 0) {\n            o = {};\n        }\n        if (oc === void 0) {\n            oc = function oc() {};\n        }\n        var source = source_;\n        var options = o;\n        var callback = oc;\n        if (typeof options === \"function\") {\n            callback = options;\n            options = {};\n        }\n        if (!this.rules || Object.keys(this.rules).length === 0) {\n            if (callback) {\n                callback(null, source);\n            }\n            return Promise.resolve(source);\n        }\n        function complete(results) {\n            var errors = [];\n            var fields = {};\n            function add(e) {\n                if (Array.isArray(e)) {\n                    var _errors;\n                    errors = (_errors = errors).concat.apply(_errors, e);\n                } else {\n                    errors.push(e);\n                }\n            }\n            for(var i = 0; i < results.length; i++){\n                add(results[i]);\n            }\n            if (!errors.length) {\n                callback(null, source);\n            } else {\n                fields = convertFieldsError(errors);\n                callback(errors, fields);\n            }\n        }\n        if (options.messages) {\n            var messages$1 = this.messages();\n            if (messages$1 === messages) {\n                messages$1 = newMessages();\n            }\n            deepMerge(messages$1, options.messages);\n            options.messages = messages$1;\n        } else {\n            options.messages = this.messages();\n        }\n        var series = {};\n        var keys = options.keys || Object.keys(this.rules);\n        keys.forEach(function(z) {\n            var arr = _this2.rules[z];\n            var value = source[z];\n            arr.forEach(function(r) {\n                var rule = r;\n                if (typeof rule.transform === \"function\") {\n                    if (source === source_) {\n                        source = _extends({}, source);\n                    }\n                    value = source[z] = rule.transform(value);\n                }\n                if (typeof rule === \"function\") {\n                    rule = {\n                        validator: rule\n                    };\n                } else {\n                    rule = _extends({}, rule);\n                } // Fill validator. Skip if nothing need to validate\n                rule.validator = _this2.getValidationMethod(rule);\n                if (!rule.validator) {\n                    return;\n                }\n                rule.field = z;\n                rule.fullField = rule.fullField || z;\n                rule.type = _this2.getType(rule);\n                series[z] = series[z] || [];\n                series[z].push({\n                    rule: rule,\n                    value: value,\n                    source: source,\n                    field: z\n                });\n            });\n        });\n        var errorFields = {};\n        return asyncMap(series, options, function(data, doIt) {\n            var rule = data.rule;\n            var deep = (rule.type === \"object\" || rule.type === \"array\") && (typeof rule.fields === \"object\" || typeof rule.defaultField === \"object\");\n            deep = deep && (rule.required || !rule.required && data.value);\n            rule.field = data.field;\n            function addFullField(key, schema) {\n                return _extends({}, schema, {\n                    fullField: rule.fullField + \".\" + key,\n                    fullFields: rule.fullFields ? [].concat(rule.fullFields, [\n                        key\n                    ]) : [\n                        key\n                    ]\n                });\n            }\n            function cb(e) {\n                if (e === void 0) {\n                    e = [];\n                }\n                var errorList = Array.isArray(e) ? e : [\n                    e\n                ];\n                if (!options.suppressWarning && errorList.length) {\n                    Schema.warning(\"async-validator:\", errorList);\n                }\n                if (errorList.length && rule.message !== undefined) {\n                    errorList = [].concat(rule.message);\n                } // Fill error info\n                var filledErrors = errorList.map(complementError(rule, source));\n                if (options.first && filledErrors.length) {\n                    errorFields[rule.field] = 1;\n                    return doIt(filledErrors);\n                }\n                if (!deep) {\n                    doIt(filledErrors);\n                } else {\n                    // if rule is required but the target object\n                    // does not exist fail at the rule level and don't\n                    // go deeper\n                    if (rule.required && !data.value) {\n                        if (rule.message !== undefined) {\n                            filledErrors = [].concat(rule.message).map(complementError(rule, source));\n                        } else if (options.error) {\n                            filledErrors = [\n                                options.error(rule, format(options.messages.required, rule.field))\n                            ];\n                        }\n                        return doIt(filledErrors);\n                    }\n                    var fieldsSchema = {};\n                    if (rule.defaultField) {\n                        Object.keys(data.value).map(function(key) {\n                            fieldsSchema[key] = rule.defaultField;\n                        });\n                    }\n                    fieldsSchema = _extends({}, fieldsSchema, data.rule.fields);\n                    var paredFieldsSchema = {};\n                    Object.keys(fieldsSchema).forEach(function(field) {\n                        var fieldSchema = fieldsSchema[field];\n                        var fieldSchemaList = Array.isArray(fieldSchema) ? fieldSchema : [\n                            fieldSchema\n                        ];\n                        paredFieldsSchema[field] = fieldSchemaList.map(addFullField.bind(null, field));\n                    });\n                    var schema = new Schema(paredFieldsSchema);\n                    schema.messages(options.messages);\n                    if (data.rule.options) {\n                        data.rule.options.messages = options.messages;\n                        data.rule.options.error = options.error;\n                    }\n                    schema.validate(data.value, data.rule.options || options, function(errs) {\n                        var finalErrors = [];\n                        if (filledErrors && filledErrors.length) {\n                            finalErrors.push.apply(finalErrors, filledErrors);\n                        }\n                        if (errs && errs.length) {\n                            finalErrors.push.apply(finalErrors, errs);\n                        }\n                        doIt(finalErrors.length ? finalErrors : null);\n                    });\n                }\n            }\n            var res;\n            if (rule.asyncValidator) {\n                res = rule.asyncValidator(rule, data.value, cb, data.source, options);\n            } else if (rule.validator) {\n                try {\n                    res = rule.validator(rule, data.value, cb, data.source, options);\n                } catch (error) {\n                    console.error == null ? void 0 : console.error(error); // rethrow to report error\n                    if (!options.suppressValidatorError) {\n                        setTimeout(function() {\n                            throw error;\n                        }, 0);\n                    }\n                    cb(error.message);\n                }\n                if (res === true) {\n                    cb();\n                } else if (res === false) {\n                    cb(typeof rule.message === \"function\" ? rule.message(rule.fullField || rule.field) : rule.message || (rule.fullField || rule.field) + \" fails\");\n                } else if (res instanceof Array) {\n                    cb(res);\n                } else if (res instanceof Error) {\n                    cb(res.message);\n                }\n            }\n            if (res && res.then) {\n                res.then(function() {\n                    return cb();\n                }, function(e) {\n                    return cb(e);\n                });\n            }\n        }, function(results) {\n            complete(results);\n        }, source);\n    };\n    _proto.getType = function getType(rule) {\n        if (rule.type === undefined && rule.pattern instanceof RegExp) {\n            rule.type = \"pattern\";\n        }\n        if (typeof rule.validator !== \"function\" && rule.type && !validators.hasOwnProperty(rule.type)) {\n            throw new Error(format(\"Unknown rule type %s\", rule.type));\n        }\n        return rule.type || \"string\";\n    };\n    _proto.getValidationMethod = function getValidationMethod(rule) {\n        if (typeof rule.validator === \"function\") {\n            return rule.validator;\n        }\n        var keys = Object.keys(rule);\n        var messageIndex = keys.indexOf(\"message\");\n        if (messageIndex !== -1) {\n            keys.splice(messageIndex, 1);\n        }\n        if (keys.length === 1 && keys[0] === \"required\") {\n            return validators.required;\n        }\n        return validators[this.getType(rule)] || undefined;\n    };\n    return Schema;\n}();\nSchema.register = function register(type, validator) {\n    if (typeof validator !== \"function\") {\n        throw new Error(\"Cannot register a validator by type, validator is not a function\");\n    }\n    validators[type] = validator;\n};\nSchema.warning = warning;\nSchema.messages = messages;\nSchema.validators = validators;\n //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/async-validator/dist-web/index.js\n");

/***/ })

};
;