import { Inter } from "next/font/google";
import "./globals.css";
import Providers from "@/lib/Providers";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "Dr<PERSON><PERSON>",
  description: "Trusted Online Banking",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <Providers>
      <html lang="en" className="dark">
        <body className={inter.className}>{children}</body>
      </html>
    </Providers>
  );
}
